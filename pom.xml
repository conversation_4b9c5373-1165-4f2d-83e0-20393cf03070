<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.finhub</groupId>
        <artifactId>finhub-root</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fenbei.fx.card</groupId>
    <artifactId>fenbei-fx-card</artifactId>
    <version>1.0.3-SNAPSHOT</version>

    <packaging>pom</packaging>

    <properties>
        <fenbei-fx-card.version>5.3.9.033${current.version}</fenbei-fx-card.version>
        <finhub.version>4.1.88${current.version}</finhub.version>
        <usercenter-api>5.3.54${current.version}</usercenter-api>
        <dech-api-version>6.2.9.011${current.version}</dech-api-version>
        <fenbei-fx-pay.version>5.2.10.103${current.version}</fenbei-fx-pay.version>
        <expense-management-api.version>5.2.7.0705${current.version}</expense-management-api.version>
        <mls-i18n.version>1.0.2${current.version}</mls-i18n.version>
        <saas-plus.version>5.4.6.062${current.version}</saas-plus.version>
        <pay.api.version>5.3.4.081${current.version}</pay.api.version>
    </properties>

    <modules>
        <module>fenbei-fx-card-api</module>
        <module>fenbei-fx-card-client</module>
        <module>fenbei-fx-card-dao</module>
        <module>fenbei-fx-card-rpc</module>
        <module>fenbei-fx-card-server</module>
        <module>fenbei-fx-card-service</module>
        <module>fenbei-fx-card-web</module>
        <module>fenbei-fx-card-common</module>
    </modules>

    <dependencies>
        <!-- Module -->
        <!-- END -->
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- Module -->
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-api</artifactId>
                <version>${fenbei-fx-card.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-client</artifactId>
                <version>${fenbei-fx-card.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-dao</artifactId>
                <version>${fenbei-fx-card.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-rpc</artifactId>
                <version>${fenbei-fx-card.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-server</artifactId>
                <version>${fenbei-fx-card.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-service</artifactId>
                <version>${fenbei-fx-card.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-web</artifactId>
                <version>${fenbei-fx-card.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-common</artifactId>
                <version>${fenbei-fx-card.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>travel-rule-api</artifactId>
                <version>1.7.071${current.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-auth</artifactId>
                <version>${finhub.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish.web</groupId>
                        <artifactId>javax.el</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>error_prone_annotations</artifactId>
                        <groupId>com.google.errorprone</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>saas-api</artifactId>
                <version>5.2.12.121-SNAPSHOT</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-base</artifactId>
                <version>${finhub.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-common</artifactId>
                <version>${finhub.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-kafka</artifactId>
                <version>${finhub.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>eventbus_2.11</artifactId>
                <version>1.0.143-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-core</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mongodb</groupId>
                        <artifactId>bson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong.business.card</groupId>
                <artifactId>business-card-api</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>usercenter-api</artifactId>
                <version>${usercenter-api}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>alipay-sdk</artifactId>
                    </exclusion>
                    <!--移除无用依赖-->
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fenbeitong</groupId>
                        <artifactId>eventbus_2.11</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-api</artifactId>
                <version>${dech-api-version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong.fxpay</groupId>
                <artifactId>fenbei-fx-pay-api</artifactId>
                <version>${fenbei-fx-pay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-api</artifactId>
                <version>${pay.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish.web</groupId>
                        <artifactId>javax.el</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>error_prone_annotations</artifactId>
                        <groupId>com.google.errorprone</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>saas-plus-api</artifactId>
                <version>${saas-plus.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong.expense.management</groupId>
                <artifactId>expense-management-api</artifactId>
                <version>${expense-management-api.version}</version>
            </dependency>


            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>harmony-unicode-client</artifactId>
                <version>2.0.15${current.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbei.mls</groupId>
                <artifactId>mls-i18n</artifactId>
                <version>${mls-i18n.version}</version>
            </dependency>

            <!-- http工具 start -->
            <dependency>
                <groupId>com.jakewharton.retrofit</groupId>
                <artifactId>retrofit2-rxjava2-adapter</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jackson</artifactId>
                <version>2.4.0</version>
            </dependency>
            <!-- http工具 end -->

        </dependencies>
    </dependencyManagement>
</project>
