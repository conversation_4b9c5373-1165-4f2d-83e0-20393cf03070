package com.fenbei.fx.card.web.cardorder.controller;

import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.common.enums.WriteOffShowEnum;
import com.fenbei.fx.card.common.vo.KeyValueVO;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.*;
import com.fenbei.fx.card.service.usercard.dto.TotalPrice;
import com.fenbei.fx.card.util.DateFormatUtil;
import com.fenbei.fx.card.util.I18nUtils;
import com.fenbei.fx.card.util.MoneyNumberFormatUtil;
import com.fenbei.fx.card.web.cardorder.converter.CardOrderVOConverter;
import com.fenbei.fx.card.web.cardorder.vo.*;
import com.fenbeitong.finhub.common.constant.CheckStatusEnum;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 国际卡订单 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/cardorder" )
public class CardOrderController extends BaseController<CardOrderService, CardOrderVOConverter> {

    /**
     * 国际卡订单-列表
     *
     * @param cardOrderListReqVO
     * @return 列表结果
     * @undone
     */
    @RequestMapping(value = "list", method = {RequestMethod.GET})
    public ItemsR<CardOrderListResVO> list(CardOrderListReqVO cardOrderListReqVO) {
        CardOrderListReqDTO cardOrderListReqDTO = converter.convertToCardOrderListReqDTO(cardOrderListReqVO);

        List<CardOrderListResDTO> cardOrderListResDTOList = service.list(cardOrderListReqDTO);
        List<CardOrderListResVO> items = converter.convertToCardOrderListResVOList(cardOrderListResDTOList);

        return responseItemsR(ResponseCodeEnum.SUCCESS, items);
    }

    /**
     * 国际卡订单-First查询
     *
     * @param cardOrderListReqVO
     * @return
     */
    @RequestMapping(value = "listOne", method = {RequestMethod.GET})
    public ItemR<CardOrderListResVO> listOne(CardOrderListReqVO cardOrderListReqVO) {
        CardOrderListReqDTO cardOrderListReqDTO = converter.convertToCardOrderListReqDTO(cardOrderListReqVO);

        CardOrderListResDTO cardOrderListResDTO = service.listOne(cardOrderListReqDTO);
        CardOrderListResVO item = converter.convertToCardOrderListResVO(cardOrderListResDTO);

        return responseItemR(ResponseCodeEnum.SUCCESS, item);
    }

    /**
     * 【Web】海外卡管理-交易记录分页
     *
     * @param cardTradeInfoWebPageReqVO 查询条件
     * @param pageNo 当前页码 默认1
     * @param pageSize 每页大小 默认10
     * @return
     */
    @RequestMapping(value = "web/page", method = {RequestMethod.GET})
    public PageR<CardTradeInfoWebPageResVO> webPage(CardTradeInfoWebPageReqVO cardTradeInfoWebPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        CardTradeInfoWebPageReqDTO cardTradeInfoWebPageReqDTO = converter.convertToCardTradeInfoWebPageReqDTO(cardTradeInfoWebPageReqVO);

        Page<CardTradeInfoWebPageResDTO> cardOrderPageResDTOPage = service.pagination(cardTradeInfoWebPageReqDTO, current, size);
        Page<CardTradeInfoWebPageResVO> cardOrderPageResVOPage = converter.convertToCardTradeInfoWebPageResVOPage(cardOrderPageResDTOPage);
        convertForShow(cardOrderPageResVOPage.getRecords());
        return responsePageR(ResponseCodeEnum.SUCCESS, cardOrderPageResVOPage.getTotal(), cardOrderPageResVOPage.getRecords(), size, current);
    }

    /**
     * 页面按钮转换
     * @param resVOS 对象
     */
    public void convertForShow(List<CardTradeInfoWebPageResVO> resVOS){
        if (resVOS == null ) {
            return ;
        }
        for (CardTradeInfoWebPageResVO cardTradeInfoWebPageResVO : resVOS) {
            if (Objects.equals(TransactionTypeEnum.CONSUME.getKey(), cardTradeInfoWebPageResVO.getTransactionTypeCode()) && BigDecimalUtils.hasPrice(cardTradeInfoWebPageResVO.getUncheckConsume())){
                cardTradeInfoWebPageResVO.setTradeCheckedStatus(false);
            }else {
                cardTradeInfoWebPageResVO.setTradeCheckedStatus(true);
            }
        }
    }

    /**
     * 国际卡订单-新增
     *
     * @param cardOrderAddReqVO
     * @return
     */
    @RequestMapping(value = "add", method = {RequestMethod.POST})
    public MessageR add(CardOrderAddReqVO cardOrderAddReqVO) {
        CardOrderAddReqDTO cardOrderAddReqDTO = converter.convertToCardOrderAddReqDTO(cardOrderAddReqVO);

        Boolean isSuccess = service.add(cardOrderAddReqDTO);

        return responseMessageR(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 国际卡订单-新增(所有字段)
     * @param cardOrderAddReqVO
     * @return
     */
    @RequestMapping(value = "addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(CardOrderAddReqVO cardOrderAddReqVO) {
        CardOrderAddReqDTO cardOrderAddReqDTO = converter.convertToCardOrderAddReqDTO(cardOrderAddReqVO);

        Boolean isSuccess = service.addAllColumn(cardOrderAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 【App】全部交易记录
     * App→企业卡付→海外卡→全部交易记录（按不同规则筛选）
     *
     * @param cardTradeInfoAppPageReqVO 查询条件
     * @return
     */
    @RequestMapping(value = "app/page", method = {RequestMethod.GET})
    public PageR<CardTradeInfoAppPageResVO> appPage(CardTradeInfoAppPageReqVO cardTradeInfoAppPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        if (cardTradeInfoAppPageReqVO == null ) {
            return null;
        }
        CardTradeInfoAppPageReqDTO cardTradeInfoAppPageReqDTO = new CardTradeInfoAppPageReqDTO();
        cardTradeInfoAppPageReqDTO.setTransactionType(cardTradeInfoAppPageReqVO.getTransactionType());
        if (StringUtils.isNotBlank(cardTradeInfoAppPageReqVO.getTradeGeTime())) {
            cardTradeInfoAppPageReqDTO.setTradeGeTime(DateUtils.parseDate(cardTradeInfoAppPageReqVO.getTradeGeTime()));
        }
        if (StringUtils.isNotBlank(cardTradeInfoAppPageReqVO.getTradeLeTime())) {
            cardTradeInfoAppPageReqDTO.setTradeLeTime(DateUtils.parseDate(cardTradeInfoAppPageReqVO.getTradeLeTime()));
        }
        if (cardTradeInfoAppPageReqVO.getTradeGeAmount() != null) {
            cardTradeInfoAppPageReqDTO.setTradeGeAmount(BigDecimalUtils.yuan2fen(cardTradeInfoAppPageReqVO.getTradeGeAmount()));
        }
        if (cardTradeInfoAppPageReqVO.getTradeLeAmount() != null) {
            cardTradeInfoAppPageReqDTO.setTradeLeAmount(BigDecimalUtils.yuan2fen(cardTradeInfoAppPageReqVO.getTradeLeAmount()));
        }
        if (cardTradeInfoAppPageReqVO.getCheckStatus() != null) {
            cardTradeInfoAppPageReqDTO.setCheckStatus(cardTradeInfoAppPageReqVO.getCheckStatus());
        }

        Page<CardTradeInfoAppPageResDTO> cardTradeInfoAppPageResDTOPage = service.pagination(cardTradeInfoAppPageReqDTO, current, size);
        Page<CardTradeInfoAppPageResVO> cardOrderPageResVOPage = convertToCardTradeInfoAppPageResVOPage(cardTradeInfoAppPageResDTOPage);

        return responsePageR(ResponseCodeEnum.SUCCESS, cardOrderPageResVOPage.getTotal(), cardOrderPageResVOPage.getRecords(), size, current);
    }

    public Page<CardTradeInfoAppPageResVO> convertToCardTradeInfoAppPageResVOPage(Page<CardTradeInfoAppPageResDTO> cardTradeInfoAppPageResDTOPage){
        if (cardTradeInfoAppPageResDTOPage == null ) {
            return null;
        }
        Page<CardTradeInfoAppPageResVO> page = new Page<>();
        page.setRecords(convertToCardTradeInfoAppPageResVOList(cardTradeInfoAppPageResDTOPage.getRecords()));
        page.setTotal(cardTradeInfoAppPageResDTOPage.getTotal() );
        page.setSize(cardTradeInfoAppPageResDTOPage.getSize() );
        page.setCurrent(cardTradeInfoAppPageResDTOPage.getCurrent() );
        return page;
    }
    public List<CardTradeInfoAppPageResVO> convertToCardTradeInfoAppPageResVOList(List<CardTradeInfoAppPageResDTO> cardTradeInfoAppPageResVOS){
        if ( cardTradeInfoAppPageResVOS == null ) {
            return null;
        }
        List<CardTradeInfoAppPageResVO> list = new ArrayList<>(cardTradeInfoAppPageResVOS.size());
        for (CardTradeInfoAppPageResDTO cardTradeInfoAppPageResDTO : cardTradeInfoAppPageResVOS ) {
            list.add(convertToCardTradeInfoAppPageResDTO(cardTradeInfoAppPageResDTO));
        }
        return list;
    }

    public CardTradeInfoAppPageResVO convertToCardTradeInfoAppPageResDTO(CardTradeInfoAppPageResDTO cardTradeInfoAppPageResDTO){
        CardTradeInfoAppPageResVO cardTradeInfoAppPageResVO = new CardTradeInfoAppPageResVO();
        cardTradeInfoAppPageResVO.setId(cardTradeInfoAppPageResDTO.getId());
        if (cardTradeInfoAppPageResDTO.getTransactionTypeCode().equals(TransactionTypeEnum.REFUND.getKey())){
            cardTradeInfoAppPageResVO.setRootOrderId(cardTradeInfoAppPageResDTO.getOriBizNo());
        }else {
            cardTradeInfoAppPageResVO.setRootOrderId(cardTradeInfoAppPageResDTO.getTransactionId());
        }
        cardTradeInfoAppPageResVO.setFbOrderId(cardTradeInfoAppPageResDTO.getTransactionId());
        cardTradeInfoAppPageResVO.setOrderId(cardTradeInfoAppPageResDTO.getTransactionId());
        //预定和预定释放ID设置为不一致，避免被关联到
        if (Objects.equals(TransactionTypeEnum.PRE_AUTH.getKey(), cardTradeInfoAppPageResDTO.getTransactionTypeCode())
            ||Objects.equals(TransactionTypeEnum.PRE_AUTH_RELEASE.getKey(), cardTradeInfoAppPageResDTO.getTransactionTypeCode())){
            cardTradeInfoAppPageResVO.setRootOrderId(cardTradeInfoAppPageResDTO.getVirtualId());
            cardTradeInfoAppPageResVO.setFbOrderId(cardTradeInfoAppPageResDTO.getVirtualId());
        }

        cardTradeInfoAppPageResVO.setApplyAmount(cardTradeInfoAppPageResDTO.getTransactionAmount());
        cardTradeInfoAppPageResVO.setCreateTime(DateUtils.formatTime(cardTradeInfoAppPageResDTO.getTransactionDate()));
        cardTradeInfoAppPageResVO.setCreateTimeShow(DateFormatUtil.tradeDateFormat(cardTradeInfoAppPageResDTO.getTransactionDate()));
        cardTradeInfoAppPageResVO.setBankAccountNo(cardTradeInfoAppPageResDTO.getMaskedCardNumber());
        cardTradeInfoAppPageResVO.setBankAccountNoMasked(cardTradeInfoAppPageResDTO.getMaskedCardNumber());

        TotalPrice totalBillPrice = new TotalPrice();
        totalBillPrice.setColor("#333333");
        totalBillPrice.setPrice(TransactionTypeEnum.getPlusOrMinusValue4Saas(cardTradeInfoAppPageResDTO.getTransactionTypeCode(),cardTradeInfoAppPageResDTO.getObversionTotalPrice()));
        totalBillPrice.setShowPrice(TransactionTypeEnum.getPlusOrMinusValue(cardTradeInfoAppPageResDTO.getTransactionTypeCode()) + CurrencyEnum.getCurrencyByCode(cardTradeInfoAppPageResDTO.getObversionCurrType()).getSymbol() + MoneyNumberFormatUtil.formart(cardTradeInfoAppPageResDTO.getObversionTotalPrice()));
        cardTradeInfoAppPageResVO.setTotalPrice(totalBillPrice);

        cardTradeInfoAppPageResVO.setBillCurrencyCode(cardTradeInfoAppPageResDTO.getObversionCurrType());
        com.fenbei.fx.card.common.vo.KeyValueVO checkStatus = new com.fenbei.fx.card.common.vo.KeyValueVO();
        checkStatus.setKey(cardTradeInfoAppPageResDTO.getCheckStatusCode());
        checkStatus.setValue(I18nUtils.transferI18nMessage(CheckStatusEnum.getEnum(cardTradeInfoAppPageResDTO.getCheckStatusCode()).getMsgForClient()));
        //finhub 和 noc 的枚举不一致,以noc枚举为准
        if (CheckStatusEnum.getEnum(cardTradeInfoAppPageResDTO.getCheckStatusCode()).getKey() == CheckStatusEnum.UNCHECK.getKey()){
            checkStatus.setValue(I18nUtils.transferI18nMessage("待核销"));
        }
        checkStatus.setColor("#333333");
        cardTradeInfoAppPageResVO.setCheckStatus(checkStatus);

        TotalPrice tradePrice = new TotalPrice();
        tradePrice.setColor("#333333");
        tradePrice.setPrice(TransactionTypeEnum.getPlusOrMinusValue4Saas(cardTradeInfoAppPageResDTO.getTransactionTypeCode(),cardTradeInfoAppPageResDTO.getTransactionAmount()));
        tradePrice.setShowPrice(TransactionTypeEnum.getPlusOrMinusValue(cardTradeInfoAppPageResDTO.getTransactionTypeCode()) +
            CurrencyEnum.getCurrencyByCode(cardTradeInfoAppPageResDTO.getTransactionCurrency()).getSymbol()
            + MoneyNumberFormatUtil.formart(cardTradeInfoAppPageResDTO.getTransactionAmount()));
        cardTradeInfoAppPageResVO.setTradePrice(tradePrice);
        cardTradeInfoAppPageResVO.setCurrencyCode(cardTradeInfoAppPageResDTO.getTransactionCurrency());
        KeyValueVO bankBindStatus = new KeyValueVO();
        bankBindStatus.setKey(1);
        bankBindStatus.setValue("未创建费用");
        //TODO --
        cardTradeInfoAppPageResVO.setBankBindStatus(bankBindStatus);
        cardTradeInfoAppPageResVO.setBankDesc(cardTradeInfoAppPageResDTO.getMaskedCardNumber());
        cardTradeInfoAppPageResVO.setBankName(FxAcctChannelEnum.AIRWALLEX.getChannelName());
        cardTradeInfoAppPageResVO.setBankNameString(FxAcctChannelEnum.AIRWALLEX.getChannelName());
        if (FxAcctChannelEnum.isLianLian(cardTradeInfoAppPageResDTO.getCardPlatform())){
            cardTradeInfoAppPageResVO.setBankName(FxAcctChannelEnum.LIANLIAN.getChannelName());
            cardTradeInfoAppPageResVO.setBankNameString(FxAcctChannelEnum.LIANLIAN.getChannelName());
        }
        cardTradeInfoAppPageResVO.setBizNo(cardTradeInfoAppPageResDTO.getTransactionId());
        cardTradeInfoAppPageResVO.setIsNew("1");
        TotalPrice payBackPrice = new TotalPrice();
        payBackPrice.setPrice(BigDecimal.ZERO);
        payBackPrice.setShowPrice(TransactionTypeEnum.getPlusOrMinusValue(cardTradeInfoAppPageResDTO.getTransactionTypeCode()) +
            CurrencyEnum.getCurrencyByCode(cardTradeInfoAppPageResDTO.getTransactionCurrency()).getSymbol()
            + MoneyNumberFormatUtil.formart(BigDecimal.ZERO));
        payBackPrice.setColor("#333333");
        cardTradeInfoAppPageResVO.setPayBackPrice(payBackPrice);
        KeyValueVO paybackStatus = new KeyValueVO();
        paybackStatus.setKey(0);
        paybackStatus.setValue("无还款");
        cardTradeInfoAppPageResVO.setPayBackStatus(paybackStatus);
        cardTradeInfoAppPageResVO.setPayMethod(cardTradeInfoAppPageResDTO.getMaskedCardNumber());
        cardTradeInfoAppPageResVO.setPettyCreateTime(DateFormatUtil.tradeDateFormat(cardTradeInfoAppPageResDTO.getCreateTime()));
        cardTradeInfoAppPageResVO.setPettyName(I18nUtils.transferI18nMessage("备用金"));
//        cardTradeInfoAppPageResVO.setPettyId("--");
        KeyValueVO refundStatus = new KeyValueVO();
        if (Objects.equals(TransactionTypeEnum.REFUND.getKey(), cardTradeInfoAppPageResDTO.getTransactionTypeCode())){
            refundStatus.setKey(1);
            refundStatus.setValue("已退款");
            cardTradeInfoAppPageResVO.setRefundPrice(cardTradeInfoAppPageResDTO.getTransactionAmountDesc());
        }else {
            refundStatus.setKey(0);
            refundStatus.setValue("无退款");
            cardTradeInfoAppPageResVO.setRefundPrice(cardTradeInfoAppPageResDTO.getTransactionAmountDesc());
        }
        cardTradeInfoAppPageResVO.setRefundStatus(refundStatus);
        cardTradeInfoAppPageResVO.setShopName(cardTradeInfoAppPageResDTO.getMerchantName());
        cardTradeInfoAppPageResVO.setTitle("--");
        KeyValueVO keyValueVO = new KeyValueVO();
        keyValueVO.setKey(cardTradeInfoAppPageResDTO.getTransactionTypeCode());
        keyValueVO.setValue(I18nUtils.transferI18nMessage(cardTradeInfoAppPageResDTO.getTransactionType()));
        cardTradeInfoAppPageResVO.setTransactionType(keyValueVO);
        cardTradeInfoAppPageResVO.setCnyTradePrice(cardTradeInfoAppPageResDTO.getCnyTradePrice());
        cardTradeInfoAppPageResVO.setTradeCnyExchangeRate(cardTradeInfoAppPageResDTO.getTradeCnyExchangeRate());
        return cardTradeInfoAppPageResVO;
    }


    /**
     * 【App】交易记录详情
     * App→企业卡付→海外卡→全部交易记录（按不同规则筛选）→交易记录详情
     *
     * @param orderId 交易记录ID
     * @param orderId 订单ID 对应表 fx_card_order 的 biz_no 字段
     * @return
     */
    @RequestMapping(value = "app/show", method = {RequestMethod.GET})
    public ItemR<CardTradeInfoAppShowResVO> appShow(String orderId,Integer transactionType) {
        CardTradeInfoAppShowResDTO cardTradeInfoAppShowResDTO = service.appShow(orderId,transactionType);
        CardTradeInfoAppShowResVO item = converter.convertToCardTradeInfoAppShowResVO(cardTradeInfoAppShowResDTO);
        item.setTransactionDateShow(DateFormatUtil.tradeDateFormat(item.getTransactionDate()));

        if (item.getHasWriteOffEntry() && item.getWriteOffStatus() != null && item.getWriteOffStatus().getKey() == WriteOffShowEnum.NO_COST.getKey()){
            //如果当前状态是待创建费用 ，查询当前所有交易列表信息
            CardTradeInfoAppPageReqDTO cardTradeInfoAppPageReqDTO = new CardTradeInfoAppPageReqDTO();
            cardTradeInfoAppPageReqDTO.setBizNo(orderId);
            cardTradeInfoAppPageReqDTO.setAppShow(true); //筛选退款&错花订单
            Page<CardTradeInfoAppPageResDTO> cardTradeInfoAppPageResDTOPage = service.pagination(cardTradeInfoAppPageReqDTO, 1, 100);
            Page<CardTradeInfoAppPageResVO> cardOrderPageResVOPage = convertToCardTradeInfoAppPageResVOPage(cardTradeInfoAppPageResDTOPage);
            item.setRootOrders(cardOrderPageResVOPage.getRecords());
        }
        item.setIsNew("1");

        return responseItemR(ResponseCodeEnum.SUCCESS, item);
    }

    /**
     * 【App】交易记录详情-备注
     * App→企业卡付→海外卡→全部交易记录（按不同规则筛选）→交易记录详情→备注
     *
     * @param cardTradeInfoAppRemarkReqVO 保存备注 VO
     * @return
     */
    @RequestMapping(value = "app/remark", method = {RequestMethod.POST})
    public MessageR appRemark(@Valid @RequestBody CardTradeInfoAppRemarkReqVO cardTradeInfoAppRemarkReqVO) {
        CardTradeInfoAppRemarkReqDTO cardTradeInfoAppRemarkReqDTO = converter.convertToCardTradeInfoAppRemarkReqDTO(cardTradeInfoAppRemarkReqVO);
        Boolean isSuccess = service.appRemark(cardTradeInfoAppRemarkReqDTO);

        return responseMessageR(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 国际卡订单-详情(批量)
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "showByIds", method = {RequestMethod.POST})
    public ItemsResult<CardOrderShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<CardOrderShowResDTO> cardOrderShowResDTOList = service.showByIds(ids.getItems());

        List<CardOrderShowResVO> items = converter.convertToCardOrderShowResVOList(cardOrderShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    /**
     * 国际卡订单-更新
     *
     * @param cardOrderModifyReqVO
     * @return
     */
    @RequestMapping(value = "cardOrder/modify", method = {RequestMethod.POST})
    public MessageResult modify(CardOrderModifyReqVO cardOrderModifyReqVO) {
        CardOrderModifyReqDTO cardOrderModifyReqDTO = converter.convertToCardOrderModifyReqDTO(cardOrderModifyReqVO);

        Boolean isSuccess = service.modify(cardOrderModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 国际卡订单-更新(所有字段)
     *
     * @param cardOrderModifyReqVO
     * @return
     */
    @RequestMapping(value = "cardOrder/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(CardOrderModifyReqVO cardOrderModifyReqVO) {
        CardOrderModifyReqDTO cardOrderModifyReqDTO = converter.convertToCardOrderModifyReqDTO(cardOrderModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(cardOrderModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 国际卡订单-删除
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "cardOrder/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 国际卡订单-删除(批量)
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "cardOrder/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 国际卡订单-删除(参数)
     *
     * @param cardOrderRemoveReqVO
     * @return
     */
    @RequestMapping(value = "cardOrder/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(CardOrderRemoveReqVO cardOrderRemoveReqVO) {
        CardOrderRemoveReqDTO cardOrderRemoveReqDTO = converter.convertToCardOrderRemoveReqDTO(cardOrderRemoveReqVO);

        Boolean isSuccess = service.removeByParams(cardOrderRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
