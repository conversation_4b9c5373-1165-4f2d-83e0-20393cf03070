package com.fenbei.fx.card.web.cardholderapply.controller.web;

import com.fenbei.fx.card.service.cardholder.dto.CardholderDetailResDTO;
import com.fenbei.fx.card.service.cardholderapply.CardholderApplyService;
import com.fenbei.fx.card.service.cardholderapply.dto.*;
import com.fenbei.fx.card.util.ValidateCommonUtils;
import com.fenbei.fx.card.common.enums.CardholderApplyStatusEnum;
import com.fenbei.fx.card.web.cardholder.vo.CardholderApproveReqVo;
import com.fenbei.fx.card.web.cardholderapply.controller.CardholderApplyCommonController;
import com.fenbei.fx.card.web.cardholderapply.converter.CardholderApplyVOConverter;
import com.fenbei.fx.card.web.cardholderapply.vo.*;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 持卡人操作申请 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Api(tags = {"持卡人操作申请 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping("/fxcard/web/cardholderApply")
public class CardholderApplyWebController extends CardholderApplyCommonController<CardholderApplyService, CardholderApplyVOConverter> {

    @ApiOperation(value = "持卡人-创建申请")
    @RequestMapping(value = "/apply/create", method = {RequestMethod.POST})
    public ItemResult<CardholderDetailResDTO> cardholderApplyCreate(@RequestBody CardholderApplyCreateReqVO cardholderApplyCreateReqVO) {
        // 申请检查：权限
        commonCheck(cardholderApplyCreateReqVO);
        // 新建申请单
        CardholderApplyAddReqDTO cardholderApplyAddReqDTO = converter.convertToCardholderApplyReqDTO(cardholderApplyCreateReqVO);
        CardholderDetailResDTO detail = service.applyCreate(cardholderApplyAddReqDTO);
        return responseItem(ResponseCodeEnum.SUCCESS, detail);
    }

    @ApiOperation(value = "持卡人-更新申请")
    @RequestMapping(value = "/apply/modify", method = {RequestMethod.POST})
    public ItemResult<CardholderDetailResDTO>  cardholderApplyModify(@RequestBody CardholderApplyCreateReqVO cardholderApplyCreateReqVO) {
        // 申请检查：权限
        commonCheck(cardholderApplyCreateReqVO);
        // 修改申请单
        CardholderApplyAddReqDTO cardholderApplyAddReqDTO = converter.convertToCardholderApplyReqDTO(cardholderApplyCreateReqVO);
        CardholderDetailResDTO detail = service.applyModify(cardholderApplyAddReqDTO);
        return responseItem(ResponseCodeEnum.SUCCESS, detail);
    }

    @ApiOperation(value = "持卡人-审批")
    @RequestMapping(value = "/approve", method = {RequestMethod.POST})
    public MessageResult approve(@RequestBody CardholderApproveReqVo approveReqVo) {
        ValidateCommonUtils.validate(approveReqVo);
        Boolean isSuccess = service.approve(approveReqVo.getApplyId(), approveReqVo.getStatus(), approveReqVo.getReason());
        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "持卡人申请-删除")
    @RequestMapping(value = "/del", method = {RequestMethod.POST})
    public MessageResult approve(@RequestBody CardholderApplyRemoveReqDTO reqDTO) {
        ValidateCommonUtils.validate(reqDTO);
        Boolean isSuccess = service.removeByApplyId(reqDTO.getApplyId());
        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
