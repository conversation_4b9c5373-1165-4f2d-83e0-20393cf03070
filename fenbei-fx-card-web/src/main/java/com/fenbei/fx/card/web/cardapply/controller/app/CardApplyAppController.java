package com.fenbei.fx.card.web.cardapply.controller.app;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.CardPlatformEnum;
import com.fenbei.fx.card.service.cardapply.CardApplyService;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyShowResDTO;
import com.fenbei.fx.card.service.cardapply.dto.CreateCardApplyReqDTO;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.web.cardapply.converter.CardApplyVOConverter;
import com.fenbei.fx.card.web.cardapply.vo.CardApplyShowResVO;
import com.fenbei.fx.card.web.cardapply.vo.CreateCardApplyReqVO;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.finhub.framework.swift.utils.ObjUtils;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 国际卡操作申请 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Api(tags = {"国际卡操作申请 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/fxcard/app/cardapply")
public class CardApplyAppController extends BaseController<CardApplyService, CardApplyVOConverter> {

    @ApiOperation(value = "创建开卡申请")
    @RequestMapping(value = "/createCardApply", method = {RequestMethod.POST})
    public ItemResult<CardApplyShowResVO>  createCardApply(@RequestBody CreateCardApplyReqVO createCardApplyReqVO) {
        CreateCardApplyReqDTO createCardApplyReqDTO = converter.convertToCreateCardApplyReqDTO(createCardApplyReqVO);
        //新增字段，兼容连连，默认AW,
        if(ObjUtils.isNull(createCardApplyReqDTO.getCardPlatform())){
            createCardApplyReqDTO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
        }
        String userId = UserAuthHolder.getCurrentUser().getUser_id();
        String companyId = UserAuthHolder.getCurrentUser().getCompany_id();
        createCardApplyReqDTO.setEmployeeId(userId);
        createCardApplyReqDTO.setCompanyId(companyId);
        CardApplyShowResDTO cardShowResDTO = service.createCardApply(createCardApplyReqDTO);
        CardApplyShowResVO item = CopyUtils.convert(cardShowResDTO,CardApplyShowResVO.class);
        return responseItem(GlobalCoreResponseCode.SUCCESS, item);
    }

    @ApiOperation(value = "卡申请详情")
    @RequestMapping(value = "/detail", method = {RequestMethod.GET})
    public ItemResult<CardApplyShowResVO> detail(String applyId) {
        CardApplyShowResDTO cardShowResDTO = service.cardApplyDetail(applyId);
        CardApplyShowResVO item = CopyUtils.convert(cardShowResDTO,CardApplyShowResVO.class);
        return responseItem(GlobalCoreResponseCode.SUCCESS, item);
    }

}
