package com.fenbei.fx.card.web.cardapply.controller.web;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.CardApplyStatusEnum;
import com.fenbei.fx.card.service.cardapply.CardApplyService;
import com.fenbei.fx.card.service.cardapply.dto.*;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.web.cardapply.converter.CardApplyVOConverter;
import com.fenbei.fx.card.web.cardapply.vo.*;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 国际卡操作申请 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Api(tags = {"国际卡操作申请 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/fxcard/web/cardapply")
public class CardApplyWebController extends BaseController<CardApplyService, CardApplyVOConverter> {
    @Deprecated
    @ApiOperation(value = "创建开卡申请")
    @RequestMapping(value = "/createCardApply", method = {RequestMethod.POST})
    public ItemResult<CardApplyShowResVO>  createCardApply(@RequestBody CreateCardApplyReqVO createCardApplyReqVO) {
        CreateCardApplyReqDTO createCardApplyReqDTO = converter.convertToCreateCardApplyReqDTO(createCardApplyReqVO);
        CardApplyShowResDTO cardShowResDTO = service.createCardApply(createCardApplyReqDTO);
        CardApplyShowResVO item = CopyUtils.convert(cardShowResDTO,CardApplyShowResVO.class);
        return responseItem(GlobalCoreResponseCode.SUCCESS, item);
    }

    @ApiOperation(value = "编辑审核")
    @RequestMapping(value = "/approvedCardApply", method = {RequestMethod.POST})
    public MessageResult approvedCardApply(@RequestBody UpdateCardApplyReqVO updateCardApplyReqVO) {
        UpdateCardApplyReqDTO updateCardApplyReqDTO = converter.convertToUpdateCardApplyReqDTO(updateCardApplyReqVO);
        String userId = UserAuthHolder.getCurrentUser().getUser_id();
        String companyId = UserAuthHolder.getCurrentUser().getCompany_id();
        updateCardApplyReqDTO.setEmployeeId(userId);
        updateCardApplyReqDTO.setEmployeeName(UserAuthHolder.getCurrentUser().getUser_name());
        updateCardApplyReqDTO.setCompanyId(companyId);
        Boolean isSuccess = service.approvedCardApply(updateCardApplyReqDTO);
//        if(isSuccess&&updateCardApplyReqVO.getCardShowStatus().equals(CardApplyStatusEnum.PASS.getStatus())){
//            //Stereo审核通过，更新卡申请状态
//            isSuccess=service.approvedCardApplyByStereo(updateCardApplyReqVO.getApplyId());
//        }
        return responseMessage(isSuccess ? GlobalCoreResponseCode.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }


    @ApiOperation(value = "卡申请详情")
    @RequestMapping(value = "/detail", method = {RequestMethod.GET})
    public ItemResult<CardApplyShowResVO> detail(String applyId) {
        CardApplyShowResDTO cardShowResDTO = service.cardApplyDetail(applyId);
        CardApplyShowResVO item = CopyUtils.convert(cardShowResDTO, CardApplyShowResVO.class);
        return responseItem(GlobalCoreResponseCode.SUCCESS, item);
    }
}
