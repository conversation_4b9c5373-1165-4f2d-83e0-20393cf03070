package com.fenbei.fx.card.web.card.controller.app;

import com.fenbei.fx.card.service.usercard.UserCardCreditManager;
import com.fenbei.fx.card.service.usercard.UserCardHomePageManager;
import com.fenbei.fx.card.service.usercard.UserCardManager;
import com.fenbei.fx.card.service.usercard.dto.*;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.ControllerSupport;
import com.finhub.framework.web.vo.ItemR;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.PageR;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户国际卡 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-20
 */
@Api(tags = {"用户国际卡 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/buscard/app/usercard" )
public class UserBusCardAppController extends ControllerSupport {


    /**
     * 用户首页国际卡信息
     * @return
     */
    @RequestMapping(value = "/infos", method = {RequestMethod.GET})
    public ItemR<UserCardInfosDTO> userCards() {

        UserCardInfosDTO userCardInfosDTO = UserCardManager.me().userCardInfos(request);

        return responseItemR(ResponseCodeEnum.SUCCESS, userCardInfosDTO);
    }

    /**
     * 用户卡的权限，包括境内卡和海外卡
     * @return
     */
    @RequestMapping(value = "/privilege", method = {RequestMethod.GET})
    public ItemR<UserCardPrivilegeDTO> privilege() {

        UserCardPrivilegeDTO privilegeDTO = UserCardManager.me().userCardPrivilege();

        return responseItemR(ResponseCodeEnum.SUCCESS, privilegeDTO);
    }


    /**
     * 用户卡的最近交易和首页备用金
     * @return ItemResult<UserCardPrivilegeDTO>
     */
    @RequestMapping(value = "/pay/homepage", method = {RequestMethod.GET})
    public ItemResult<UserCardHomePageDTO> payHomepage(String fxCardId) {
        if (StringUtils.isEmpty(fxCardId)){
            //无卡片信息为首页
            UserComInfoVO user = UserAuthHolder.getCurrentUser();
            // 备用金卡片 + 交易列表卡片（3条）
            UserCardHomePageDTO userCardHomePageDTO = UserCardHomePageManager.me().homePageByEmployeeId(user.getCompany_id(),user.getUser_id());
            return responseItem(ResponseCodeEnum.SUCCESS, userCardHomePageDTO);
        }else {
            //有卡片信息为账户详情页,只查询最近交易5条
            //交易列表卡片
            UserCardHomePageDTO userCardHomePageDTO = UserCardHomePageManager.me().homePage(fxCardId);
            return responseItem(ResponseCodeEnum.SUCCESS, userCardHomePageDTO);
        }
    }


}
