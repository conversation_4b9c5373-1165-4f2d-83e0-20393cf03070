package com.fenbei.fx.card.rpc.card;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.api.base.KeyValueRpcVO;
import com.fenbei.fx.card.api.base.PriceRpcVo;
import com.fenbei.fx.card.api.card.ICardTradeService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.api.card.enums.UnCheckOrderSortByEnum;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.common.kafka.KafkaProducer;
import com.fenbei.fx.card.common.threadpool.AirThreadPoolExecutor;
import com.fenbei.fx.card.common.utils.FeiShuUtil;
import com.fenbei.fx.card.constants.RedisKeyConstant;
import com.fenbei.fx.card.service.bankcardflow.BankCardFlowService;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.CardModifyReqDTO;
import com.fenbei.fx.card.service.cardauthorize.CardAuthorizeService;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeDTO;
import com.fenbei.fx.card.service.cardchargingnotice.CardChargingNoticeService;
import com.fenbei.fx.card.service.cardchargingnotice.dto.CardChargingNoticeDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.cardmodelconfig.dto.EmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardmodelconfig.impl.CardModelConfigServiceImpl;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.*;
import com.fenbei.fx.card.service.cardorder.manager.CardOrderConsumeNoticeManager;
import com.fenbei.fx.card.service.cardorder.manager.CardOrderLargeOverNoticeManager;
import com.fenbei.fx.card.service.cardorder.manager.CardOrderManager;
import com.fenbei.fx.card.service.redis.RedissonService;
import com.fenbei.fx.card.service.remote.FxPayAcctManager;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.service.webservice.dto.PushAlertDto;
import com.fenbei.fx.card.util.*;
import com.fenbeitong.expense.management.api.virtual.IVirtualCardBudgetRpcService;
import com.fenbeitong.expense.management.api.virtual.dto.TradeRefundRpcDTO;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.fenbeitong.fxpay.api.enums.ExchangeType;
import com.fenbeitong.fxpay.api.enums.FxAccountStatusEnum;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.fxpay.api.enums.OperatorRoleEnum;
import com.fenbeitong.fxpay.api.enums.charging.ChargingEventType;
import com.fenbeitong.fxpay.api.interfaces.IExchangeRateService;
import com.fenbeitong.fxpay.api.interfaces.IFxCardFundChangingService;
import com.fenbeitong.fxpay.api.vo.*;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * 卡交易
 *
 */
@Slf4j
@DubboService
public class CardTradeServiceImpl implements ICardTradeService {
    @Autowired
    CardOrderService cardOrderService;
    @Autowired
    CardAuthorizeService cardAuthorizeService;
    @Autowired
    CardService cardService;
    @Autowired
    CardModelConfigServiceImpl cardModelConfigService;
    @Autowired
    CardChargingNoticeService cardChargingNoticeService;
    @Autowired
    BankCardFlowService bankCardFlowService;
    @DubboReference
    IFxCardFundChangingService iFxCardFundChangingService;
    @Autowired
    FxPayAcctManager fxPayAcctManager;
    @DubboReference
    private IExchangeRateService iExchangeRateService;
    @DubboReference
    private IVirtualCardBudgetRpcService iVirtualCardBudgetRpcService;
    @Autowired
    private RedissonService redissonService;
    @Autowired
    public MessageService messageService;

    @Autowired
    private CardOrderConsumeNoticeManager cardOrderConsumeNoticeManager;
    @Autowired
    private CardOrderLargeOverNoticeManager cardOrderLargeOverNoticeManager;

    private static final String TITLE = "海外卡通知-";

    public CardTradeServiceImpl() {
    }

    /**
     * 授权
     * @param authorizationRpcReqDTO 授权请求
     * @return AuthorizationRpcRespDTO
     */
    @Override
    public AuthorizationRpcRespDTO authorize(AuthorizationRpcReqDTO authorizationRpcReqDTO) {
        FinhubLogger.info("【授权通知】请求参数->{}", JSON.toJSONString(authorizationRpcReqDTO));
        //业务处理
        AuthorizationRpcRespDTO authorizationRpcRespDTO = new AuthorizationRpcRespDTO();
        authorizationRpcRespDTO.setTransactionId(authorizationRpcReqDTO.getTransactionId());
        authorizationRpcRespDTO.setNetworkTransactionId(authorizationRpcReqDTO.getNetworkTransactionId());
        //0.落授权数据
        CardAuthorizeDTO cardAuthorizeDTO =  cardAuthorizeService.findByTradeIdAndSub(authorizationRpcReqDTO.getNetworkTransactionId(),authorizationRpcReqDTO.getTransactionId(),authorizationRpcReqDTO.getTransactionType());
        if (cardAuthorizeDTO != null){
            authorizationRpcRespDTO.setResponseStatus("AUTHORIZED");
            authorizationRpcRespDTO.setStatusReason("valid transaction");
            return authorizationRpcRespDTO;
        }
        if (addAuthorizeByAuthorize(authorizationRpcReqDTO)){
            //1.是否可以交易验证
            CardDTO cardDTO = cardService.cardDetailByCardId(authorizationRpcReqDTO.getCardId());
            //未查到卡
            if (cardDTO == null){
                authorizationRpcRespDTO.setResponseStatus("DECLINED");
                authorizationRpcRespDTO.setStatusReason("card not found");
                return authorizationRpcRespDTO;
            }
            //卡余额校验
            AuthorizationBillingOrder baseOrder = authorizationRpcReqDTO.getBillingOrder()
                .stream()
                .filter(authorizationBillingOrder -> CurrencyEnum.USD.getCurrencyCode().equals(authorizationBillingOrder.getCurrency())).findFirst().orElse(null);
            boolean isReject = checkIsReject(cardDTO,baseOrder);
            if (isReject){
                authorizationRpcRespDTO.setResponseStatus("DECLINED");
                authorizationRpcRespDTO.setStatusReason("data save error");
                return authorizationRpcRespDTO;
            }
            //2.落订单数据
            CardOrderDTO cardOrderPO = cardOrderService.findByTradeIdAndSub(authorizationRpcReqDTO.getNetworkTransactionId(), authorizationRpcReqDTO.getTransactionId(), Objects.requireNonNull(AirwallexTransactionTypeEnum.getEnum(authorizationRpcReqDTO.getTransactionType())).getTransactionTypeEnum().getKey());
            if (cardOrderPO == null){
                String orderId = BizIdUtils.getFxOrderId();

                CardOrderDTO cardOrderAuthFirstPO = cardOrderService.findByTradeId(authorizationRpcReqDTO.getNetworkTransactionId(), Objects.requireNonNull(AirwallexTransactionTypeEnum.getEnum(authorizationRpcReqDTO.getTransactionType())).getTransactionTypeEnum().getKey());
                if(cardOrderAuthFirstPO!=null){
                    orderId=cardOrderAuthFirstPO.getBizNo();
                }
                addRecordByAuth(authorizationRpcReqDTO,cardDTO,baseOrder,orderId);
                //3.卡冻结金额
                frozenBalance(cardDTO,baseOrder,orderId);
                try {
                    //发送授权成功消息
                    FinhubLogger.info("发送push 消息 ");
                    sendPushAndMsg(baseOrder.getAmount().toString(),orderId ,TransactionTypeEnum.PRE_AUTH,cardDTO.getEmployeeId(),cardDTO.getCompanyId());
                    cardOrderConsumeNoticeManager.sendNoticeMsgForConsume(cardDTO.getFxCardId(),orderId,authorizationRpcReqDTO.getNetworkTransactionId(),authorizationRpcReqDTO.getTransactionId(),TransactionTypeEnum.PRE_AUTH.getKey());
                    cardOrderLargeOverNoticeManager.sendNoticeMsgForLargeOver(cardDTO.getFxCardId(),orderId,authorizationRpcReqDTO.getNetworkTransactionId(),authorizationRpcReqDTO.getTransactionId(),TransactionTypeEnum.PRE_AUTH.getKey());
                }catch (Exception ex){
                    FinhubLogger.error("发送push 消息失败 ",ex);
                }
            }
            authorizationRpcRespDTO.setResponseStatus("AUTHORIZED");
            authorizationRpcRespDTO.setStatusReason("valid transaction");

            KafkaProducer.me().sendCardAuthorizeMsg(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), cardDTO.getFxCardId(), BigDecimalUtils.yuan2fen(baseOrder.getAmount()).abs());
        }else {
            authorizationRpcRespDTO.setResponseStatus("DECLINED");
            authorizationRpcRespDTO.setStatusReason("data save error");
        }
        return authorizationRpcRespDTO;
    }

    /**
     * 交易接口只接收webhook,只是底层实现统一业务逻辑
     * @param transactionAckRpcReqDTO 交易通知
     * @return TransactionAckRpcRespDTO
     */
    @Override
    public TransactionAckRpcRespDTO trade(@Validated TransactionAckRpcReqDTO transactionAckRpcReqDTO) {

        boolean tradeResult = trade0(transactionAckRpcReqDTO);

        String lockKey = RedisKeyConstant.TRADE_REDIS_KEY_PRE + transactionAckRpcReqDTO.getCardId();
        tryUnlock4Trade(lockKey);
        if (tradeResult){
            TransactionAckRpcRespDTO transactionAckRpcRespDTO = new TransactionAckRpcRespDTO();
            transactionAckRpcRespDTO.setCode("success");
            transactionAckRpcRespDTO.setMessage("success");
            return transactionAckRpcRespDTO;
        }else {
            TransactionAckRpcRespDTO transactionAckRpcRespDTO = new TransactionAckRpcRespDTO();
            transactionAckRpcRespDTO.setCode("failed");
            transactionAckRpcRespDTO.setMessage("failed");
            return transactionAckRpcRespDTO;
        }

    }

    private boolean trade0(TransactionAckRpcReqDTO transactionAckRpcReqDTO){
        //1. 交易上锁
        String lockKey = RedisKeyConstant.TRADE_REDIS_KEY_PRE + transactionAckRpcReqDTO.getCardId();
        boolean isLocked = tryLockForTrade(lockKey);
        if (!isLocked){
            return false;
        }
        //唯一性检查
        String tradeId = transactionAckRpcReqDTO.getNetworkTransactionId();
        String subTradeId= transactionAckRpcReqDTO.getTransactionId();
        CardOrderDTO cardOrderPO;
        if(StringUtils.isBlank(subTradeId)){
            cardOrderPO = cardOrderService.findByTradeId(tradeId,Objects.requireNonNull(AirwallexTransactionTypeEnum.getEnum(transactionAckRpcReqDTO.getTransactionType())).getTransactionTypeEnum().getKey());
        }else{
            cardOrderPO = cardOrderService.findByTradeIdAndSub(tradeId,subTradeId,Objects.requireNonNull(AirwallexTransactionTypeEnum.getEnum(transactionAckRpcReqDTO.getTransactionType())).getTransactionTypeEnum().getKey());
        }
        if (cardOrderPO != null){
            FinhubLogger.info("【交易通知】请求参数已经入库，重复请求->{}", JSON.toJSONString(transactionAckRpcReqDTO));
            return true;
        }
        //2. 授权验证
        FinhubLogger.info("【交易通知】请求参数->{}", JSON.toJSONString(transactionAckRpcReqDTO));
        //查询授权数据
        CardAuthorizeDTO cardAuthorizeDTO = queryAuth(transactionAckRpcReqDTO);
        if (cardAuthorizeDTO == null){
            //钉钉预警
            FinhubLogger.error("webhook通知异常,{}",JSON.toJSONString(transactionAckRpcReqDTO));
            //找到授权通知且通知结果为失败,直接响应为业务处理成功即可
            if (Objects.equals(AirwallexTransactionStatusEnum.FAILED.getStatus(), transactionAckRpcReqDTO.getStatus())){
                return true;
            }
        }
        //正常处理
        //1.是否可以交易验证
        AirwallexTransactionTypeEnum airwallexTransactionTypeEnum = AirwallexTransactionTypeEnum.getEnum(transactionAckRpcReqDTO.getTransactionType());
        if (airwallexTransactionTypeEnum == null){
            //未查到对应交易类型
            FinhubLogger.info("无此交易类型,{}",JSON.toJSONString(transactionAckRpcReqDTO));
            return false;
        }
        boolean tradeResult;
        if (Objects.equals(airwallexTransactionTypeEnum.getTransactionType(), AirwallexTransactionTypeEnum.REFUND.getTransactionType())){
            tradeResult = refund(transactionAckRpcReqDTO);
        }else {
            tradeResult = consume(transactionAckRpcReqDTO);
        }
        return tradeResult;
    }
    private void tryUnlock4Trade(String lockKey){
        //放锁
        try {
            redissonService.unLock(lockKey);
        } catch (Exception e) {
            log.error("持卡人申请释放锁失败：{}", lockKey, e);
        }
    }
    private boolean tryLockForTrade(String lockKey){
        try {
            boolean tryLock = redissonService.tryLock4Trade(lockKey);
            if (!tryLock) {
                return false;
            }
        }catch (InterruptedException e){
            return false;
        }
        return true;
    }
    private void chargingNotice(CardOrderDTO cardOrderDTO,CardDTO cardDTO ) {
        CardChargingNoticeDTO cardChargingNoticeDTO = buildChargingNoticeDTO(cardOrderDTO,cardDTO);
        cardChargingNoticeService.saveChargingNotice(cardChargingNoticeDTO);
    }

    private CardChargingNoticeDTO buildChargingNoticeDTO(CardOrderDTO cardOrderDTO, CardDTO cardDTO) {
        CardChargingNoticeDTO cardChargingNoticeDTO = new CardChargingNoticeDTO();
        cardChargingNoticeDTO.setRequestId(cardOrderDTO.getBizNo());
        cardChargingNoticeDTO.setEmployeeId(cardDTO.getEmployeeId());
        cardChargingNoticeDTO.setCompanyId(cardDTO.getCompanyId());
        cardChargingNoticeDTO.setFxCardId(cardDTO.getFxCardId());
        cardChargingNoticeDTO.setEventType(ChargingEventType.FXCARD_CONSUME.getCode());
        cardChargingNoticeDTO.setTradeAmount(cardOrderDTO.getBillTradeAmount().abs());
        cardChargingNoticeDTO.setCardStatus(cardDTO.getCardStatus());
        return cardChargingNoticeDTO;
    }

    private void chargingNotice(CardOrderAddReqDTO cardOrderAddReqDTO,CardDTO cardDTO ) {
        CardChargingNoticeDTO cardChargingNoticeDTO = buildChargingNoticeDTO(cardOrderAddReqDTO,cardDTO);
        cardChargingNoticeService.saveChargingNotice(cardChargingNoticeDTO);
    }

    private CardChargingNoticeDTO buildChargingNoticeDTO(CardOrderAddReqDTO cardOrderAddReqDTO, CardDTO cardDTO) {
        CardChargingNoticeDTO cardChargingNoticeDTO = new CardChargingNoticeDTO();
        cardChargingNoticeDTO.setRequestId(cardOrderAddReqDTO.getBizNo());
        cardChargingNoticeDTO.setEmployeeId(cardDTO.getEmployeeId());
        cardChargingNoticeDTO.setCompanyId(cardDTO.getCompanyId());
        cardChargingNoticeDTO.setFxCardId(cardDTO.getFxCardId());
        cardChargingNoticeDTO.setEventType(ChargingEventType.FXCARD_CONSUME.getCode());
        cardChargingNoticeDTO.setTradeAmount(cardOrderAddReqDTO.getBillTradeAmount().abs());
        cardChargingNoticeDTO.setCardStatus(cardDTO.getCardStatus());
        return cardChargingNoticeDTO;
    }

    @Override
    public List<CardOrderConvertRpcResDTO> uncheckOrderList(UncheckOrderRpcReqDTO rpcReqDTO) {
        log.info("获取待核销列表信息，rpcReqDTO={}", JSON.toJSONString(rpcReqDTO));
        if (StringUtils.isAnyBlank(rpcReqDTO.getCompanyId(), rpcReqDTO.getEmployId())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }


        try {
            //获取未核销的消费定单
            UncheckOrderReqDTO uncheckOrderReqDTO = new UncheckOrderReqDTO();
            BeanUtils.copyProperties(rpcReqDTO, uncheckOrderReqDTO);

            List<CardOrderDTO> uncheckOrders = CardOrderManager.me().getUnchecksByParams(uncheckOrderReqDTO);
            if (CollectionUtils.isEmpty(uncheckOrders)){
                log.info("获取待核销列表信息结果为空，rpcReqDTO={}", JSON.toJSONString(rpcReqDTO));
                uncheckOrders = new ArrayList<>();
            }

            List<CardOrderDTO> unchecksByParamsAndCostId = new ArrayList<>();
            if (StringUtils.isNotBlank(uncheckOrderReqDTO.getCostId())){
                log.info("获取特定费用关联的单据为空，rpcReqDTO={}", JSON.toJSONString(rpcReqDTO));
                unchecksByParamsAndCostId = CardOrderManager.me().getUnchecksByParamsAndCostId(uncheckOrderReqDTO);
                uncheckOrders.addAll(unchecksByParamsAndCostId);
            }

            if (CollectionUtils.isEmpty(uncheckOrders)){
                return new ArrayList<>();
            }

            List<CardOrderConvertRpcResDTO> unchecks = convertList(uncheckOrders);
            //获取相关的退款定单
            List<String> bizNos = uncheckOrders.stream().map(CardOrderDTO::getBizNo).collect(Collectors.toList());
            List<CardOrderDTO> refundOrders = CardOrderManager.me().findRefundByOriBizNos(bizNos);
            List<CardOrderConvertRpcResDTO> refunds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(refundOrders)){
                refunds = convertList(refundOrders);
            }

            //排序
            List<CardOrderConvertRpcResDTO>  rpcRespDTOS = uncheckOrderSort(rpcReqDTO, unchecks, refunds);

            log.info("获取待核销列表信息结果 res={}", JsonUtils.toJson(rpcRespDTOS));
            return rpcRespDTOS;
        } catch (FinhubException e){
            log.warn("获取待核销列表信息 warn params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("获取待核销列表信息 error params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }

    private List<CardOrderConvertRpcResDTO> convertList(List<CardOrderDTO> uncheckOrders) {
        List<CardOrderConvertRpcResDTO> unchecks = new ArrayList<>();
        uncheckOrders.forEach(p->{
            unchecks.add(convertToUserCardTradeInfoDTO(p));
        });
        return unchecks;
    }

    private CardOrderConvertRpcResDTO convertToUserCardTradeInfoDTO(CardOrderDTO cardOrderDTO){
        CardOrderConvertRpcResDTO userCardTradeInfoDTO = new CardOrderConvertRpcResDTO();
        userCardTradeInfoDTO.setTradeCnyExchangeRate(cardOrderDTO.getTradeCnyExchangeRate());
        userCardTradeInfoDTO.setFxCardId(cardOrderDTO.getFxCardId());
        userCardTradeInfoDTO.setTradeRemark(cardOrderDTO.getTradeRemark());
        userCardTradeInfoDTO.setOriOrderId(cardOrderDTO.getOriBizNo());
        userCardTradeInfoDTO.setFbOrderId(Optional.ofNullable(cardOrderDTO.getOriBizNo()).orElse(cardOrderDTO.getBizNo()));
        userCardTradeInfoDTO.setBankName(cardOrderDTO.getCardPlatform());
        userCardTradeInfoDTO.setOrderId(cardOrderDTO.getBizNo());
        userCardTradeInfoDTO.setTradeAddress(cardOrderDTO.getTradeAddress());
        KeyValueRpcVO checkStatus = new KeyValueRpcVO();
        checkStatus.setKey(cardOrderDTO.getCheckStatus());
        checkStatus.setValue(CheckStatusEnum.getEnum(cardOrderDTO.getCheckStatus()).getMsgForClient());

        userCardTradeInfoDTO.setCheckStatus(checkStatus);
        userCardTradeInfoDTO.setBankAccountNo(cardOrderDTO.getMaskedCardNumber());//此处不展示卡号
        userCardTradeInfoDTO.setBankAccountNoMasked(cardOrderDTO.getMaskedCardNumber());
        userCardTradeInfoDTO.setCreateTime(cardOrderDTO.getTradeTime());
        userCardTradeInfoDTO.setCreateTimeShow(DateFormatUtil.tradeDateFormat(cardOrderDTO.getTradeTime()));
        userCardTradeInfoDTO.setShopName(cardOrderDTO.getTradeName());

        PriceRpcVo tradePrice = new PriceRpcVo();
        tradePrice.setPrice(TransactionTypeEnum.getVerPlusOrMinusValue(cardOrderDTO.getType(),BigDecimalUtils.fen2yuan(cardOrderDTO.getTradeAmount())));
        tradePrice.setShowPrice(TransactionTypeEnum.getVerPlusOrMinusValue(cardOrderDTO.getType()) + CurrencyEnum.getCurrencyByCode(cardOrderDTO.getTradeCurrency()).getSymbol() + MoneyNumberFormatUtil.formart(BigDecimalUtils.fen2yuan(cardOrderDTO.getTradeAmount())));
        tradePrice.setCurrencyCode(CurrencyEnum.getCurrencyByCode(cardOrderDTO.getTradeCurrency()).getCurrencyCode());
        userCardTradeInfoDTO.setTradePrice(tradePrice);

        PriceRpcVo totalPrice = new PriceRpcVo();
        totalPrice.setPrice(TransactionTypeEnum.getVerPlusOrMinusValue(cardOrderDTO.getType(),BigDecimalUtils.fen2yuan(cardOrderDTO.getBillTradeAmount())));
        totalPrice.setShowPrice(TransactionTypeEnum.getVerPlusOrMinusValue(cardOrderDTO.getType()) + CurrencyEnum.getCurrencyByCode(cardOrderDTO.getBillTradeCurrency()).getSymbol() + MoneyNumberFormatUtil.formart(BigDecimalUtils.fen2yuan(cardOrderDTO.getBillTradeAmount())));
        totalPrice.setCurrencyCode(CurrencyEnum.getCurrencyByCode(cardOrderDTO.getBillTradeCurrency()).getCurrencyCode());
        userCardTradeInfoDTO.setTotalPrice(totalPrice);

        PriceRpcVo cnyTradePrice = new PriceRpcVo();
        cnyTradePrice.setPrice(TransactionTypeEnum.getVerPlusOrMinusValue(cardOrderDTO.getType(),BigDecimalUtils.fen2yuan(cardOrderDTO.getCnyTradeAmount())));
        cnyTradePrice.setShowPrice(TransactionTypeEnum.getVerPlusOrMinusValue(cardOrderDTO.getType()) + CurrencyEnum.CNY.getSymbol() + MoneyNumberFormatUtil.formart(BigDecimalUtils.fen2yuan(cardOrderDTO.getCnyTradeAmount())));
        totalPrice.setCurrencyCode(CurrencyEnum.CNY.getCurrencyCode());
        userCardTradeInfoDTO.setCnyTradePrice(cnyTradePrice);

        if (CheckStatusEnum.UNCHECK.getKey() != cardOrderDTO.getCheckStatus()) {
            userCardTradeInfoDTO.setUncheckConsume(BigDecimalUtils.fen2yuan(cardOrderDTO.getUncheckedAmount()));
            userCardTradeInfoDTO.setUncheckUse(1);
        }

        KeyValueRpcVO transactionType = new KeyValueRpcVO();
        transactionType.setKey(cardOrderDTO.getType());
        transactionType.setValue(TransactionTypeEnum.getEnum(cardOrderDTO.getType()).getValue());

        userCardTradeInfoDTO.setCurrencyCode(cardOrderDTO.getTradeCurrency());
        userCardTradeInfoDTO.setBillCurrencyCode(cardOrderDTO.getBillTradeCurrency());

        userCardTradeInfoDTO.setTransactionType(transactionType);

        KeyValueRpcVO bankBindStatus = new KeyValueRpcVO();
        bankBindStatus.setKey(1);
        bankBindStatus.setValue("未创建费用");
        userCardTradeInfoDTO.setBankBindStatus(bankBindStatus);
        userCardTradeInfoDTO.setBankDesc(cardOrderDTO.getMaskedCardNumber());
        userCardTradeInfoDTO.setBankName(FxAcctChannelEnum.AIRWALLEX.getChannel());
        userCardTradeInfoDTO.setBankNameString(FxAcctChannelEnum.AIRWALLEX.getChannelName());
        if (FxAcctChannelEnum.isLianLian(cardOrderDTO.getCardPlatform())){
            userCardTradeInfoDTO.setBankName(FxAcctChannelEnum.LIANLIAN.getChannel());
            userCardTradeInfoDTO.setBankNameString(FxAcctChannelEnum.LIANLIAN.getChannelName());
        }
        userCardTradeInfoDTO.setBizNo(cardOrderDTO.getBizNo());
        userCardTradeInfoDTO.setIsNew("1");
        PriceRpcVo payBackPrice = new PriceRpcVo();
        payBackPrice.setPrice(BigDecimal.ZERO);
        payBackPrice.setShowPrice(TransactionTypeEnum.getPlusOrMinusValue(cardOrderDTO.getType()) +
            CurrencyEnum.getCurrencyByCode(cardOrderDTO.getTradeCurrency()).getSymbol()
            + MoneyNumberFormatUtil.formart(BigDecimal.ZERO));
        payBackPrice.setColor("#333333");
        userCardTradeInfoDTO.setPayBackPrice(payBackPrice);
        KeyValueRpcVO paybackStatus = new KeyValueRpcVO();
        paybackStatus.setKey(0);
        paybackStatus.setValue("无还款");
        userCardTradeInfoDTO.setPayBackStatus(paybackStatus);
        userCardTradeInfoDTO.setPayMethod(cardOrderDTO.getMaskedCardNumber());
        userCardTradeInfoDTO.setPettyCreateTime(DateFormatUtil.tradeDateFormat(cardOrderDTO.getCreateTime()));
        //userCardTradeInfoDTO.setPettyName("备用金");
        //userCardTradeInfoDTO.setPettyId("--");
        KeyValueRpcVO refundStatus = new KeyValueRpcVO();
        if (Objects.equals(TransactionTypeEnum.REFUND.getKey(), cardOrderDTO.getType())){
            refundStatus.setKey(1);
            refundStatus.setValue("已退款");
            userCardTradeInfoDTO.setRefundPrice(userCardTradeInfoDTO.getTradePrice());
        }else {
            refundStatus.setKey(0);
            refundStatus.setValue("无退款");
            userCardTradeInfoDTO.setRefundPrice(userCardTradeInfoDTO.getTradePrice());
        }
        userCardTradeInfoDTO.setRefundStatus(refundStatus);
        userCardTradeInfoDTO.setRootOrderId(Optional.ofNullable(cardOrderDTO.getOriBizNo()).orElse(cardOrderDTO.getBizNo()));
        userCardTradeInfoDTO.setTitle("--");
        userCardTradeInfoDTO.setRemarks(cardOrderDTO.getTradeRemark());
        return userCardTradeInfoDTO;
    }

    @Override
    public CardOrderConvertRpcResDTO orderDetail(OrderDetailRpcReqDTO rpcReqDTO) {
        log.info("获取交易单详情，rpcReqDTO={}", JSON.toJSONString(rpcReqDTO));
        if (StringUtils.isAnyBlank(rpcReqDTO.getCompanyId(), rpcReqDTO.getBizNo()) || Objects.isNull(rpcReqDTO.getType())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        try {
            CardOrderDTO orderDTO = CardOrderManager.me().findByBizNoAndCompanyId(rpcReqDTO.getBizNo(), rpcReqDTO.getCompanyId(), rpcReqDTO.getType());
            if (Objects.isNull(orderDTO)){
                log.info("获取交易单不存在，rpcReqDTO={}", JSON.toJSONString(rpcReqDTO));
            }else {
                return convertToUserCardTradeInfoDTO(orderDTO);
            }
            //未查到
            return null;
        } catch (FinhubException e){
            log.warn("获取交易单详情 warn params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("获取交易单详情 error params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }

    @Override
    public Integer getUnbindCostRecordCount(UnbindCostOrderRpcReqDTO rpcReqDTO) {
        log.info("getUnbindCostRecordCount，rpcReqDTO={}", JSON.toJSONString(rpcReqDTO));
        if (StringUtils.isAnyBlank(rpcReqDTO.getCompanyId(), rpcReqDTO.getEmployeeId())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        try {
            return CardOrderManager.me().getUnbindCostRecordCount(rpcReqDTO.getEmployeeId(), rpcReqDTO.getCompanyId());
        } catch (FinhubException e){
            log.warn("getUnbindCostRecordCount warn params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("getUnbindCostRecordCount error params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }

    @Override
    public Integer getNotCheckedCount(String companyId) {
        log.info("getNotCheckedCount，companyId={}", companyId);
        if (StringUtils.isAnyBlank(companyId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        try {
            return CardOrderManager.me().getNotCheckedCount(companyId);
        } catch (FinhubException e){
            log.warn("getNotCheckedCount warn companyId={}", companyId, e);
            throw e;
        } catch (Exception e){
            log.error("getNotCheckedCount error companyId={}", companyId, e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }

    private List<CardOrderConvertRpcResDTO> uncheckOrderSort(UncheckOrderRpcReqDTO rpcReqDTO, List<CardOrderConvertRpcResDTO> unchecks, List<CardOrderConvertRpcResDTO> refunds) {
        List<CardOrderConvertRpcResDTO> rpcRespDTOS = new ArrayList<>();
        if(UnCheckOrderSortByEnum.isTradeTimeAsc(rpcReqDTO.getSortBy())){
            rpcRespDTOS.addAll(unchecks);
            rpcRespDTOS.addAll(refunds);
            rpcRespDTOS.sort(Comparator.comparing(CardOrderConvertRpcResDTO::getCreateTime));
        }else if (UnCheckOrderSortByEnum.isTradeTimeDesc(rpcReqDTO.getSortBy())){
            rpcRespDTOS.addAll(unchecks);
            rpcRespDTOS.addAll(refunds);
            rpcRespDTOS.sort(Comparator.comparing(CardOrderConvertRpcResDTO::getCreateTime).reversed());
        }else if (UnCheckOrderSortByEnum.isTypeTradeTimeDesc(rpcReqDTO.getSortBy())){
            //交易时间由近及远
            unchecks.sort(Comparator.comparing(CardOrderConvertRpcResDTO::getCreateTime).reversed());
            if (CollectionUtils.isEmpty(refunds)){
                rpcRespDTOS.addAll(unchecks);
            } else{
                Map<String, List<CardOrderConvertRpcResDTO>> map = refunds.stream().collect(Collectors.groupingBy(CardOrderConvertRpcResDTO::getOriOrderId));
                unchecks.forEach(p->{
                    rpcRespDTOS.add(p);
                    List<CardOrderConvertRpcResDTO> dtos = map.get(p.getOrderId());
                    if (CollectionUtils.isNotEmpty(dtos)){
                        //由近到远排序
                        dtos.sort(Comparator.comparing(CardOrderConvertRpcResDTO::getCreateTime).reversed());
                        rpcRespDTOS.addAll(dtos);
                    }
                });
            }
        } else {
            rpcRespDTOS.addAll(unchecks);
            rpcRespDTOS.addAll(refunds);
            rpcRespDTOS.sort(Comparator.comparing(CardOrderConvertRpcResDTO::getCreateTime).reversed());
        }

        return rpcRespDTOS;
    }

    /**
     * 查询授权记录, 也可以通过matched_authorizations 查询
     * @param transactionAckRpcReqDTO 交易请求
     * @return CardAuthorizeDTO
     */
    public CardAuthorizeDTO queryAuth(TransactionAckRpcReqDTO transactionAckRpcReqDTO){
        CardAuthorizeDTO cardAuthorizeDTO = null;
        if (AirwallexTransactionTypeEnum.CLEARING.getTransactionType().equals(transactionAckRpcReqDTO.getTransactionType())){
            cardAuthorizeDTO =  cardAuthorizeService.findByTradeId(transactionAckRpcReqDTO.getNetworkTransactionId(),AirwallexTransactionTypeEnum.CLEARING.getTransactionType());
        }
        if (cardAuthorizeDTO == null){
            cardAuthorizeDTO =  cardAuthorizeService.findByTradeId(transactionAckRpcReqDTO.getNetworkTransactionId(),AirwallexTransactionTypeEnum.AUTHORIZATION.getTransactionType());
        }
        return cardAuthorizeDTO;
    }

    public void modifyBalance(String id,BigDecimal afterBalance){
        CardModifyReqDTO cardModifyReqDTO = new CardModifyReqDTO();
        cardModifyReqDTO.setId(id);
        cardModifyReqDTO.setBalance(afterBalance);
        cardService.modify(cardModifyReqDTO);
    }
    public void unfrozenBalance(String id,BigDecimal afterBalance,BigDecimal freezenBalance){
        CardModifyReqDTO cardModifyReqDTO = new CardModifyReqDTO();
        cardModifyReqDTO.setId(id);
        cardModifyReqDTO.setBalance(afterBalance);
        cardModifyReqDTO.setFreezenBalance(freezenBalance);
        cardService.modify(cardModifyReqDTO);
    }
    public void frozenBalance(CardDTO cardDTO,AuthorizationBillingOrder baseOrder,String bizNo){
        FinhubLogger.info("frozenBalance success req:{}",JSON.toJSONString(baseOrder));

        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(),cardDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        CardModifyReqDTO cardModifyReqDTO = new CardModifyReqDTO();
        cardModifyReqDTO.setId(cardDTO.getId());
        //卡余额校验
        BigDecimal baseTradeAmount = BigDecimalUtils.yuan2fen(baseOrder.getAmount()).abs();
        BigDecimal afterBalance = cardDTO.getBalance().subtract(baseTradeAmount);
        BigDecimal afterFrozenBalance = cardDTO.getFreezenBalance().add(baseTradeAmount);
        cardModifyReqDTO.setBalance(afterBalance);
        cardModifyReqDTO.setFreezenBalance(afterFrozenBalance);
        cardService.modify(cardModifyReqDTO);
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = buildBankCardFlowAddReqDTO(bizNo,cardModel,cardDTO);
        bankCardFlowAddReqDTO.setOperationType(CardFlowOperationTypeEnum.FROZEN.getCode());
        bankCardFlowAddReqDTO.setCurrentAmount(cardDTO.getBalance());
        bankCardFlowAddReqDTO.setOperationAmount(baseTradeAmount);
        bankCardFlowAddReqDTO.setBalance(afterBalance);
        bankCardFlowAddReqDTO.setOperationFreezenAmount(baseTradeAmount);
        bankCardFlowAddReqDTO.setFreezenBalance(afterFrozenBalance);
        bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
        bankCardFlowService.add(bankCardFlowAddReqDTO);
    }

    /**
     * 业务规则
     *      1. 交易状态为失败,则都要解冻
     *      2. 交易状态为成功,则
     *          AUTHORIZATION无动作
     *          CLEARING 解冻加扣款
     *          REVERSAL 解冻
     *          ORIGINAL_CREDIT 发报警消息
     * @param transactionAckRpcReqDTO webhook通知交易信息
     */
    public boolean consume(TransactionAckRpcReqDTO transactionAckRpcReqDTO){
        CardDTO cardDTO = cardService.cardDetailByCardId(transactionAckRpcReqDTO.getCardId());
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(),cardDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        List<CardOrderDTO> cardOrderAuthDTOS = cardOrderService.findListByTradeId(transactionAckRpcReqDTO.getNetworkTransactionId(), TransactionTypeEnum.PRE_AUTH.getKey());
        if (ObjUtils.isEmpty(cardOrderAuthDTOS)){
            return false;
        }
        //如果是撤销,则不需要扣款
        if (Objects.equals(AirwallexTransactionTypeEnum.REVERSAL.getTransactionType(), transactionAckRpcReqDTO.getTransactionType())){
            for (CardOrderDTO cardOrderDTO:cardOrderAuthDTOS){
                consume0(transactionAckRpcReqDTO,cardDTO,cardOrderDTO,cardModel);
            }
        }
        //收到授权通知且最终交易结果为拒绝时,解冻
        if (Objects.equals(AirwallexTransactionTypeEnum.AUTHORIZATION.getTransactionType(), transactionAckRpcReqDTO.getTransactionType())) {
            for (CardOrderDTO cardOrderDTO:cardOrderAuthDTOS){
                if (Objects.equals(AirwallexTransactionStatusEnum.FAILED.getStatus(), transactionAckRpcReqDTO.getStatus())) {
                    consume0(transactionAckRpcReqDTO,cardDTO,cardOrderDTO,cardModel);
                }else {
                    //AUTHORIZATION webhook 不做动作,等CLEARING的时候再解冻并扣款
                    FinhubLogger.info("AUTHORIZATION success no action:{}",JsonUtils.toJson(transactionAckRpcReqDTO));
                }
            }
        }
        //CLEARING 通知但是是FAILED
        if (Objects.equals(AirwallexTransactionTypeEnum.CLEARING.getTransactionType(), transactionAckRpcReqDTO.getTransactionType())) {
            // 检查billingCurrency是否为美元，如果不是则发送飞书报警并跳过后续逻辑
            if (StringUtils.isNotBlank(transactionAckRpcReqDTO.getBillingCurrency()) &&
                !CurrencyEnum.USD.getCurrencyCode().equalsIgnoreCase(transactionAckRpcReqDTO.getBillingCurrency())) {
                // 发送飞书报警
                String alertMessage = String.format("AIRWALLEX CLEARING通知异常：billingCurrency为%s，非USD币种。交易ID：%s，卡ID：%s",
                    transactionAckRpcReqDTO.getBillingCurrency(),
                    transactionAckRpcReqDTO.getTransactionId(),
                    transactionAckRpcReqDTO.getCardId());
                FeiShuUtil.me().sendMsg(FeiShuTypeEnum.billCurrentNoUSD,alertMessage);
                FinhubLogger.error("AIRWALLEX CLEARING billingCurrency非USD报警：{}", alertMessage);
                // 跳过后续逻辑处理
                return true;
            }

            if (Objects.equals(AirwallexTransactionStatusEnum.FAILED.getStatus(), transactionAckRpcReqDTO.getStatus())) {
                for (CardOrderDTO cardOrderDTO : cardOrderAuthDTOS) {
                    consume0(transactionAckRpcReqDTO, cardDTO, cardOrderDTO, cardModel);
                }
            } else if (Objects.equals(AirwallexTransactionStatusEnum.APPROVED.getStatus(), transactionAckRpcReqDTO.getStatus())) {
                //除撤销之外的成功状态都需要扣款
                FinhubLogger.info("CLEARING success do action:{}", JsonUtils.toJson(transactionAckRpcReqDTO));
                if (cardOrderAuthDTOS.size() == 1) {
                    //一次预授权
                    CardOrderDTO cardOrderDTO = cardOrderAuthDTOS.get(0);
                    //按比例解冻
                    BigDecimal newBillTransactionAmount = transactionAckRpcReqDTO.getTransactionAmount().divide(cardOrderDTO.getTradeAmount(), 2, RoundingMode.HALF_UP).multiply(cardOrderDTO.getBillTradeAmount());
                    BigDecimal billAmount = BigDecimalUtils.yuan2fen(newBillTransactionAmount).abs();
                    CardOrderDTO releaseOrder = cardOrderService.findByTradeId(transactionAckRpcReqDTO.getNetworkTransactionId(), TransactionTypeEnum.PRE_AUTH_RELEASE.getKey());
                    //没有解冻过
                    BigDecimal unReleaseAmount = cardOrderDTO.getBillTradeAmount();
                    if (releaseOrder != null) {
                        unReleaseAmount = unReleaseAmount.subtract(releaseOrder.getBillTradeAmount());
                    }
                    BigDecimal balanceAfterUnfreezen = BigDecimal.ZERO;
                    BigDecimal freezenBalanceAfterUnfreezen = cardDTO.getFreezenBalance().subtract(billAmount);
                    //冻结余额最小为0
                    if (BigDecimalUtils.hasNoPrice(freezenBalanceAfterUnfreezen)) {
                        freezenBalanceAfterUnfreezen = BigDecimal.ZERO;
                    }
                    if (BigDecimalUtils.hasPrice(unReleaseAmount)) {
                        balanceAfterUnfreezen = consume0(transactionAckRpcReqDTO, cardDTO, cardOrderDTO, cardModel);
                    } else {
                        //消费无需解冻,说明超额消费了
                        balanceAfterUnfreezen = cardDTO.getBalance();
                    }
                    //3.扣款并落订单
                    //扣款按照结算金额扣款
                    billAmount = BigDecimalUtils.yuan2fen(transactionAckRpcReqDTO.getBillingAmount()).abs();
                    BigDecimal balanceAfterConsume = balanceAfterUnfreezen.subtract(billAmount);
                    modifyBalance(cardDTO.getId(), balanceAfterConsume);
                    //记录流水
                    BankCardFlowAddReqDTO bankCardFlowAddReqDTOOfConsume = buildBankCardFlowAddReqDTO(cardOrderDTO.getBizNo(), cardModel, cardDTO);
                    bankCardFlowAddReqDTOOfConsume.setOperationType(CardFlowOperationTypeEnum.CONSUME.getCode());
                    bankCardFlowAddReqDTOOfConsume.setCurrentAmount(balanceAfterUnfreezen);
                    bankCardFlowAddReqDTOOfConsume.setOperationAmount(billAmount);
                    bankCardFlowAddReqDTOOfConsume.setBalance(balanceAfterConsume);
                    bankCardFlowAddReqDTOOfConsume.setOperationFreezenAmount(BigDecimal.ZERO);
                    bankCardFlowAddReqDTOOfConsume.setFreezenBalance(freezenBalanceAfterUnfreezen);
                    bankCardFlowAddReqDTOOfConsume.setCurrentFreezenAmount(freezenBalanceAfterUnfreezen);
                    bankCardFlowService.add(bankCardFlowAddReqDTOOfConsume);
                    CardOrderAddReqDTO cardOrderAddReqDTO = buildCardOrderAddReqDTO(cardDTO, transactionAckRpcReqDTO);
                    cardOrderAddReqDTO.setBizNo(cardOrderDTO.getBizNo());
                    cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
                    //待核销相关金额与交易金额及其币种保持一致
                    cardOrderAddReqDTO.setUncheckedAmount(cardOrderAddReqDTO.getTradeAmount());
                    cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
                    cardOrderAddReqDTO.setNeedNotCheckAmount(BigDecimal.ZERO);
                    cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.UNCHECK.getKey());
                    cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
                    cardOrderAddReqDTO.setMaskedCardNumber(transactionAckRpcReqDTO.getMaskedCardNumber());
                    cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
                    cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
                    cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());
                    currencyExchangeRate(cardOrderAddReqDTO);
                    //交易币种兑人民币汇率计算，以及人民币金额计算
//                    cardOrderAddReqDTO.setTradeCnyExchangeRate(cardOrderDTO.getTradeCnyExchangeRate());
//                    cardOrderAddReqDTO.setBillTradeCnyExchangeRate(cardOrderDTO.getBillTradeCnyExchangeRate());
//                    cardOrderAddReqDTO.setCnyTradeAmount(getCnyTradeAmount(cardOrderDTO.getTradeCnyExchangeRate(), cardOrderAddReqDTO.getTradeAmount()));
                    FinhubLogger.info("consume 交易  添加付款记录 req = {} ", JSON.toJSONString(cardOrderAddReqDTO));

                    cardOrderService.add(cardOrderAddReqDTO);
                    //消费计费通知
                    chargingNotice(cardOrderDTO, cardDTO);
                    asyncCallAcct(buildFundChangingReq(cardDTO, cardOrderDTO.getBizNo(), null, transactionAckRpcReqDTO), CardFlowOperationTypeEnum.CONSUME);
                    //更新额度申请上的可用余额
                    updateAvailable(cardDTO.getFxCardId(), billAmount);
                } else {
                    //多次预授权,全额解冻，扣款
                    String bizNo = null;
                    BigDecimal tradeCnyExchangeRate = BigDecimal.ONE;
                    BigDecimal billTradeCnyExchangeRate = BigDecimal.ONE;

                    for (CardOrderDTO cardOrderDTO : cardOrderAuthDTOS) {
                        bizNo = cardOrderDTO.getBizNo();
                        tradeCnyExchangeRate = cardOrderDTO.getTradeCnyExchangeRate();
                        billTradeCnyExchangeRate = cardOrderDTO.getBillTradeCnyExchangeRate();
                        cardDTO = cardService.cardDetailByCardId(transactionAckRpcReqDTO.getCardId());
                        consume0(transactionAckRpcReqDTO, cardDTO, cardOrderDTO, cardModel);
                    }
                    //重新获取卡信息
                    cardDTO = cardService.cardDetailByCardId(transactionAckRpcReqDTO.getCardId());
                    BigDecimal currentAmount = cardDTO.getBalance();
                    //3.扣款并落订单
                    //扣款按照结算金额扣款
                    BigDecimal billAmount = BigDecimalUtils.yuan2fen(transactionAckRpcReqDTO.getBillingAmount()).abs();
                    BigDecimal balanceAfterConsume = currentAmount.subtract(billAmount);
                    modifyBalance(cardDTO.getId(), balanceAfterConsume);
                    //记录流水
                    BankCardFlowAddReqDTO bankCardFlowAddReqDTOOfConsume = buildBankCardFlowAddReqDTO(bizNo, cardModel, cardDTO);
                    bankCardFlowAddReqDTOOfConsume.setOperationType(CardFlowOperationTypeEnum.CONSUME.getCode());
                    bankCardFlowAddReqDTOOfConsume.setCurrentAmount(currentAmount);
                    bankCardFlowAddReqDTOOfConsume.setOperationAmount(billAmount);
                    bankCardFlowAddReqDTOOfConsume.setBalance(balanceAfterConsume);
                    bankCardFlowAddReqDTOOfConsume.setOperationFreezenAmount(BigDecimal.ZERO);
                    bankCardFlowAddReqDTOOfConsume.setFreezenBalance(cardDTO.getFreezenBalance());
                    bankCardFlowAddReqDTOOfConsume.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
                    bankCardFlowService.add(bankCardFlowAddReqDTOOfConsume);

                    CardOrderAddReqDTO cardOrderAddReqDTO = buildCardOrderAddReqDTO(cardDTO, transactionAckRpcReqDTO);
                    cardOrderAddReqDTO.setBizNo(bizNo);
                    cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
                    cardOrderAddReqDTO.setUncheckedAmount(cardOrderAddReqDTO.getTradeAmount());
                    cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
                    cardOrderAddReqDTO.setNeedNotCheckAmount(BigDecimal.ZERO);
                    cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.UNCHECK.getKey());
                    cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
                    cardOrderAddReqDTO.setMaskedCardNumber(transactionAckRpcReqDTO.getMaskedCardNumber());
                    cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
                    cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
                    cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());
                    //交易币种兑人民币汇率计算，以及人民币金额计算
                    cardOrderAddReqDTO.setTradeCnyExchangeRate(tradeCnyExchangeRate);
                    cardOrderAddReqDTO.setBillTradeCnyExchangeRate(billTradeCnyExchangeRate);
                    cardOrderAddReqDTO.setCnyTradeAmount(getCnyTradeAmount(tradeCnyExchangeRate, cardOrderAddReqDTO.getTradeAmount()));
                    FinhubLogger.info("consume 交易  添加付款记录 req = {} ", JSON.toJSONString(cardOrderAddReqDTO));

                    cardOrderService.add(cardOrderAddReqDTO);
                    //消费计费通知
                    chargingNotice(cardOrderAddReqDTO, cardDTO);
                    asyncCallAcct(buildFundChangingReq(cardDTO, bizNo, null, transactionAckRpcReqDTO), CardFlowOperationTypeEnum.CONSUME);
                    //更新额度申请上的可用余额
                    updateAvailable(cardDTO.getFxCardId(), billAmount);
                }
                KafkaProducer.me().sendCardTradeMsg(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), cardDTO.getFxCardId(), BigDecimalUtils.yuan2fen(transactionAckRpcReqDTO.getBillingAmount()).abs());
            }
        }
         return true;
    }
    public void updateAvailable(String fxCardId,BigDecimal billAmount){
        FinhubLogger.info("updateAvailable fxCardId = {} , billAmount = {}" , fxCardId , billAmount);
        //更新额度申请上的可用余额
        List<CardCreditManagerDTO> apply = CardCreditManagerManager.me().queryUncheckApply(fxCardId);
        CardCreditManagerDTO canUse = null;
        for (CardCreditManagerDTO cardCreditManagerDTO: apply){
            boolean isCanUse =  cardCreditManagerDTO.getAvalibleAmount().compareTo(billAmount) >=0;
            if (isCanUse){
                canUse = cardCreditManagerDTO;
                break;
            }
        }
        FinhubLogger.info("updateAvailable canUse = {} ", JSON.toJSONString(canUse));
        if (canUse != null) {
            CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
            cardCreditManagerModifyReqDTO.setId(Long.valueOf(canUse.getId()));
            cardCreditManagerModifyReqDTO.setAvalibleAmount(canUse.getAvalibleAmount().subtract(billAmount));
            CardCreditManagerManager.me().modify(cardCreditManagerModifyReqDTO);
        }else {
            CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
            cardCreditManagerModifyReqDTO.setId(Long.valueOf(apply.get(0).getId()));
            cardCreditManagerModifyReqDTO.setAvalibleAmount(apply.get(0).getAvalibleAmount().subtract(billAmount));
            CardCreditManagerManager.me().modify(cardCreditManagerModifyReqDTO);
        }
        FinhubLogger.info("updateAvailable CardCreditManagerManager modify fxCardId = {} ", fxCardId);
    }
    /**
     * 标准交易流程
     * 1. 解冻
     * 2. 记录卡流水
     * 3. 记录订单
     */
    public BigDecimal consume0(TransactionAckRpcReqDTO transactionAckRpcReqDTO,CardDTO cardDTO,CardOrderDTO cardOrderDTO,Integer cardModel){
        //按比例解冻
        BigDecimal billAmount = cardOrderDTO.getBillTradeAmount();
        BigDecimal balanceAfterUnfreezen = cardDTO.getBalance().add(billAmount);
        BigDecimal freezenBalanceAfterUnfreezen = cardDTO.getFreezenBalance().subtract(billAmount);
        BigDecimal operationFreezenAmount = billAmount;
        BigDecimal operationAmount = billAmount;
        //冻结余额最小为0
        if (BigDecimalUtils.hasNoPrice(freezenBalanceAfterUnfreezen)){
            freezenBalanceAfterUnfreezen = BigDecimal.ZERO;
            operationFreezenAmount = cardDTO.getFreezenBalance();
            //只能全额解冻
            balanceAfterUnfreezen = cardDTO.getBalance().add(cardDTO.getFreezenBalance());
            operationAmount = cardDTO.getFreezenBalance();
        }
        //2.解冻(避免解冻超额,验证下解冻记录和金额) 并落单
        unfrozenBalance(cardDTO.getId(), balanceAfterUnfreezen, freezenBalanceAfterUnfreezen);
        //记录流水
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = buildBankCardFlowAddReqDTO(cardOrderDTO.getBizNo(), cardModel, cardDTO);
        bankCardFlowAddReqDTO.setOperationType(CardFlowOperationTypeEnum.UNFROZEN.getCode());
        bankCardFlowAddReqDTO.setCurrentAmount(cardDTO.getBalance());
        bankCardFlowAddReqDTO.setOperationAmount(operationAmount);
        bankCardFlowAddReqDTO.setBalance(balanceAfterUnfreezen);
        bankCardFlowAddReqDTO.setOperationFreezenAmount(operationFreezenAmount);
        bankCardFlowAddReqDTO.setFreezenBalance(freezenBalanceAfterUnfreezen);
        bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
        bankCardFlowService.add(bankCardFlowAddReqDTO);
        reversal(cardDTO,cardOrderDTO,transactionAckRpcReqDTO,operationAmount);

        return balanceAfterUnfreezen;
    }
    /**
     * 预定释放
     * @param cardDTO 卡
     * @param cardOrderDTO 交易订单
     * @param transactionAckRpcReqDTO webhook通知
     */
    public void reversal(CardDTO cardDTO,CardOrderDTO cardOrderDTO,TransactionAckRpcReqDTO transactionAckRpcReqDTO,BigDecimal billAmount){
        CardOrderAddReqDTO cardOrderAddReqDTO = buildCardOrderAddReqDTO(cardDTO,transactionAckRpcReqDTO);


        //人工设置为预定释放
        cardOrderAddReqDTO.setType(AirwallexTransactionTypeEnum.REVERSAL.getTransactionTypeEnum().getKey());
        cardOrderAddReqDTO.setTradeAmount(cardOrderDTO.getTradeAmount());
        cardOrderAddReqDTO.setTradeCurrency(cardOrderDTO.getTradeCurrency());
        cardOrderAddReqDTO.setBillTradeAmount(cardOrderDTO.getBillTradeAmount());
        cardOrderAddReqDTO.setBillTradeCurrency(transactionAckRpcReqDTO.getBillingCurrency());

        cardOrderAddReqDTO.setBizNo(cardOrderDTO.getBizNo());
        cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
        //待核销相关金额与交易金额及其币种保持一致
        cardOrderAddReqDTO.setNeedNotCheckAmount(cardOrderAddReqDTO.getTradeAmount());
        cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
        cardOrderAddReqDTO.setTradeCnyExchangeRate(cardOrderDTO.getTradeCnyExchangeRate());
        cardOrderAddReqDTO.setBillTradeCnyExchangeRate(cardOrderDTO.getBillTradeCnyExchangeRate());
        cardOrderAddReqDTO.setCnyTradeAmount(getCnyTradeAmount(cardOrderDTO.getTradeCnyExchangeRate(), cardOrderAddReqDTO.getTradeAmount()));
        cardOrderAddReqDTO.setOrderShow(0);
        cardOrderService.add(cardOrderAddReqDTO);
        //同时将预授权交易设置为不展示
        CardOrderListReqDTO cardOrderListReqDTO = new CardOrderListReqDTO();
        cardOrderListReqDTO.setBizNo(cardOrderDTO.getBizNo());
        cardOrderListReqDTO.setTradeId(cardOrderDTO.getTradeId());
        cardOrderListReqDTO.setSubTradeId(cardOrderDTO.getSubTradeId());
        cardOrderListReqDTO.setType(TransactionTypeEnum.PRE_AUTH.getKey());
        CardOrderListResDTO cardOrderListResDTO = cardOrderService.listOne(cardOrderListReqDTO);
        if (cardOrderListResDTO != null) {
            cardOrderService.updateOrderShow(String.valueOf(cardOrderListResDTO.getId()));
        }
    }
    public FundChangingReq buildFundChangingReq(CardDTO cardDTO,String orderId,String oriOrderId,TransactionAckRpcReqDTO transactionAckRpcReqDTO){
        FundChangingReq fundChangingReq = new FundChangingReq();
        fundChangingReq.setChannel(FxAcctChannelEnum.AIRWALLEX.getChannel());
        fundChangingReq.setAmount(BigDecimalUtils.yuan2fen(transactionAckRpcReqDTO.getBillingAmount()).abs());
        fundChangingReq.setCurrency(transactionAckRpcReqDTO.getBillingCurrency());
        fundChangingReq.setCompanyId(cardDTO.getCompanyId());
        fundChangingReq.setBizNo(orderId);
        fundChangingReq.setOriBizNo(oriOrderId);
        fundChangingReq.setBankTransNo(transactionAckRpcReqDTO.getNetworkTransactionId());
        try {
            fundChangingReq.setBankTransTime(DateTimeUtil.fromUtcToDate(transactionAckRpcReqDTO.getTransactionDate(), "yyyy-MM-dd'T'HH:mm:ss.SSSZ"));
        } catch (Exception e){
            FinhubLogger.warn("交易时间转换失败:{}",JsonUtils.toJson(transactionAckRpcReqDTO));
        }
        fundChangingReq.setOperatorId(cardDTO.getEmployeeId());
        fundChangingReq.setOperatorName(cardDTO.getNameOnCard());
        fundChangingReq.setOperatorRole(OperatorRoleEnum.COMPANY_EMPLOEE);
        return fundChangingReq;
    }
    private void asyncCallAcct(FundChangingReq fundChangingReq,CardFlowOperationTypeEnum cardFlowOperationTypeEnum) {
        CompletableFuture.runAsync(()->{
            try {
                if (Objects.equals(cardFlowOperationTypeEnum.getCode(), CardFlowOperationTypeEnum.CONSUME.getCode())){
                    ResponseVo<FundChangingRes> responseVo = iFxCardFundChangingService.consume(fundChangingReq);
                    FinhubLogger.info(JSON.toJSONString(responseVo));
                }
                if (Objects.equals(cardFlowOperationTypeEnum.getCode(), CardFlowOperationTypeEnum.REFUND.getCode())){
                    ResponseVo<FundChangingRes> responseVo = iFxCardFundChangingService.refund(fundChangingReq);
                    FinhubLogger.info(JSON.toJSONString(responseVo));
                }

            } catch (Exception e){
                FinhubLogger.error("异步调用账户操作，applyDTO={}", JsonUtils.toJson(fundChangingReq), e);
            }
        }, AirThreadPoolExecutor.consumeExecutorInstance);
    }
    public boolean refund(TransactionAckRpcReqDTO transactionAckRpcReqDTO){
        CardDTO cardDTO = cardService.cardDetailByCardId(transactionAckRpcReqDTO.getCardId());
        CardOrderDTO cardOrderDTO = cardOrderService.findByTradeId(transactionAckRpcReqDTO.getNetworkTransactionId(),TransactionTypeEnum.CONSUME.getKey());
        //TODO 多次退款得考虑
        CardOrderDTO refund = cardOrderService.findByTradeId(transactionAckRpcReqDTO.getNetworkTransactionId(),TransactionTypeEnum.REFUND.getKey());
        if (cardOrderDTO != null && refund == null){
            //交易币种金额
            BigDecimal tradeAmount = BigDecimalUtils.yuan2fen(transactionAckRpcReqDTO.getTransactionAmount()).abs();
            //结算币种金额
            BigDecimal billAmount = BigDecimalUtils.yuan2fen(transactionAckRpcReqDTO.getBillingAmount()).abs();
            BigDecimal afterBalance = cardDTO.getBalance().add(billAmount);
            modifyBalance(cardDTO.getId(),afterBalance);
            EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(),cardDTO.getEmployeeId());
            //当前生效模式
            Integer cardModel = employeeModelConfigDTO.getActiveModel();
            BankCardFlowAddReqDTO bankCardFlowAddReqDTO = buildBankCardFlowAddReqDTO(cardOrderDTO.getBizNo(),cardModel,cardDTO);
            bankCardFlowAddReqDTO.setOperationType(CardFlowOperationTypeEnum.REFUND.getCode());
            bankCardFlowAddReqDTO.setCurrentAmount(cardDTO.getBalance());
            bankCardFlowAddReqDTO.setOperationAmount(billAmount);
            bankCardFlowAddReqDTO.setBalance(afterBalance);
            bankCardFlowAddReqDTO.setOperationFreezenAmount(BigDecimal.ZERO);
            bankCardFlowAddReqDTO.setFreezenBalance(cardDTO.getFreezenBalance());
            bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
            bankCardFlowService.add(bankCardFlowAddReqDTO);
            String refundOrderId = BizIdUtils.getFxRefundOrderId();
            CardOrderAddReqDTO cardOrderAddReqDTO =  buildCardOrderAddReqDTO(cardDTO,transactionAckRpcReqDTO);
            cardOrderAddReqDTO.setBizNo(refundOrderId);
            cardOrderAddReqDTO.setOriBizNo(cardOrderDTO.getBizNo());
            cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
            //待核销相关金额与交易金额及其币种保持一致
            cardOrderAddReqDTO.setNeedNotCheckAmount(cardOrderAddReqDTO.getTradeAmount());
            BigDecimal currencyRate = queryCurrencyExchangeRate();
            BigDecimal tradeRate = tradeCnyExchangeRateCal(cardOrderAddReqDTO.getBillTradeAmount(), cardOrderAddReqDTO.getTradeAmount(),currencyRate);
//            BigDecimal rate = cardOrderDTO.getTradeCnyExchangeRate();

            cardOrderAddReqDTO.setBillTradeCnyExchangeRate(currencyRate);
            if ("CNY".equals(cardOrderDTO.getTradeCurrency())){
                BigDecimal cnyTradeAmount = tradeAmount;
                cardOrderAddReqDTO.setTradeCnyExchangeRate(new BigDecimal("1.0000"));
                cardOrderAddReqDTO.setCnyTradeAmount(cnyTradeAmount);
            }else {
                cardOrderAddReqDTO.setTradeCnyExchangeRate(tradeRate);
                BigDecimal cnyTradeAmount = getCnyTradeAmount(currencyRate, cardOrderAddReqDTO.getBillTradeAmount());
                cardOrderAddReqDTO.setCnyTradeAmount(cnyTradeAmount);
            }
            cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
            cardOrderAddReqDTO.setMaskedCardNumber(transactionAckRpcReqDTO.getMaskedCardNumber());
            cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
            cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
            cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());
            cardOrderService.add(cardOrderAddReqDTO);
            FinhubLogger.info("海外卡  退款 入库  req = {}",JSON.toJSONString(cardOrderAddReqDTO));
            modifyCheckInfo(cardOrderDTO,tradeAmount,refundOrderId);
            asyncCallAcct(buildFundChangingReq(cardDTO,refundOrderId,cardOrderDTO.getBizNo(), transactionAckRpcReqDTO),CardFlowOperationTypeEnum.REFUND);
            //更新额度申请上的可用余额
            List<CardCreditManagerDTO> apply = CardCreditManagerManager.me().queryUncheckApply(cardDTO.getFxCardId());
            CardCreditManagerDTO canUse = null;
            for (CardCreditManagerDTO cardCreditManagerDTO: apply){
                boolean isCanUse =  cardCreditManagerDTO.getAvalibleAmount().compareTo(billAmount) >=0;
                if (isCanUse){
                    canUse = cardCreditManagerDTO;
                    break;
                }
            }
            if (canUse != null) {
                CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
                cardCreditManagerModifyReqDTO.setId(Long.valueOf(canUse.getId()));
                cardCreditManagerModifyReqDTO.setAvalibleAmount(canUse.getAvalibleAmount().add(billAmount));
                CardCreditManagerManager.me().modify(cardCreditManagerModifyReqDTO);
            }

            try {
                FinhubLogger.info("退款发送消息  req = {}" , JSON.toJSONString(cardOrderAddReqDTO));
                sendPushAndMsg(transactionAckRpcReqDTO.getBillingAmount().toString(),refundOrderId ,TransactionTypeEnum.REFUND,cardDTO.getEmployeeId(),cardDTO.getCompanyId());
                cardOrderConsumeNoticeManager.sendNoticeMsgForConsume(cardDTO.getFxCardId(),refundOrderId,transactionAckRpcReqDTO.getNetworkTransactionId(),transactionAckRpcReqDTO.getTransactionId(),TransactionTypeEnum.REFUND.getKey());
            }catch (Exception ex){
                FinhubLogger.error("发送push 消息失败 ",ex);
            }
        }
        return true;
    }

    private BigDecimal getCnyTradeAmount(BigDecimal rate, BigDecimal tradeAmount) {
        return rate.multiply(tradeAmount, MathContext.UNLIMITED);
    }

    public void modifyCheckInfo(CardOrderDTO cardOrderDTO,BigDecimal refundTradeAmount, String refundOrderId){
        //查询原始单据是否有核销的情况，如果有，需要调用核销
        if (StringUtils.isNotBlank(cardOrderDTO.getCostId())){
            TradeRefundRpcDTO tradeRefundRpcDTO = new TradeRefundRpcDTO();
            tradeRefundRpcDTO.setOrderId(cardOrderDTO.getBizNo());
            tradeRefundRpcDTO.setRefundOrderId(refundOrderId);
            tradeRefundRpcDTO.setCompanyId(cardOrderDTO.getCompanyId());
            tradeRefundRpcDTO.setType(TransactionTypeEnum.REFUND.getKey());
            log.info("消费退款，调用核销处理，tradeRefundRpcDTO={}", JsonUtils.toJson(tradeRefundRpcDTO));
            iVirtualCardBudgetRpcService.fxTradeRefundAmount(tradeRefundRpcDTO);
            log.info("消费退款，调用核销处理结果成功，tradeRefundRpcDTO={}", JsonUtils.toJson(tradeRefundRpcDTO));

            //重新获取交易记录
            cardOrderDTO = cardOrderService.findByTradeId(cardOrderDTO.getTradeId(),TransactionTypeEnum.CONSUME.getKey());
        }

        CardOrderModifyReqDTO modifyReqDTO = new CardOrderModifyReqDTO();
        modifyReqDTO.setId(Long.valueOf(cardOrderDTO.getId()));
        modifyReqDTO.setUncheckedAmount(cardOrderDTO.getUncheckedAmount().subtract(refundTradeAmount));
        modifyReqDTO.setRefundAmount(cardOrderDTO.getRefundAmount().add(refundTradeAmount));
        if (modifyReqDTO.getUncheckedAmount().compareTo(BigDecimal.ZERO) > 0){
            modifyReqDTO.setCheckStatus(CheckStatusEnum.UNCHECK.getKey());
        } else {
            modifyReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
        }
        cardOrderService.modify(modifyReqDTO);
    }
    public boolean checkIsReject(CardDTO cardDTO,AuthorizationBillingOrder baseOrder){
        //卡状态校验
        boolean isActive = Objects.equals(cardDTO.getCardStatus(), CardStatusEnum.ACTIVE.getStatus());
        if (!isActive){
            return true;
        }

        BigDecimal baseTradeAmount = BigDecimalUtils.yuan2fen(baseOrder.getAmount()).abs();
        BigDecimal balanceOfAfterTrade = cardDTO.getBalance().subtract(baseTradeAmount);
        if (balanceOfAfterTrade.compareTo(BigDecimal.ZERO) < 0){
            return true;
        }
        //查询账户余额
        CompanyAcctRes companyAcctRes = fxPayAcctManager.queryAcct4Petty(cardDTO.getCompanyId(),FxAcctChannelEnum.AIRWALLEX);
        if (companyAcctRes.getStatus() == FxAccountStatusEnum.VALID.getStatus()){
            return false;
        }
        BigDecimal acctBalanceOfAfterTrade = companyAcctRes.getAvailableBalance().subtract(baseTradeAmount);
        return acctBalanceOfAfterTrade.compareTo(BigDecimal.ZERO) < 0;
    }
    public boolean addAuthorizeByAuthorize(AuthorizationRpcReqDTO authorizationRpcReqDTO){

        CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO = new CardAuthorizeAddReqDTO();
        cardAuthorizeAddReqDTO.setCardPlatform(FxAcctChannelEnum.AIRWALLEX.getChannel());
        cardAuthorizeAddReqDTO.setTradeId(authorizationRpcReqDTO.getNetworkTransactionId());
        cardAuthorizeAddReqDTO.setSubTradeId(authorizationRpcReqDTO.getTransactionId());
        cardAuthorizeAddReqDTO.setTradeCurrency(authorizationRpcReqDTO.getTransactionCurrency());
        cardAuthorizeAddReqDTO.setTradeAmount(authorizationRpcReqDTO.getTransactionAmount());
        cardAuthorizeAddReqDTO.setTradeName(authorizationRpcReqDTO.getMerchant().getName());
        cardAuthorizeAddReqDTO.setSourceData(JsonUtils.toJson(authorizationRpcReqDTO));
        //目前看授权状态没什么用
        cardAuthorizeAddReqDTO.setAuthStatus(0);
        cardAuthorizeAddReqDTO.setTradeType(authorizationRpcReqDTO.getTransactionType());
        return cardAuthorizeService.add(cardAuthorizeAddReqDTO);
    }

    public CardOrderAddReqDTO buildCardOrderAddReqDTO(CardDTO cardDTO,TransactionAckRpcReqDTO transactionAckRpcReqDTO){
        try {
            CardOrderAddReqDTO cardOrderAddReqDTO = new CardOrderAddReqDTO();
            cardOrderAddReqDTO.setFxCardId(cardDTO.getFxCardId());
            cardOrderAddReqDTO.setTradeAddress(tradeAddress(transactionAckRpcReqDTO.getMerchant()));
            cardOrderAddReqDTO.setTradeAmount(BigDecimalUtils.yuan2fen(transactionAckRpcReqDTO.getTransactionAmount()).abs());
            cardOrderAddReqDTO.setTradeCurrency(transactionAckRpcReqDTO.getTransactionCurrency());
            cardOrderAddReqDTO.setTradeName(transactionAckRpcReqDTO.getMerchant().getName());
            cardOrderAddReqDTO.setTradeId(transactionAckRpcReqDTO.getNetworkTransactionId());
            cardOrderAddReqDTO.setSubTradeId(transactionAckRpcReqDTO.getTransactionId());
//            cardOrderAddReqDTO.setOriTradeTime(DateTimeUtil.fromUtc(transactionAckRpcReqDTO.getTransactionDate(),"yyyy-MM-dd'T'HH:mm:ss.SSSZ"));
            cardOrderAddReqDTO.setTradeTime(DateTimeUtil.fromUtcToUtcString(transactionAckRpcReqDTO.getTransactionDate()));
//            cardOrderAddReqDTO.setTradeTime(DateTimeUtil.fromUtcToDate(transactionAckRpcReqDTO.getTransactionDate(),"yyyy-MM-dd'T'HH:mm:ss.SSSZ"));
            cardOrderAddReqDTO.setTradeTimeZone("TODO");
            cardOrderAddReqDTO.setType(Objects.requireNonNull(AirwallexTransactionTypeEnum.getEnum(transactionAckRpcReqDTO.getTransactionType())).getTransactionTypeEnum().getKey());
            cardOrderAddReqDTO.setEmployeeId(cardDTO.getEmployeeId());
            cardOrderAddReqDTO.setCompanyId(cardDTO.getCompanyId());
            cardOrderAddReqDTO.setCreateTime(new Date());
            cardOrderAddReqDTO.setBillTradeAmount(BigDecimalUtils.yuan2fen(transactionAckRpcReqDTO.getBillingAmount()).abs());
            cardOrderAddReqDTO.setBillTradeCurrency(transactionAckRpcReqDTO.getBillingCurrency());
            cardOrderAddReqDTO.setMaskedCardNumber(transactionAckRpcReqDTO.getMaskedCardNumber());
            cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
            cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
            cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());

            return cardOrderAddReqDTO;
        }catch (Exception e){
            return new CardOrderAddReqDTO();
        }

    }

    private String tradeAddress(TransactionMerchant transactionMerchant){
        return transactionMerchant.getCountry() + " " +  transactionMerchant.getCity() + " " + transactionMerchant.getName();
    }
    public void addRecordByAuth(AuthorizationRpcReqDTO authorizationRpcReqDTO,CardDTO cardDTO,AuthorizationBillingOrder baseOrder,String bizNo){
        try{
            CardOrderAddReqDTO cardOrderAddReqDTO = new CardOrderAddReqDTO();
            cardOrderAddReqDTO.setFxCardId(cardDTO.getFxCardId());
            cardOrderAddReqDTO.setTradeAddress(tradeAddress(authorizationRpcReqDTO.getMerchant()));
            cardOrderAddReqDTO.setTradeAmount(BigDecimalUtils.yuan2fen(authorizationRpcReqDTO.getTransactionAmount()));
            cardOrderAddReqDTO.setTradeCurrency(authorizationRpcReqDTO.getTransactionCurrency());
            cardOrderAddReqDTO.setTradeName(authorizationRpcReqDTO.getMerchant().getName());
            cardOrderAddReqDTO.setBizNo(bizNo);
//            cardOrderAddReqDTO.setOriTradeTime(DateTimeUtil.fromUtc(authorizationRpcReqDTO.getTransactionDate(),"yyyy-MM-dd'T'HH:mm:ss.SSSZ"));
            cardOrderAddReqDTO.setTradeTime(DateTimeUtil.fromUtcToUtcString(authorizationRpcReqDTO.getTransactionDate()));
//            cardOrderAddReqDTO.setTradeTime(DateTimeUtil.fromUtcToDate(authorizationRpcReqDTO.getTransactionDate(),"yyyy-MM-dd'T'HH:mm:ss.SSSZ"));
            cardOrderAddReqDTO.setTradeTimeZone("TODO");
            cardOrderAddReqDTO.setType(TransactionTypeEnum.PRE_AUTH.getKey());
            cardOrderAddReqDTO.setEmployeeId(cardDTO.getEmployeeId());
            cardOrderAddReqDTO.setCompanyId(cardDTO.getCompanyId());
            cardOrderAddReqDTO.setCreateTime(new Date());
            cardOrderAddReqDTO.setBillTradeAmount(BigDecimalUtils.yuan2fen(baseOrder.getAmount()));
            cardOrderAddReqDTO.setBillTradeCurrency(baseOrder.getCurrency());
            cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
            cardOrderAddReqDTO.setTradeId(authorizationRpcReqDTO.getNetworkTransactionId());
            cardOrderAddReqDTO.setSubTradeId(authorizationRpcReqDTO.getTransactionId());
            cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setRefundAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setNeedNotCheckAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
            cardOrderAddReqDTO.setMaskedCardNumber(authorizationRpcReqDTO.getMaskedCardNumber());
            cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
            cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());
            cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
            //交易币种兑人民币汇率计算，以及人民币金额计算
            currencyExchangeRate(cardOrderAddReqDTO);
            cardOrderService.add(cardOrderAddReqDTO);
        }catch (Exception e){
            FinhubLogger.error("订单生成异常",e);
        }

    }

    private BigDecimal queryCurrencyExchangeRate() {
        try{
            ExchangeRateReq rateReq = new ExchangeRateReq();
            rateReq.setExchangeType(ExchangeType.USD_CNY);
            log.info("美元兑换人民币汇率获取参数 rateReq={}", JsonUtils.toJson(rateReq));
            ResponseVo<ExchangeRateRes> usdToCnyRate = iExchangeRateService.queryExchangeRate(rateReq);
            log.info("美元兑换人民币汇率获取结果usdToCnyRate={}", JsonUtils.toJson(usdToCnyRate));
            if (Objects.isNull(usdToCnyRate)){
                log.error("美元兑换人名币汇率获取为空");
                return null;
            }
            boolean success = usdToCnyRate.isSuccess();
            if(!success){
                log.error("美元兑换人名币汇率获取为空失败");
                return null;
            }
            ExchangeRateRes data = usdToCnyRate.getData();
            return data.getRate();
        } catch (Exception e){
            log.error("消费交易币种兑换人名币计算报错", e);
            return null;
        }
    }
    /**
     * 美元兑换人民币汇率计算
     */
    private void currencyExchangeRate(CardOrderAddReqDTO cardOrderAddReqDTO) {
        try{
            BigDecimal currencyRate = queryCurrencyExchangeRate();
            if (currencyRate == null){
                return;
            }
            BigDecimal tradeAmount = cardOrderAddReqDTO.getTradeAmount();
            BigDecimal billTradeAmount = cardOrderAddReqDTO.getBillTradeAmount();
            String tradeCurrency = cardOrderAddReqDTO.getTradeCurrency();
            if (Objects.equals(tradeCurrency, CurrencyEnum.CNY.getCurrencyCode())){
                //若交易币种为CNY，则人民币金额同交易金额
                cardOrderAddReqDTO.setTradeCnyExchangeRate(new BigDecimal("1.0000"));
                cardOrderAddReqDTO.setBillTradeCnyExchangeRate(currencyRate);
                cardOrderAddReqDTO.setCnyTradeAmount(tradeAmount);
            } else{
                //若交易币种为其他币种，则人民币金额为：结算币种金额*美元折人民币汇率
                BigDecimal tradeRate = tradeCnyExchangeRateCal(billTradeAmount, tradeAmount,currencyRate);
                cardOrderAddReqDTO.setTradeCnyExchangeRate(tradeRate);
                cardOrderAddReqDTO.setBillTradeCnyExchangeRate(currencyRate);
                BigDecimal amount = getCnyTradeAmount(currencyRate, billTradeAmount);
                cardOrderAddReqDTO.setCnyTradeAmount(amount);
            }
        } catch (Exception e){
            log.error("消费交易币种兑换人名币计算报错", e);
        }
    }

    private BigDecimal tradeCnyExchangeRateCal(BigDecimal billTradeAmount, BigDecimal tradeAmount, BigDecimal usdToCnyRate) {
        BigDecimal divide = billTradeAmount.divide(tradeAmount, 8, RoundingMode.HALF_UP);
        return usdToCnyRate.multiply(divide, MathContext.UNLIMITED);
    }

    public BankCardFlowAddReqDTO buildBankCardFlowAddReqDTO(String bizNo,Integer cardModel,CardDTO cardDTO){
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = new BankCardFlowAddReqDTO();
        bankCardFlowAddReqDTO.setId(IdUtils.getId());
        bankCardFlowAddReqDTO.setFxCardId(cardDTO.getFxCardId());
        bankCardFlowAddReqDTO.setEmployeeId(cardDTO.getEmployeeId());
        bankCardFlowAddReqDTO.setCompanyId(cardDTO.getCompanyId());
        bankCardFlowAddReqDTO.setBizNo(bizNo);
        bankCardFlowAddReqDTO.setCardModel(cardModel);
        bankCardFlowAddReqDTO.setCreateTime(new Date());
        bankCardFlowAddReqDTO.setUpdateTime(new Date());
        bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
        return bankCardFlowAddReqDTO;
    }

    public void sendPushAndMsg(String transAmount,String orderId ,TransactionTypeEnum transactionTypeEnum , String receiverId ,String companyId) {
        log.info("发送push 消息 orderId  = {}  transactionTypeEnum = {} , receiverId = {} ,companyId = {}",orderId ,transactionTypeEnum.getValue() ,receiverId ,companyId);
        if (StringUtils.isNotBlank(receiverId)) {
            // 替换消息内容
            String content = "您有一笔${0}的{1}";
            String text = MessageFormat.format(content, transAmount ,transactionTypeEnum.getValue() );
            // APP push推送 / OA消息推送
            try {
                messageService.pushAlertMsg(buildAppAndOaPush(orderId,transactionTypeEnum, text, receiverId));
            } catch (IOException e) {
                log.error("APP push推送 / OA消息推送异常", e);
            }
            // 批量发送-APP消息中心
            KafkaProducer.me().publish(buildAppMsg(companyId,orderId,transactionTypeEnum, text, receiverId));
            // 批量发送-企业web消息中心
            KafkaProducer.me().publish(buildWebMsg(companyId,orderId,transactionTypeEnum, text, receiverId));
        }
    }

    private PushAlertDto buildAppAndOaPush(String orderId , TransactionTypeEnum transactionTypeEnum , String content, String receiver) {
        PushAlertDto push = new PushAlertDto();
        push.setMsgType("81");
        push.setTitle(TITLE + transactionTypeEnum.getValue());
        push.setContent(content);
        push.setDesc(content);
        push.setUserId(receiver);
        // 自定义参数 提供路由跳转参数
        Map<String, Object> map = Maps.newHashMap();
        map.put("order_id", orderId);
        map.put("order_type", transactionTypeEnum.getKey());
        push.setMsgObj(map);
        return push;
    }

    private KafkaSaasMessageMsg buildAppMsg(String companyId , String orderId , TransactionTypeEnum transactionTypeEnum , String content, String receiver) {
        log.info("海外卡消费发送消息，参数:{}, 类型：{},状态：{}", orderId,transactionTypeEnum.getValue(), content);

        String titleName = TITLE + transactionTypeEnum.getValue();

        KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
        kafkaSaasMessageMsg.setBizType(81);
        kafkaSaasMessageMsg.setComment(content);
        kafkaSaasMessageMsg.setTitle(titleName);
        kafkaSaasMessageMsg.setCompanyId(companyId);
        kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
        kafkaSaasMessageMsg.setSenderType(SenderType.HL.getCode());
        kafkaSaasMessageMsg.setReceiver(receiver);
        kafkaSaasMessageMsg.setSender(receiver);
        kafkaSaasMessageMsg.setBizOrder(orderId);
        kafkaSaasMessageMsg.setInfo(getInfo(orderId ,transactionTypeEnum));
        log.info("海外卡消费发送消息，企业编号" + companyId + "," + JsonUtils.toJson(kafkaSaasMessageMsg));
        return kafkaSaasMessageMsg;
    }

    private KafkaWebMessageMsg buildWebMsg(String companyId , String orderId , TransactionTypeEnum transactionTypeEnum , String content, String receiver) {
        log.info("海外卡消费发送消息，参数:{}, 类型：{},状态：{}", orderId,transactionTypeEnum.getValue(), content);
        String titleName = TITLE + transactionTypeEnum.getValue();

        KafkaWebMessageMsg kafkaSaasMessageMsg = new KafkaWebMessageMsg();
        kafkaSaasMessageMsg.setBizType(BizType.AllConsume.getCode());
        kafkaSaasMessageMsg.setComment(content);
        kafkaSaasMessageMsg.setTitle(titleName);
        kafkaSaasMessageMsg.setCompanyId(companyId);
        kafkaSaasMessageMsg.setMsgType(MessageType.Consume.getCode());
        kafkaSaasMessageMsg.setSenderType(SenderType.HL.getCode());
        kafkaSaasMessageMsg.setReceiver(receiver);
        kafkaSaasMessageMsg.setSender(receiver);
        kafkaSaasMessageMsg.setBizOrder(orderId);
        kafkaSaasMessageMsg.setMsgSubType(MessageSubType.LARGE_OVER.getCode());
        log.info("海外卡消费发送消息，企业编号" + companyId + "," + JsonUtils.toJson(kafkaSaasMessageMsg));
        return kafkaSaasMessageMsg;
    }

    private String getInfo(String orderId ,TransactionTypeEnum transactionTypeEnum) {
        JSONObject json = new JSONObject();
        json.put("generate_time", DateUtils.format(new Date(), DateUtils.FORMAT_DATE_TIME_PATTERN));
        json.put("apply_type", transactionTypeEnum.getKey());
        json.put("apply_id", orderId);
        json.put("data_type" , transactionTypeEnum.getKey());
        return json.toJSONString();
    }



}
