package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.ICardCreditManagerService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.ApplyOrderTypeEnum;
import com.fenbei.fx.card.common.enums.CardPlatformCaseEnum;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.service.cardcreditmanager.dto.*;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderListReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderListResDTO;
import com.fenbei.fx.card.service.cardorder.manager.CardOrderManager;
import com.fenbei.fx.card.service.remote.dto.BudgetCostAttributionDTO;
import com.fenbei.fx.card.util.*;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.fenbei.fx.card.common.enums.TransactionTypeEnum.getBankTradeEnumValues;

/**
 * 额度管理
 * <AUTHOR>
 */
@Slf4j
@Validated
@DubboService
public class CardCreditManagerServiceRpcImpl implements ICardCreditManagerService {

    /**
     * 海外卡额度申请
     * @param cardCreditApplyRpcReqDTO 额度申请参数
     * @return CardCreditApplyRpcRespDTO
     */
    @Override
    public CardCreditApplyRpcRespDTO apply(CardCreditApplyRpcReqDTO cardCreditApplyRpcReqDTO) {
        FinhubLogger.info("海外卡额度申请：{}",JsonUtils.toJson(cardCreditApplyRpcReqDTO));
        CardCreditManagerApplyRespDTO cardCreditManagerApplyRespDTO = null;
        try {
            cardCreditManagerApplyRespDTO = CardCreditManagerManager.me().apply(converterCardCreditManagerApplyReqDTO(cardCreditApplyRpcReqDTO));
        } catch (FxCardException fxCardException) {
            throw fxCardException;
        }catch (Exception e) {
           throw  e;
        }
        return convertToCardCreditApplyRpcRespDTO(cardCreditManagerApplyRespDTO);
    }

    /**
     * 海外卡额度退回
     * @param cardCreditReturnRpcReqDTO 额度退回参数
     * @return CardCreditReturnRpcRespDTO
     */
    @Override
    public CardCreditReturnRpcRespDTO refund(CardCreditReturnRpcReqDTO cardCreditReturnRpcReqDTO){
        CardCreditManagerReturnRespDTO cardCreditManagerReturnRespDTO =  CardCreditManagerManager.me().refund(convertToCardCreditManagerReturnReqDTO(cardCreditReturnRpcReqDTO));
        return convertToCardCreditReturnRpcRespDTO(cardCreditManagerReturnRespDTO);
    }

    /**
     * 构建额度退还响应
     * @param cardCreditManagerReturnRespDTO 额度退还请求
     * @return CardCreditReturnRpcRespDTO
     */
    public CardCreditReturnRpcRespDTO convertToCardCreditReturnRpcRespDTO(CardCreditManagerReturnRespDTO cardCreditManagerReturnRespDTO){
        CardCreditReturnRpcRespDTO cardCreditReturnRpcRespDTO = new CardCreditReturnRpcRespDTO();
        cardCreditReturnRpcRespDTO.setCardBalance(cardCreditManagerReturnRespDTO.getCardBalance());
        cardCreditReturnRpcRespDTO.setCardStatus(cardCreditManagerReturnRespDTO.getCardStatus());
        cardCreditReturnRpcRespDTO.setFxCardId(cardCreditManagerReturnRespDTO.getFxCardId());
        cardCreditReturnRpcRespDTO.setEmployeeId(cardCreditManagerReturnRespDTO.getEmployeeId());
        cardCreditReturnRpcRespDTO.setCompanyId(cardCreditManagerReturnRespDTO.getCompanyId());
        cardCreditReturnRpcRespDTO.setApplyTransNo(cardCreditManagerReturnRespDTO.getApplyTransNo());
        cardCreditReturnRpcRespDTO.setRefundAmount(cardCreditManagerReturnRespDTO.getRefundAmount());
        cardCreditReturnRpcRespDTO.setBizNo(cardCreditManagerReturnRespDTO.getBizNo());
        return cardCreditReturnRpcRespDTO;
    }

    /**
     * 额度退还请求
     * @param cardCreditReturnRpcReqDTO 额度退还请求
     * @return CardCreditManagerReturnReqDTO
     */
    public CardCreditManagerReturnReqDTO convertToCardCreditManagerReturnReqDTO(CardCreditReturnRpcReqDTO cardCreditReturnRpcReqDTO){
        CardCreditManagerReturnReqDTO cardCreditManagerReturnReqDTO = new CardCreditManagerReturnReqDTO();
        cardCreditManagerReturnReqDTO.setReturnAmount(cardCreditReturnRpcReqDTO.getReturnAmount());
        cardCreditManagerReturnReqDTO.setApplyReasonDesc(cardCreditReturnRpcReqDTO.getApplyReasonDesc());
        cardCreditManagerReturnReqDTO.setDismissionEmployee(cardCreditReturnRpcReqDTO.isDismissionEmployee());
        cardCreditManagerReturnReqDTO.setApplyReason(cardCreditReturnRpcReqDTO.getApplyReason());
        cardCreditManagerReturnReqDTO.setEmployeeId(cardCreditReturnRpcReqDTO.getEmployeeId());
        cardCreditManagerReturnReqDTO.setCompanyId(cardCreditReturnRpcReqDTO.getCompanyId());
        cardCreditManagerReturnReqDTO.setFxCardId(cardCreditReturnRpcReqDTO.getFxCardId());
        Map<String, BigDecimal>  map = cardCreditReturnRpcReqDTO.getReturnAmount();
        BigDecimal refundCreditAmount = BigDecimal.ZERO;
        for (String key:map.keySet()){
            refundCreditAmount = refundCreditAmount.add(map.get(key));
        }
        cardCreditManagerReturnReqDTO.setRefundCreditAmount(refundCreditAmount);
        return cardCreditManagerReturnReqDTO;
    }

    @Override
    public List<FxBankCardDistributeCreditRespDTO> distributeDetailList(FxCreditDistributeQueryReqDTO reqDTO) {
        //查询未核销的备用金
        CardCreditManagerListReqDTO cardCreditManagerListReqDTO = new CardCreditManagerListReqDTO();
        cardCreditManagerListReqDTO.setBizNo(reqDTO.getSaasApplyNo());
        cardCreditManagerListReqDTO.setApplyType(1);
        List<CardCreditManagerListResDTO> creditManagerListResDTOS = CardCreditManagerManager.me().list(cardCreditManagerListReqDTO);
        return converter(creditManagerListResDTOS);
    }

    @Override
    public List<ApplyCreditApplyAppRpcVO> queryUncheckApply(UncheckCardCreditRpcReqDTO rpcReqDTO) {
        log.info("获取未核销的申请单请求，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO));
        if (StringUtils.isBlank(rpcReqDTO.getEmployeeId())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        try{
            List<ApplyCreditApplyAppRpcVO> list = new ArrayList<>();
            List<CardCreditManagerDTO> cardCreditManagerDTOS =  new ArrayList<>();
            if(ObjectUtils.isNotEmpty(rpcReqDTO.getOrderIds())){
                String orderId = rpcReqDTO.getOrderIds().get(0);
                CardOrderListReqDTO cardOrderListReqDTO = new CardOrderListReqDTO();
                cardOrderListReqDTO.setBizNo(orderId);
                List<CardOrderListResDTO> cardOrderListResDTOList = CardOrderService.me().list(cardOrderListReqDTO);
                if(ObjectUtils.isNotEmpty(cardOrderListResDTOList)){
                    cardCreditManagerDTOS = CardCreditManagerManager.me().queryUncheckApplyByEmployeeIdAndPlat(rpcReqDTO.getEmployeeId(),cardOrderListResDTOList.get(0).getCardPlatform());
                    if (CollectionUtils.isEmpty(cardCreditManagerDTOS)){
                        log.info("获取未核销的申请单请求结果不存在，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO));
                        return list;
                    }

                }else{
                    log.info("获取未核销的申请单请求结果不存在，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO));
                    return list;
                }
            }else{
                log.info("获取未核销的申请单请求结果不存在，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO));
                return list;
            }


            log.info("获取未核销的申请单请求结果，rpcReqDTO={}，size={}", JsonUtils.toJson(rpcReqDTO), cardCreditManagerDTOS.size());

            cardCreditManagerDTOS.forEach(p->{
                ApplyCreditApplyAppRpcVO apply = CopyUtils.convert(p, ApplyCreditApplyAppRpcVO.class);
                apply.setApplyTransNo(p.getApplyTransNo());
                apply.setApplyTransBatchNo(p.getApplyTransBatchNo());
                apply.setApplyReason(p.getApplyReason());
                apply.setApplyId(p.getBizNo());
                apply.setOperationAmount(BigDecimal.ZERO);
                apply.setApplyAmount(BigDecimalUtils.fen2yuan(p.getAmount()));
                apply.setUseBalance(BigDecimalUtils.fen2yuan(p.getUncheckedAmount()));
                CurrencyEnum currencyEnum = CardPlatformCaseEnum.getEnumByCardPlatformCode(p.getCardPlatform()).getCurrencyEnum();
                apply.setApplyAmountShow(CurrencyNumberFormatUtil.moneyFormart(currencyEnum,BigDecimalUtils.fen2yuan(p.getAmount())));
                apply.setUseBalanceShow(CurrencyNumberFormatUtil.moneyFormart(currencyEnum,BigDecimalUtils.fen2yuan(p.getUncheckedAmount())));
                apply.setApplyTime(DateFormatUtil.tradeDateFormat(p.getCreateTime()));
                apply.setCardPlatform(p.getCardPlatform());
                apply.setCurrency(StringUtils.isNotBlank(p.getCurrency())?p.getCurrency():currencyEnum.getCurrencyCode());
                apply.setFxCardId(p.getFxCardId());
                apply.setApplyOrderType(p.getApplyOrderType());
                apply.setApplyOrderId(p.getOriApplyTransNo());
                list.add(apply);
            });

            return list;
        } catch (FinhubException e){
            log.warn("获取未核销的申请单请求异常，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("获取未核销的申请单请求报错，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }

    @Override
    public UncheckCreditApplyAndStatisticRpcVO queryUncheckApplyAndStatistic(UncheckCardCreditRpcReqDTO rpcReqDTO) {
        log.info("获取未核销的申请单和统计请求，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO));
        try{
            List<ApplyCreditApplyAppRpcVO> applyCreditApplyAppRpcVOS = queryUncheckApply(rpcReqDTO);

            UncheckCreditApplyAndStatisticRpcVO rpcVO = new UncheckCreditApplyAndStatisticRpcVO();
            if (CollectionUtils.isNotEmpty(applyCreditApplyAppRpcVOS)){
                rpcVO.setDataList(applyCreditApplyAppRpcVOS);
                rpcVO.setTotalCount(applyCreditApplyAppRpcVOS.size());
                rpcVO.setCurrency(applyCreditApplyAppRpcVOS.get(0).getCurrency());
                rpcVO.setCurrencySymbol(CurrencyEnum.getCurrencyByCodeIgnoreCase(applyCreditApplyAppRpcVOS.get(0).getCurrency()).getSymbol());
            }

            List<String> orderIds = rpcReqDTO.getOrderIds();
            log.info("获取待核销订单请求参数，orderIds={}", orderIds);
            if (CollectionUtils.isEmpty(orderIds)){
                return rpcVO;
            }
            List<CardOrderDTO> orderInfos = CardOrderManager.me().getOrderByOrderIds(getBankTradeEnumValues(),orderIds);
            if(CollectionUtils.isNotEmpty(orderInfos)){
                BigDecimal reduceConsume = orderInfos.stream().filter(p -> Objects.equals(TransactionTypeEnum.CONSUME.getKey(), p.getType()))
                    .map(CardOrderDTO::getBillTradeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal reduceRefund = orderInfos.stream().filter(p -> Objects.equals(TransactionTypeEnum.REFUND.getKey(), p.getType()))
                    .map(CardOrderDTO::getBillTradeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal toCheck = reduceConsume.subtract(reduceRefund);
                if(BigDecimalUtils.hasNoPrice(toCheck)){
                    toCheck=BigDecimal.ZERO;
                }
                rpcVO.setConditionExt(BigDecimalUtils.fen2yuan(toCheck));

                //计算每个申请单本次关联，从上到下分配
                handleApplyOperationAmount(applyCreditApplyAppRpcVOS, rpcVO.getConditionExt());
            }

            return rpcVO;
        } catch (FinhubException e){
            log.warn("获取未核销的申请单统计请求异常，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("获取未核销的申请单统计请求报错，rpcReqDTO={}", JsonUtils.toJson(rpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }

    @Override
    public List<ApplyCreditApplyVoucherRpcVO> applyList(String companyId, String bizNo) {
        log.info("获取申请单详情，bizNo={}", bizNo);
        List<ApplyCreditApplyVoucherRpcVO> res = Lists.newArrayList();
        List<CardCreditManagerDTO> applyList = CardCreditManagerManager.me().queryExistedApply(bizNo);
        if(CollectionUtils.isNotEmpty(applyList)){
            ApplyCreditApplyVoucherRpcVO applyInfo = CopyUtils.convert(applyList.get(0), ApplyCreditApplyVoucherRpcVO.class);
            res.add(applyInfo);
        }
        return res;
    }

    private void handleApplyOperationAmount(List<ApplyCreditApplyAppRpcVO> applyCreditApplyAppRpcVOS, BigDecimal conditionExt) {
        for(int i = 0; i< applyCreditApplyAppRpcVOS.size(); i++){
            ApplyCreditApplyAppRpcVO p = applyCreditApplyAppRpcVOS.get(i);
            BigDecimal useBalance = p.getUseBalance();
            if (BigDecimalUtils.hasPrice(conditionExt)){
                if (conditionExt.compareTo(useBalance) <= 0){
                    p.setOperationAmount(conditionExt);
                    break;
                } else {
                    p.setOperationAmount(useBalance);
                    conditionExt = conditionExt.subtract(useBalance);
                }
            }
        }
    }

    public List<FxBankCardDistributeCreditRespDTO> converter(List<CardCreditManagerListResDTO> creditManagerListResDTOS){
        List<FxBankCardDistributeCreditRespDTO> fxBankCardDistributeCreditRespDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(creditManagerListResDTOS)){
            for (CardCreditManagerListResDTO cardCreditManagerListResDTO:creditManagerListResDTOS) {
                FxBankCardDistributeCreditRespDTO fxBankCardDistributeCreditRespDTO = new FxBankCardDistributeCreditRespDTO();
                fxBankCardDistributeCreditRespDTO.setBankName(cardCreditManagerListResDTO.getCardPlatform());
                fxBankCardDistributeCreditRespDTO.setBankAccountNo(cardCreditManagerListResDTO.getBankCardNo());
                fxBankCardDistributeCreditRespDTO.setDistributeAmount(cardCreditManagerListResDTO.getAmount());
                fxBankCardDistributeCreditRespDTO.setDistributeStatus(cardCreditManagerListResDTO.getApplyStatus());
//                fxBankCardDistributeCreditRespDTO.setDistributeOrderNo();
                fxBankCardDistributeCreditRespDTO.setFxCardId(cardCreditManagerListResDTO.getFxCardId());
//                fxBankCardDistributeCreditRespDTO.setFailureReason(cardCreditManagerListResDTO.getApplyReason());
//                fxBankCardDistributeCreditRespDTO.setFailureReasonCode();
                fxBankCardDistributeCreditRespDTO.setCompanyId(cardCreditManagerListResDTO.getCompanyId());
                fxBankCardDistributeCreditRespDTO.setApplyTransNo(cardCreditManagerListResDTO.getApplyTransNo());
                fxBankCardDistributeCreditRespDTO.setEmployeeId(cardCreditManagerListResDTO.getEmployeeId());
                fxBankCardDistributeCreditRespDTOS.add(fxBankCardDistributeCreditRespDTO);
            }
        }
        return fxBankCardDistributeCreditRespDTOS;
    }

    public CardCreditManagerApplyReqDTO converterCardCreditManagerApplyReqDTO(CardCreditApplyRpcReqDTO cardCreditApplyRpcReqDTO){
        CardCreditManagerApplyReqDTO cardCreditManagerApplyReqDTO = new CardCreditManagerApplyReqDTO();
        cardCreditManagerApplyReqDTO.setApplyCreditAmount(cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        cardCreditManagerApplyReqDTO.setApplyReason(cardCreditApplyRpcReqDTO.getApplyReason());
        cardCreditManagerApplyReqDTO.setApplyReasonDesc(cardCreditApplyRpcReqDTO.getApplyReasonDesc());
        cardCreditManagerApplyReqDTO.setApplyAppendBatchId(cardCreditApplyRpcReqDTO.getApplyAppendBatchId());
        cardCreditManagerApplyReqDTO.setAppendFlag(cardCreditApplyRpcReqDTO.getAppendFlag());
        List<CostAttribution> costAttributions = cardCreditApplyRpcReqDTO.getAttributions();
        if (CollectionUtils.isNotEmpty(costAttributions)) {
            List<BudgetCostAttributionDTO> attributions = new ArrayList<>(costAttributions.size());
            for (CostAttribution costAttribution:costAttributions){
                BudgetCostAttributionDTO budgetCostAttributionDTO = new BudgetCostAttributionDTO();
                budgetCostAttributionDTO.setCost_attribution_category(costAttribution.getCostAttributionType());
                budgetCostAttributionDTO.setCost_attribution_name(costAttribution.getCostAttributionName());
                budgetCostAttributionDTO.setCost_attribution_id(costAttribution.getCostAttributionId());
                attributions.add(budgetCostAttributionDTO);
            }
            cardCreditManagerApplyReqDTO.setAttributions(attributions);
        }
        cardCreditManagerApplyReqDTO.setBudgetOpt(cardCreditApplyRpcReqDTO.getBudgetOpt());
        cardCreditManagerApplyReqDTO.setBankName(cardCreditApplyRpcReqDTO.getBankName());
        cardCreditManagerApplyReqDTO.setCurrency(cardCreditApplyRpcReqDTO.getCurrency());
        cardCreditManagerApplyReqDTO.setCostAttributionName(cardCreditApplyRpcReqDTO.getCostTypeDesc());
        cardCreditManagerApplyReqDTO.setCostAttributionId(cardCreditApplyRpcReqDTO.getCostType());
        cardCreditManagerApplyReqDTO.setCostAttributionType(cardCreditApplyRpcReqDTO.getCostAttributionType());
        cardCreditManagerApplyReqDTO.setCostAttributionOpt(cardCreditApplyRpcReqDTO.getCostAttributionOpt());
        cardCreditManagerApplyReqDTO.setCompanyId(cardCreditApplyRpcReqDTO.getCompanyId());
        cardCreditManagerApplyReqDTO.setDeductionMode(cardCreditApplyRpcReqDTO.getDeductionMode());
        cardCreditManagerApplyReqDTO.setExpireDate(cardCreditApplyRpcReqDTO.getExpireDate());
        cardCreditManagerApplyReqDTO.setFxCardId(cardCreditApplyRpcReqDTO.getFxCardId());
        cardCreditManagerApplyReqDTO.setPettyId(cardCreditApplyRpcReqDTO.getPettyId());
        cardCreditManagerApplyReqDTO.setPettyType(cardCreditApplyRpcReqDTO.getPettyType());
        cardCreditManagerApplyReqDTO.setPettyName(cardCreditApplyRpcReqDTO.getPettyName());
        cardCreditManagerApplyReqDTO.setSaasApplyNo(cardCreditApplyRpcReqDTO.getSaasApplyNo());
        cardCreditManagerApplyReqDTO.setSaasApplyMeaningNo(cardCreditApplyRpcReqDTO.getSaasApplyMeaningNo());
        cardCreditManagerApplyReqDTO.setUsdCnyExchangeRate(cardCreditApplyRpcReqDTO.getUsdCnyExchangeRate());
        cardCreditManagerApplyReqDTO.setCnyAmount(cardCreditApplyRpcReqDTO.getCnyAmount());
        cardCreditManagerApplyReqDTO.setApplyOrderType(ApplyOrderTypeEnum.VIRTUAL_CARD.getTypeReal());
        return cardCreditManagerApplyReqDTO;
    }

    public CardCreditApplyRpcRespDTO convertToCardCreditApplyRpcRespDTO(CardCreditManagerApplyRespDTO cardCreditManagerApplyRespDTO){
        CardCreditApplyRpcRespDTO cardCreditApplyRpcRespDTO = new CardCreditApplyRpcRespDTO();
        cardCreditApplyRpcRespDTO.setCardBalance(cardCreditManagerApplyRespDTO.getCardBalance());
        cardCreditApplyRpcRespDTO.setFxCardId(cardCreditManagerApplyRespDTO.getFxCardId());
        cardCreditApplyRpcRespDTO.setBizNo(cardCreditManagerApplyRespDTO.getBizNo());
        cardCreditApplyRpcRespDTO.setApplyTransNo(cardCreditManagerApplyRespDTO.getApplyTransNo());
        cardCreditApplyRpcRespDTO.setEmployeeId(cardCreditManagerApplyRespDTO.getEmployeeId());
        cardCreditApplyRpcRespDTO.setCompanyId(cardCreditManagerApplyRespDTO.getCompanyId());
        cardCreditApplyRpcRespDTO.setApplyStatus(cardCreditManagerApplyRespDTO.getApplyStatus());
        cardCreditApplyRpcRespDTO.setFailedReason(cardCreditManagerApplyRespDTO.getFailedReason());
        return cardCreditApplyRpcRespDTO;
    }
}
