package com.fenbei.fx.card.rpc.card;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fenbei.fx.card.api.card.ILianLianTradeService;
import com.fenbei.fx.card.api.card.dto.lianlian.*;
import com.fenbei.fx.card.api.card.enums.LianLianTopicEnum;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.common.kafka.KafkaProducer;
import com.fenbei.fx.card.constants.RedisKeyConstant;
import com.fenbei.fx.card.service.bankcardflow.BankCardFlowService;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.CardModifyReqDTO;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbei.fx.card.service.cardauthorize.CardAuthorizeService;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.cardmodelconfig.dto.EmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardmodelconfig.impl.CardModelConfigServiceImpl;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.*;
import com.fenbei.fx.card.service.cardorder.manager.CardOrderConsumeNoticeManager;
import com.fenbei.fx.card.service.cardorder.manager.CardOrderLargeOverNoticeManager;
import com.fenbei.fx.card.service.redis.RedissonService;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.service.webservice.dto.PushAlertDto;
import com.fenbei.fx.card.util.BizIdUtils;
import com.fenbei.fx.card.util.DateTimeUtil;
import com.fenbei.fx.card.util.IdUtils;
import com.fenbeitong.expense.management.api.virtual.IVirtualCardBudgetRpcService;
import com.fenbeitong.expense.management.api.virtual.dto.TradeRefundRpcDTO;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@DubboService
public class LianLianTradeServiceImpl implements ILianLianTradeService {
    @Autowired
    CardAuthorizeService cardAuthorizeService;
    @Autowired
    CardService cardService;
    @Autowired
    CardOrderService cardOrderService;
    @Autowired
    CardModelConfigServiceImpl cardModelConfigService;
    @Autowired
    BankCardFlowService bankCardFlowService;
    @Autowired
    public MessageService messageService;
    @Autowired
    private RedissonService redissonService;
    @Autowired
    private CardApplyManager cardApplyManager;
    @DubboReference
    private IVirtualCardBudgetRpcService iVirtualCardBudgetRpcService;
    @Autowired
    private CardOrderConsumeNoticeManager cardOrderConsumeNoticeManager;
    @Autowired
    private CardOrderLargeOverNoticeManager cardOrderLargeOverNoticeManager;

    /**
     * webhook
     * @param tradeTopic 通知接口名
     * @param data 数据
     * @return
     */
    @Override
    public boolean webhook(String tradeTopic, String data) {
        FinhubLogger.info("tradeTopic:{},data:{}",tradeTopic,data);
        if (LianLianTopicEnum.CARD_V1_APPLY_RESULT.getTopicKey().equals(tradeTopic)){
            LianLianWebhookDTO<String> notice = JSON.parseObject(data, new TypeReference<LianLianWebhookDTO<String>>() {});
            LianLianCardApplyResultDTO lianLianCardApplyResultDTO = JSON.parseObject(notice.getData(), LianLianCardApplyResultDTO.class);
            if ("FAIL".equals(lianLianCardApplyResultDTO.getApplyStatus())) {
                cardApplyManager.updateCardApply(lianLianCardApplyResultDTO.getAccountNo(),lianLianCardApplyResultDTO.getFailReason());
            }
            return true;
        }
        if (LianLianTopicEnum.PRE_AUTH.getTopicKey().equals(tradeTopic)) {
            LianLianWebhookDTO<String> notice = JSON.parseObject(data, new TypeReference<LianLianWebhookDTO<String>>() {});
            LianLianOrderV1StatusDTO lianLianOrderPreAuthDTO = JSON.parseObject(notice.getData(), LianLianOrderV1StatusDTO.class);
            return auth(lianLianOrderPreAuthDTO);
        } else if (LianLianTopicEnum.SETTLEMENT.getTopicKey().equals(tradeTopic)) {
            LianLianWebhookDTO<String> notice = JSON.parseObject(data, new TypeReference<LianLianWebhookDTO<String>>() {});
            LianLianOrderV1SettleSuccessDTO lianLianOrderV1SettleSuccessDTO = JSON.parseObject(notice.getData(), LianLianOrderV1SettleSuccessDTO.class);
            return settlement(lianLianOrderV1SettleSuccessDTO);
        } else if (LianLianTopicEnum.REFUND.getTopicKey().equals(tradeTopic)) {
            LianLianWebhookDTO<String> notice = JSON.parseObject(data, new TypeReference<LianLianWebhookDTO<String>>() {});
            LianLianOrderV1RefundDTO lianLianOrderV1RefundDTO = JSON.parseObject(notice.getData(), LianLianOrderV1RefundDTO.class);
            return refund(lianLianOrderV1RefundDTO);
        } else if (LianLianTopicEnum.REVERSE.getTopicKey().equals(tradeTopic)) {
            LianLianWebhookDTO<String> notice = JSON.parseObject(data, new TypeReference<LianLianWebhookDTO<String>>() {});
            LianLianOrderV1ReverseDTO lianLianOrderV1ReverseDTO = JSON.parseObject(notice.getData(), LianLianOrderV1ReverseDTO.class);
            //开发人员应该注意，部分情况下因为网络原因，会导致交易撤销通知先到，交易结果通知后到的情况，开发人员应当对该情况做特殊处理。
            return reversal(lianLianOrderV1ReverseDTO);
        } else {
            FinhubLogger.warn("未知交易:{},{}", tradeTopic, data);
            return true;
        }

    }

    /**
     * 交易结果通知: 授权
     * order.v1.status
     */
    public boolean auth(LianLianOrderV1StatusDTO lianLianOrderV1StatusDTO) {
        //1. 检测是否处理
        String payScene = lianLianOrderV1StatusDTO.getPayScene();
        if (!"VCC".equals(payScene)) {
            FinhubLogger.error("非合作范围内交易类型,请关注:{},{}", payScene, lianLianOrderV1StatusDTO.getOrderNo());
            return true;
        }
        String orderStatus = lianLianOrderV1StatusDTO.getOrderStatus();
        if (!"SUCCESS".equals(orderStatus)) {
            return true;
        }
        //交易金额
        BigDecimal orderAmount = new BigDecimal(lianLianOrderV1StatusDTO.getOrderAmount());
        BigDecimal tradeAmountFen = BigDecimalUtils.yuan2fen(orderAmount);
        //结算金额
        BigDecimal settleAmount = new BigDecimal(lianLianOrderV1StatusDTO.getSettleAmount());
        BigDecimal settleAmountFen = BigDecimalUtils.yuan2fen(settleAmount);

        CardAuthorizeDTO cardAuthorizeDTO = cardAuthorizeService.findByTradeId(lianLianOrderV1StatusDTO.getOrderNo(), "AUTHORIZATION");
        if (cardAuthorizeDTO == null) {
            CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO = new CardAuthorizeAddReqDTO();
            cardAuthorizeAddReqDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
            cardAuthorizeAddReqDTO.setTradeId(lianLianOrderV1StatusDTO.getOrderNo());
            cardAuthorizeAddReqDTO.setTradeCurrency(lianLianOrderV1StatusDTO.getOrderCurrency());
            cardAuthorizeAddReqDTO.setTradeAmount(tradeAmountFen);
            LianLianOrderV1PayeeMerInfo lianLianOrderV1PayeeMerInfo = lianLianOrderV1StatusDTO.getPayeeMerInfo();
            if (lianLianOrderV1PayeeMerInfo == null || lianLianOrderV1PayeeMerInfo.getPayeeMerName() == null) {
                cardAuthorizeAddReqDTO.setTradeName("未知商家");
                cardAuthorizeAddReqDTO.setTradeAddress("未知地址");
            } else {
                cardAuthorizeAddReqDTO.setTradeName(lianLianOrderV1PayeeMerInfo.getPayeeMerName());
                cardAuthorizeAddReqDTO.setTradeAddress(lianLianOrderV1PayeeMerInfo.getPayeeMerCatCode());
            }

            cardAuthorizeAddReqDTO.setSourceData(JsonUtils.toJson(lianLianOrderV1StatusDTO));
            cardAuthorizeAddReqDTO.setAuthStatus(0);
            cardAuthorizeAddReqDTO.setTradeType("AUTHORIZATION");
            cardAuthorizeAddReqDTO.setTradeTime(DateTimeUtil.fromUtcToUtcString(lianLianOrderV1StatusDTO.getSuccessTime()));
            boolean addData = cardAuthorizeService.add(cardAuthorizeAddReqDTO);
            if (!addData) {
                return false;
            }
        }

        List<LianLianOrderV1PayList> lianLianOrderV1PayList = lianLianOrderV1StatusDTO.getPayList();
        if (CollectionUtils.isEmpty(lianLianOrderV1PayList)) {
            return false;
        }
        //1.查询卡
        CardDTO cardDTO = cardService.cardDetailByCardId(lianLianOrderV1PayList.get(0).getAccountNo());
        //未查到卡
        if (cardDTO == null) {
            return false;
        }
        //卡状态校验
        boolean isActive = Objects.equals(cardDTO.getCardStatus(), CardStatusEnum.ACTIVE.getStatus());
        if (!isActive) {
            return false;
        }

        BigDecimal balanceOfAfterTrade = cardDTO.getBalance().subtract(settleAmountFen);
        if (balanceOfAfterTrade.compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }

        //2.落订单数据
        CardOrderDTO cardOrderPO = cardOrderService.findByTradeId(lianLianOrderV1StatusDTO.getOrderNo(), TransactionTypeEnum.PRE_AUTH.getKey());
        if (cardOrderPO == null) {
            String orderId = BizIdUtils.getFxOrderId();
            addRecordByAuth(lianLianOrderV1StatusDTO, cardDTO, orderId);

            //3.卡冻结金额
            frozenBalance(cardDTO, settleAmountFen, orderId);
            //发送授权成功消息
            try {
                FinhubLogger.info("发送push 消息 ");
                sendPushAndMsg(lianLianOrderV1StatusDTO.getSettleAmount(),lianLianOrderV1StatusDTO.getSettleCurrency(), orderId, TransactionTypeEnum.PRE_AUTH, cardDTO.getEmployeeId(), cardDTO.getCompanyId());
                //TODO 看看消息谁用
                KafkaProducer.me().sendCardAuthorizeMsg(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), cardDTO.getFxCardId(), settleAmount);
                cardOrderConsumeNoticeManager.sendNoticeMsgForConsume(cardDTO.getFxCardId(),orderId,lianLianOrderV1StatusDTO.getOrderNo(),null,TransactionTypeEnum.PRE_AUTH.getKey());
                cardOrderLargeOverNoticeManager.sendNoticeMsgForLargeOver(cardDTO.getFxCardId(),orderId,lianLianOrderV1StatusDTO.getOrderNo(),null,TransactionTypeEnum.PRE_AUTH.getKey());
            }catch (Exception ex){
                FinhubLogger.error("发送push 消息失败 ",ex);
            }

        }
        return true;
    }

    public void frozenBalance(CardDTO cardDTO, BigDecimal billTradeAmount, String bizNo) {

        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        CardModifyReqDTO cardModifyReqDTO = new CardModifyReqDTO();
        cardModifyReqDTO.setId(cardDTO.getId());
        //卡余额校验
        BigDecimal afterBalance = cardDTO.getBalance().subtract(billTradeAmount);
        BigDecimal afterFrozenBalance = cardDTO.getFreezenBalance().add(billTradeAmount);
        cardModifyReqDTO.setBalance(afterBalance);
        cardModifyReqDTO.setFreezenBalance(afterFrozenBalance);
        cardService.modify(cardModifyReqDTO);
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = buildBankCardFlowAddReqDTO(bizNo, cardModel, cardDTO);
        bankCardFlowAddReqDTO.setOperationType(CardFlowOperationTypeEnum.FROZEN.getCode());
        bankCardFlowAddReqDTO.setCurrentAmount(cardDTO.getBalance());
        bankCardFlowAddReqDTO.setOperationAmount(billTradeAmount);
        bankCardFlowAddReqDTO.setBalance(afterBalance);
        bankCardFlowAddReqDTO.setOperationFreezenAmount(billTradeAmount);
        bankCardFlowAddReqDTO.setFreezenBalance(afterFrozenBalance);
        bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
        bankCardFlowService.add(bankCardFlowAddReqDTO);
    }

    public BankCardFlowAddReqDTO buildBankCardFlowAddReqDTO(String bizNo, Integer cardModel, CardDTO cardDTO) {
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = new BankCardFlowAddReqDTO();
        bankCardFlowAddReqDTO.setId(IdUtils.getId());
        bankCardFlowAddReqDTO.setFxCardId(cardDTO.getFxCardId());
        bankCardFlowAddReqDTO.setEmployeeId(cardDTO.getEmployeeId());
        bankCardFlowAddReqDTO.setCompanyId(cardDTO.getCompanyId());
        bankCardFlowAddReqDTO.setBizNo(bizNo);
        bankCardFlowAddReqDTO.setCardModel(cardModel);
        bankCardFlowAddReqDTO.setCreateTime(new Date());
        bankCardFlowAddReqDTO.setUpdateTime(new Date());
        bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
        return bankCardFlowAddReqDTO;
    }

    public void addRecordByAuth(LianLianOrderV1StatusDTO lianLianOrderV1StatusDTO, CardDTO cardDTO, String bizNo) {
        try {
            CardOrderAddReqDTO cardOrderAddReqDTO = new CardOrderAddReqDTO();
            cardOrderAddReqDTO.setFxCardId(cardDTO.getFxCardId());
            cardOrderAddReqDTO.setTradeAddress(lianLianOrderV1StatusDTO.getPayeeMerInfo().getPayeeMerAreaCode());
            BigDecimal orderAmount = new BigDecimal(lianLianOrderV1StatusDTO.getOrderAmount());
            BigDecimal tradeAmount = BigDecimalUtils.yuan2fen(orderAmount);
            cardOrderAddReqDTO.setTradeAmount(tradeAmount);
            cardOrderAddReqDTO.setTradeCurrency(lianLianOrderV1StatusDTO.getOrderCurrency());
            cardOrderAddReqDTO.setTradeName(lianLianOrderV1StatusDTO.getPayeeMerInfo().getPayeeMerName());
            cardOrderAddReqDTO.setBizNo(bizNo);
            cardOrderAddReqDTO.setTradeTime(DateTimeUtil.fromUtcToUtcString(lianLianOrderV1StatusDTO.getSuccessTime()));
            cardOrderAddReqDTO.setTradeTimeZone("TODO");
            cardOrderAddReqDTO.setType(TransactionTypeEnum.PRE_AUTH.getKey());
            cardOrderAddReqDTO.setEmployeeId(cardDTO.getEmployeeId());
            cardOrderAddReqDTO.setCompanyId(cardDTO.getCompanyId());
            cardOrderAddReqDTO.setCreateTime(new Date());
            BigDecimal settlementAmount = new BigDecimal(lianLianOrderV1StatusDTO.getSettleAmount());
            BigDecimal billTradeAmount = BigDecimalUtils.yuan2fen(settlementAmount);
            cardOrderAddReqDTO.setBillTradeAmount(billTradeAmount);
            cardOrderAddReqDTO.setBillTradeCurrency(lianLianOrderV1StatusDTO.getSettleCurrency());
            cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
            cardOrderAddReqDTO.setTradeId(lianLianOrderV1StatusDTO.getOrderNo());
            cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setNeedNotCheckAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setRefundAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
            cardOrderAddReqDTO.setMaskedCardNumber(cardDTO.getBankCardNo());
            cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
            cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());
            cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
            cardOrderAddReqDTO.setCnyTradeAmount(billTradeAmount);
            cardOrderAddReqDTO.setTradeCnyExchangeRate(new BigDecimal("1.00"));
            //连连VCC验卡交易，会发送order_amount为0的交易结算通知
            if(BigDecimalUtils.hasNoPrice(orderAmount)){
                cardOrderAddReqDTO.setOrderShow(0);
            }
            cardOrderService.add(cardOrderAddReqDTO);
        } catch (Exception e) {
            FinhubLogger.error("订单生成异常", e);
        }

    }

    public void sendPushAndMsg(String transAmount,String settlementCurrency, String orderId, TransactionTypeEnum transactionTypeEnum, String receiverId, String companyId) {
        log.info("发送push 消息 orderId  = {}  transactionTypeEnum = {} , receiverId = {} ,companyId = {}", orderId, transactionTypeEnum.getValue(), receiverId, companyId);
        if (StringUtils.isNotBlank(receiverId)) {
            // 替换消息内容
            String content = "您有一笔"+ CurrencyEnum.getCurrencyByCodeIgnoreCase(settlementCurrency).getSymbol() + "{0}的{1}";
            String text = MessageFormat.format(content, transAmount, transactionTypeEnum.getValue());
            // APP push推送 / OA消息推送
            try {
                messageService.pushAlertMsg(buildAppAndOaPush(orderId, transactionTypeEnum, text, receiverId));
            } catch (IOException e) {
                log.error("APP push推送 / OA消息推送异常", e);
            }
            // 批量发送-APP消息中心
            KafkaProducer.me().publish(buildAppMsg(companyId, orderId, transactionTypeEnum, text, receiverId));
            // 批量发送-企业web消息中心
            KafkaProducer.me().publish(buildWebMsg(companyId, orderId, transactionTypeEnum, text, receiverId));
        }
    }

    private KafkaWebMessageMsg buildWebMsg(String companyId, String orderId, TransactionTypeEnum transactionTypeEnum, String content, String receiver) {
        log.info("境外虚拟卡消费发送消息，参数:{}, 类型：{},状态：{}", orderId, transactionTypeEnum.getValue(), content);
        String titleName = "海外卡通知-" + transactionTypeEnum.getValue();

        KafkaWebMessageMsg kafkaSaasMessageMsg = new KafkaWebMessageMsg();
        kafkaSaasMessageMsg.setBizType(BizType.AllConsume.getCode());
        kafkaSaasMessageMsg.setComment(content);
        kafkaSaasMessageMsg.setTitle(titleName);
        kafkaSaasMessageMsg.setCompanyId(companyId);
        kafkaSaasMessageMsg.setMsgType(MessageType.Consume.getCode());
        kafkaSaasMessageMsg.setSenderType(SenderType.HL.getCode());
        kafkaSaasMessageMsg.setReceiver(receiver);
        kafkaSaasMessageMsg.setSender(receiver);
        kafkaSaasMessageMsg.setBizOrder(orderId);
        kafkaSaasMessageMsg.setMsgSubType(MessageSubType.LARGE_OVER.getCode());
        log.info("境外虚拟卡消费发送消息，企业编号" + companyId + "," + JsonUtils.toJson(kafkaSaasMessageMsg));
        return kafkaSaasMessageMsg;
    }

    private KafkaSaasMessageMsg buildAppMsg(String companyId, String orderId, TransactionTypeEnum transactionTypeEnum, String content, String receiver) {
        log.info("境外虚拟卡消费发送消息，参数:{}, 类型：{},状态：{}", orderId, transactionTypeEnum.getValue(), content);

        String titleName = "海外卡通知-" + transactionTypeEnum.getValue();

        KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
        kafkaSaasMessageMsg.setBizType(81);
        kafkaSaasMessageMsg.setComment(content);
        kafkaSaasMessageMsg.setTitle(titleName);
        kafkaSaasMessageMsg.setCompanyId(companyId);
        kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
        kafkaSaasMessageMsg.setSenderType(SenderType.HL.getCode());
        kafkaSaasMessageMsg.setReceiver(receiver);
        kafkaSaasMessageMsg.setSender(receiver);
        kafkaSaasMessageMsg.setBizOrder(orderId);
        kafkaSaasMessageMsg.setInfo(getInfo(orderId, transactionTypeEnum));
        log.info("境外虚拟卡消费发送消息，企业编号" + companyId + "," + JsonUtils.toJson(kafkaSaasMessageMsg));
        return kafkaSaasMessageMsg;
    }

    private String getInfo(String orderId, TransactionTypeEnum transactionTypeEnum) {
        JSONObject json = new JSONObject();
        json.put("generate_time", DateUtils.format(new Date(), DateUtils.FORMAT_DATE_TIME_PATTERN));
        json.put("apply_type", transactionTypeEnum.getKey());
        json.put("apply_id", orderId);
        json.put("data_type", transactionTypeEnum.getKey());
        return json.toJSONString();
    }

    private PushAlertDto buildAppAndOaPush(String orderId, TransactionTypeEnum transactionTypeEnum, String content, String receiver) {
        PushAlertDto push = new PushAlertDto();
        push.setMsgType("81");
        push.setTitle("海外卡通知-" + transactionTypeEnum.getValue());
        push.setContent(content);
        push.setDesc(content);
        push.setUserId(receiver);
        // 自定义参数 提供路由跳转参数
        Map<String, Object> map = Maps.newHashMap();
        map.put("order_id", orderId);
        map.put("order_type", transactionTypeEnum.getKey());
        push.setMsgObj(map);
        return push;
    }

    /**
     * 交易结算通知: 结算
     */
    public boolean settlement(LianLianOrderV1SettleSuccessDTO lianLianOrderV1SettleSuccessDTO) {
        //1. 交易上锁
        String lockKey = RedisKeyConstant.TRADE_REDIS_KEY_PRE + lianLianOrderV1SettleSuccessDTO.getOrderNo();
        boolean isLocked = tryLockForTrade(lockKey);
        if (!isLocked) {
            return false;
        }
        //2. 授权验证
        FinhubLogger.info("【交易通知】请求参数->{}", JSON.toJSONString(lianLianOrderV1SettleSuccessDTO));
        CardAuthorizeDTO cardAuthorizeDTO = cardAuthorizeService.findByTradeId(lianLianOrderV1SettleSuccessDTO.getOrderNo(), "AUTHORIZATION");
        if (cardAuthorizeDTO == null) {
            //钉钉预警
            FinhubLogger.error("webhook通知异常,{}", JSON.toJSONString(lianLianOrderV1SettleSuccessDTO));
        }

        CardOrderDTO cardPreAuthOrderDTO = cardOrderService.findByTradeId(lianLianOrderV1SettleSuccessDTO.getOrderNo(), TransactionTypeEnum.PRE_AUTH.getKey());
        if (cardPreAuthOrderDTO == null) {
            return false;
        }
        CardDTO cardDTO = cardService.cardDetailByFxCardId(cardPreAuthOrderDTO.getFxCardId());
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardPreAuthOrderDTO.getCompanyId(), cardPreAuthOrderDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        //预授权的预结算币种
        BigDecimal preSettleAmount = cardPreAuthOrderDTO.getBillTradeAmount();
        BigDecimal balanceAfterUnfreezen = BigDecimal.ZERO;
        BigDecimal freezenBalanceAfterUnfreezen = cardDTO.getFreezenBalance().subtract(preSettleAmount);
        //冻结余额最小为0
        if (BigDecimalUtils.hasNoPrice(freezenBalanceAfterUnfreezen)) {
            freezenBalanceAfterUnfreezen = BigDecimal.ZERO;
        }
        if (BigDecimalUtils.hasPrice(preSettleAmount)) {
            balanceAfterUnfreezen = consume0(cardDTO, cardPreAuthOrderDTO, cardModel);
        } else {
            //消费无需解冻,说明超额消费了
            balanceAfterUnfreezen = cardDTO.getBalance();
        }
        //3.扣款并落订单
        //交易金额转换
        BigDecimal lianlianOrderAmount = new BigDecimal(lianLianOrderV1SettleSuccessDTO.getOrderAmount());
        BigDecimal orderAmount = BigDecimalUtils.yuan2fen(lianlianOrderAmount).abs();
        //扣款按照结算金额扣款
        BigDecimal lianlianSettleAmount = new BigDecimal(lianLianOrderV1SettleSuccessDTO.getSettleAmount());
        BigDecimal settleAmount = BigDecimalUtils.yuan2fen(lianlianSettleAmount).abs();
        BigDecimal balanceAfterConsume = balanceAfterUnfreezen.subtract(settleAmount);
        modifyBalance(cardDTO.getId(), balanceAfterConsume);
        //记录流水
        BankCardFlowAddReqDTO bankCardFlowAddReqDTOOfConsume = buildBankCardFlowAddReqDTO(cardPreAuthOrderDTO.getBizNo(), cardModel, cardDTO);
        bankCardFlowAddReqDTOOfConsume.setOperationType(CardFlowOperationTypeEnum.CONSUME.getCode());
        //操作前金额
        bankCardFlowAddReqDTOOfConsume.setCurrentAmount(balanceAfterUnfreezen);
        bankCardFlowAddReqDTOOfConsume.setOperationAmount(settleAmount);
        bankCardFlowAddReqDTOOfConsume.setBalance(balanceAfterConsume);
        bankCardFlowAddReqDTOOfConsume.setOperationFreezenAmount(BigDecimal.ZERO);
        bankCardFlowAddReqDTOOfConsume.setFreezenBalance(freezenBalanceAfterUnfreezen);
        bankCardFlowAddReqDTOOfConsume.setCurrentFreezenAmount(freezenBalanceAfterUnfreezen);
        bankCardFlowService.add(bankCardFlowAddReqDTOOfConsume);
        CardOrderAddReqDTO cardOrderAddReqDTO = buildCardOrderAddReqDTO(cardDTO, cardPreAuthOrderDTO,TransactionTypeEnum.CONSUME.getKey());
        cardOrderAddReqDTO.setBizNo(cardPreAuthOrderDTO.getBizNo());
        //消费（结算）的相关交易数据要以最新的交易结算通知为准
        cardOrderAddReqDTO.setTradeAmount(orderAmount);
        cardOrderAddReqDTO.setTradeCurrency(lianLianOrderV1SettleSuccessDTO.getOrderCurrency());
        cardOrderAddReqDTO.setBillTradeAmount(settleAmount);
        cardOrderAddReqDTO.setBillTradeCurrency(lianLianOrderV1SettleSuccessDTO.getSettleCurrency());
        //待核销相关金额与交易金额及其币种保持一致
        cardOrderAddReqDTO.setUncheckedAmount(orderAmount);
        cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setNeedNotCheckAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.UNCHECK.getKey());
        cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
        cardOrderAddReqDTO.setMaskedCardNumber(cardDTO.getBankCardNo());
        cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
        cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
        cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());
        //交易币种兑人民币汇率计算，以及人民币金额计算
        cardOrderAddReqDTO.setTradeCnyExchangeRate(cardPreAuthOrderDTO.getTradeCnyExchangeRate());
        cardOrderAddReqDTO.setCnyTradeAmount(settleAmount);
        //连连VCC验卡交易，会发送order_amount为0的交易结算通知
        if(BigDecimalUtils.hasNoPrice(orderAmount)){
            cardOrderAddReqDTO.setOrderShow(0);
        }
        FinhubLogger.info("consume 交易  添加付款记录 req = {} ", JSON.toJSONString(cardOrderAddReqDTO));
        cardOrderService.add(cardOrderAddReqDTO);
        //更新额度申请上的可用余额
        updateAvailable(cardDTO.getFxCardId(), preSettleAmount);
        //TODO 单位核对
        KafkaProducer.me().sendCardAuthorizeMsg(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), cardDTO.getFxCardId(), preSettleAmount);
        return true;
    }

    public void modifyBalance(String id, BigDecimal afterBalance) {
        CardModifyReqDTO cardModifyReqDTO = new CardModifyReqDTO();
        cardModifyReqDTO.setId(id);
        cardModifyReqDTO.setBalance(afterBalance);
        cardService.modify(cardModifyReqDTO);
    }

    /**
     * 标准交易流程
     * 1. 解冻
     * 2. 记录卡流水
     * 3. 记录订单
     */
    public BigDecimal consume0(CardDTO cardDTO, CardOrderDTO cardOrderDTO, Integer cardModel) {
        //解冻预授权的金额
        BigDecimal balanceAfterUnfreezen = cardDTO.getBalance().add(cardOrderDTO.getBillTradeAmount());
        BigDecimal freezenBalanceAfterUnfreezen = cardDTO.getFreezenBalance().subtract(cardOrderDTO.getBillTradeAmount());
        BigDecimal operationFreezenAmount = cardOrderDTO.getBillTradeAmount();
        BigDecimal operationAmount = cardOrderDTO.getBillTradeAmount();
        //冻结余额最小为0
        if (BigDecimalUtils.hasNoPrice(freezenBalanceAfterUnfreezen)) {
            freezenBalanceAfterUnfreezen = BigDecimal.ZERO;
            operationFreezenAmount = cardDTO.getFreezenBalance();
            //只能全额解冻
            balanceAfterUnfreezen = cardDTO.getBalance().add(cardDTO.getFreezenBalance());
            operationAmount = cardDTO.getFreezenBalance();
        }
        //2.解冻(避免解冻超额,验证下解冻记录和金额) 并落单
        unfrozenBalance(cardDTO.getId(), balanceAfterUnfreezen, freezenBalanceAfterUnfreezen);
        //记录流水
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = buildBankCardFlowAddReqDTO(cardOrderDTO.getBizNo(), cardModel, cardDTO);
        bankCardFlowAddReqDTO.setOperationType(CardFlowOperationTypeEnum.UNFROZEN.getCode());
        bankCardFlowAddReqDTO.setCurrentAmount(cardDTO.getBalance());
        bankCardFlowAddReqDTO.setOperationAmount(operationAmount);
        bankCardFlowAddReqDTO.setBalance(balanceAfterUnfreezen);
        bankCardFlowAddReqDTO.setOperationFreezenAmount(operationFreezenAmount);
        bankCardFlowAddReqDTO.setFreezenBalance(freezenBalanceAfterUnfreezen);
        bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
        bankCardFlowService.add(bankCardFlowAddReqDTO);
        releaseFrozen(cardDTO,cardOrderDTO,operationAmount);
        return balanceAfterUnfreezen;
    }
    /**
     * 预定释放
     * @param cardDTO 卡
     * @param cardOrderDTO 交易订单
     */
    public void releaseFrozen(CardDTO cardDTO, CardOrderDTO cardOrderDTO, BigDecimal billAmount){
        CardOrderAddReqDTO cardOrderAddReqDTO = buildCardOrderAddReqDTO(cardDTO,cardOrderDTO,TransactionTypeEnum.PRE_AUTH_RELEASE.getKey());
        //人工设置为预定释放
        cardOrderAddReqDTO.setType(AirwallexTransactionTypeEnum.REVERSAL.getTransactionTypeEnum().getKey());
        cardOrderAddReqDTO.setBillTradeAmount(billAmount);
        cardOrderAddReqDTO.setBillTradeCurrency(cardOrderDTO.getBillTradeCurrency());
        cardOrderAddReqDTO.setBizNo(cardOrderDTO.getBizNo());
        cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
        //待核销相关金额与交易金额及其币种保持一致
        cardOrderAddReqDTO.setNeedNotCheckAmount(cardOrderAddReqDTO.getTradeAmount());
        cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
        cardOrderAddReqDTO.setTradeCnyExchangeRate(cardOrderDTO.getTradeCnyExchangeRate());
        cardOrderAddReqDTO.setCnyTradeAmount(billAmount);
        cardOrderAddReqDTO.setOrderShow(0);
        cardOrderService.add(cardOrderAddReqDTO);
        //同时将预授权交易设置为不展示
        CardOrderListReqDTO cardOrderListReqDTO = new CardOrderListReqDTO();
        cardOrderListReqDTO.setBizNo(cardOrderDTO.getBizNo());
        cardOrderListReqDTO.setType(TransactionTypeEnum.PRE_AUTH.getKey());
        CardOrderListResDTO cardOrderListResDTO = cardOrderService.listOne(cardOrderListReqDTO);
        if (cardOrderListResDTO != null) {
            cardOrderService.updateOrderShow(String.valueOf(cardOrderListResDTO.getId()));
        }
    }

    public void unfrozenBalance(String id, BigDecimal afterBalance, BigDecimal freezenBalance) {
        CardModifyReqDTO cardModifyReqDTO = new CardModifyReqDTO();
        cardModifyReqDTO.setId(id);
        cardModifyReqDTO.setBalance(afterBalance);
        cardModifyReqDTO.setFreezenBalance(freezenBalance);
        cardService.modify(cardModifyReqDTO);
    }

    public CardOrderAddReqDTO buildCardOrderAddReqDTO(CardDTO cardDTO, CardOrderDTO cardOrderDTO,Integer type) {
        try {
            CardOrderAddReqDTO cardOrderAddReqDTO = new CardOrderAddReqDTO();
            cardOrderAddReqDTO.setFxCardId(cardDTO.getFxCardId());
            cardOrderAddReqDTO.setTradeAddress(cardOrderDTO.getTradeAddress());
            cardOrderAddReqDTO.setTradeAmount(cardOrderDTO.getTradeAmount());
            cardOrderAddReqDTO.setTradeCurrency(cardOrderDTO.getTradeCurrency());
            cardOrderAddReqDTO.setTradeName(cardOrderDTO.getTradeName());
            cardOrderAddReqDTO.setTradeId(cardOrderDTO.getTradeId());
            cardOrderAddReqDTO.setTradeTime(cardOrderDTO.getTradeTime());
            cardOrderAddReqDTO.setTradeTimeZone("TODO");
            cardOrderAddReqDTO.setType(type);
            cardOrderAddReqDTO.setEmployeeId(cardDTO.getEmployeeId());
            cardOrderAddReqDTO.setCompanyId(cardDTO.getCompanyId());
            cardOrderAddReqDTO.setCreateTime(new Date());
            cardOrderAddReqDTO.setBillTradeAmount(cardOrderDTO.getBillTradeAmount());
            cardOrderAddReqDTO.setBillTradeCurrency(cardOrderDTO.getBillTradeCurrency());
            cardOrderAddReqDTO.setMaskedCardNumber(cardOrderDTO.getMaskedCardNumber());
            cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
            cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
            cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());

            return cardOrderAddReqDTO;
        } catch (Exception e) {
            return new CardOrderAddReqDTO();
        }

    }

    public void updateAvailable(String fxCardId, BigDecimal billAmount) {
        FinhubLogger.info("updateAvailable fxCardId = {} , billAmount = {}", fxCardId, billAmount);
        //更新额度申请上的可用余额
        List<CardCreditManagerDTO> apply = CardCreditManagerManager.me().queryUncheckApply(fxCardId);
        CardCreditManagerDTO canUse = null;
        for (CardCreditManagerDTO cardCreditManagerDTO : apply) {
            boolean isCanUse = cardCreditManagerDTO.getAvalibleAmount().compareTo(billAmount) >= 0;
            if (isCanUse) {
                canUse = cardCreditManagerDTO;
                break;
            }
        }
        FinhubLogger.info("updateAvailable canUse = {} ", JSON.toJSONString(canUse));
        if (canUse != null) {
            CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
            cardCreditManagerModifyReqDTO.setId(Long.valueOf(canUse.getId()));
            cardCreditManagerModifyReqDTO.setAvalibleAmount(canUse.getAvalibleAmount().subtract(billAmount));
            CardCreditManagerManager.me().modify(cardCreditManagerModifyReqDTO);
        } else {
            CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
            cardCreditManagerModifyReqDTO.setId(Long.valueOf(apply.get(0).getId()));
            cardCreditManagerModifyReqDTO.setAvalibleAmount(apply.get(0).getAvalibleAmount().subtract(billAmount));
            CardCreditManagerManager.me().modify(cardCreditManagerModifyReqDTO);
        }
        FinhubLogger.info("updateAvailable CardCreditManagerManager modify fxCardId = {} ", fxCardId);
    }

    private boolean tryLockForTrade(String lockKey) {
        try {
            boolean tryLock = redissonService.tryLock4Trade(lockKey);
            if (!tryLock) {
                return false;
            }
        } catch (InterruptedException e) {
            return false;
        }
        return true;
    }
    /**
     * 交易退款通知: 退款
     */
    public boolean refund(LianLianOrderV1RefundDTO lianLianOrderV1RefundDTO) {
        CardOrderDTO cardOrderDTO = cardOrderService.findByTradeId(lianLianOrderV1RefundDTO.getOriOrderNo(), TransactionTypeEnum.CONSUME.getKey());
        CardDTO cardDTO = cardService.cardDetailByFxCardId(cardOrderDTO.getFxCardId());
        CardOrderDTO refund = cardOrderService.findByTradeId(lianLianOrderV1RefundDTO.getRefundNo(), TransactionTypeEnum.REFUND.getKey());
        if (refund == null) {
            BigDecimal refundSettlement = new BigDecimal(lianLianOrderV1RefundDTO.getRefundSettleAmount());
            BigDecimal billAmount = BigDecimalUtils.yuan2fen(refundSettlement).abs();
            BigDecimal afterBalance = cardDTO.getBalance().add(billAmount);
            modifyBalance(cardDTO.getId(), afterBalance);
            EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
            //当前生效模式
            Integer cardModel = employeeModelConfigDTO.getActiveModel();
            BankCardFlowAddReqDTO bankCardFlowAddReqDTO = buildBankCardFlowAddReqDTO(cardOrderDTO.getBizNo(), cardModel, cardDTO);
            bankCardFlowAddReqDTO.setOperationType(CardFlowOperationTypeEnum.REFUND.getCode());
            bankCardFlowAddReqDTO.setCurrentAmount(cardDTO.getBalance());
            bankCardFlowAddReqDTO.setOperationAmount(billAmount);
            bankCardFlowAddReqDTO.setBalance(afterBalance);
            bankCardFlowAddReqDTO.setOperationFreezenAmount(BigDecimal.ZERO);
            bankCardFlowAddReqDTO.setFreezenBalance(cardDTO.getFreezenBalance());
            bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
            bankCardFlowService.add(bankCardFlowAddReqDTO);
            String refundOrderId = BizIdUtils.getFxRefundOrderId();
            CardOrderAddReqDTO cardOrderAddReqDTO = buildCardOrderAddReqDTO(cardDTO, cardOrderDTO,TransactionTypeEnum.REFUND.getKey());
            BigDecimal tradeAmount = new BigDecimal(lianLianOrderV1RefundDTO.getRefundAmount());
            BigDecimal refundTradeAmount = BigDecimalUtils.yuan2fen(tradeAmount).abs();
            cardOrderAddReqDTO.setTradeAmount(refundTradeAmount);
            cardOrderAddReqDTO.setTradeCurrency(lianLianOrderV1RefundDTO.getRefundCurrency());
            cardOrderAddReqDTO.setBillTradeAmount(billAmount);
            cardOrderAddReqDTO.setBillTradeCurrency(lianLianOrderV1RefundDTO.getRefundSettleCurrency());
            cardOrderAddReqDTO.setBizNo(refundOrderId);
            cardOrderAddReqDTO.setOriBizNo(cardOrderDTO.getBizNo());
            cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
            //待核销相关金额与交易金额及其币种保持一致
            cardOrderAddReqDTO.setNeedNotCheckAmount(cardOrderAddReqDTO.getTradeAmount());
            BigDecimal rate = cardOrderDTO.getTradeCnyExchangeRate();
            cardOrderAddReqDTO.setTradeCnyExchangeRate(rate);
            cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
            cardOrderAddReqDTO.setMaskedCardNumber(cardDTO.getBankCardNo());
            cardOrderAddReqDTO.setCardFormFactor(cardDTO.getCardFormFactor());
            cardOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
            cardOrderAddReqDTO.setNameOnCard(cardDTO.getNameOnCard());
            cardOrderAddReqDTO.setCnyTradeAmount(billAmount);
            cardOrderAddReqDTO.setTradeCnyExchangeRate(new BigDecimal("1.00"));
            cardOrderService.add(cardOrderAddReqDTO);
            FinhubLogger.info("境外企业卡  退款 入库  req = {}", JSON.toJSONString(cardOrderAddReqDTO));
            modifyCheckInfo(cardOrderDTO, refundTradeAmount, refundOrderId);
            //更新额度申请上的可用余额
            List<CardCreditManagerDTO> apply = CardCreditManagerManager.me().queryUncheckApply(cardDTO.getFxCardId());
            CardCreditManagerDTO canUse = null;
            for (CardCreditManagerDTO cardCreditManagerDTO : apply) {
                boolean isCanUse = cardCreditManagerDTO.getAvalibleAmount().compareTo(billAmount) >= 0;
                if (isCanUse) {
                    canUse = cardCreditManagerDTO;
                    break;
                }
            }
            if (canUse != null) {
                CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
                cardCreditManagerModifyReqDTO.setId(Long.valueOf(canUse.getId()));
                cardCreditManagerModifyReqDTO.setAvalibleAmount(canUse.getAvalibleAmount().add(billAmount));
                CardCreditManagerManager.me().modify(cardCreditManagerModifyReqDTO);
            }
            try {
                FinhubLogger.info("退款发送消息  req = {}", JSON.toJSONString(cardOrderAddReqDTO));
                sendPushAndMsg(refundSettlement.toString(),cardOrderDTO.getBillTradeCurrency(), refundOrderId, TransactionTypeEnum.REFUND, cardDTO.getEmployeeId(), cardDTO.getCompanyId());
                cardOrderConsumeNoticeManager.sendNoticeMsgForConsume(cardDTO.getFxCardId(),refundOrderId,cardOrderDTO.getTradeId(),null,TransactionTypeEnum.REFUND.getKey());
            }catch (Exception ex){
                FinhubLogger.error("发送push 消息失败 ",ex);
            }
        }
        return true;
    }

    public void modifyCheckInfo(CardOrderDTO cardOrderDTO, BigDecimal refundTradeAmount, String refundOrderId) {
        //查询原始单据是否有核销的情况，如果有，需要调用核销
        if (StringUtils.isNotBlank(cardOrderDTO.getCostId())) {
            TradeRefundRpcDTO tradeRefundRpcDTO = new TradeRefundRpcDTO();
            tradeRefundRpcDTO.setOrderId(cardOrderDTO.getBizNo());
            tradeRefundRpcDTO.setRefundOrderId(refundOrderId);
            tradeRefundRpcDTO.setCompanyId(cardOrderDTO.getCompanyId());
            tradeRefundRpcDTO.setType(TransactionTypeEnum.REFUND.getKey());
            log.info("消费退款，调用核销处理，tradeRefundRpcDTO={}", JsonUtils.toJson(tradeRefundRpcDTO));
            iVirtualCardBudgetRpcService.fxTradeRefundAmount(tradeRefundRpcDTO);
            log.info("消费退款，调用核销处理结果成功，tradeRefundRpcDTO={}", JsonUtils.toJson(tradeRefundRpcDTO));

            //重新获取交易记录
            cardOrderDTO = cardOrderService.findByTradeId(cardOrderDTO.getTradeId(), TransactionTypeEnum.CONSUME.getKey());
        }

        CardOrderModifyReqDTO modifyReqDTO = new CardOrderModifyReqDTO();
        modifyReqDTO.setId(Long.valueOf(cardOrderDTO.getId()));
        modifyReqDTO.setUncheckedAmount(cardOrderDTO.getUncheckedAmount().subtract(refundTradeAmount));
        modifyReqDTO.setRefundAmount(cardOrderDTO.getRefundAmount().add(refundTradeAmount));
        if (modifyReqDTO.getUncheckedAmount().compareTo(BigDecimal.ZERO) > 0) {
            modifyReqDTO.setCheckStatus(CheckStatusEnum.UNCHECK.getKey());
        } else {
            modifyReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
        }
        cardOrderService.modify(modifyReqDTO);
    }

    /**
     * 交易撤销通知: 撤销
     */
    public boolean reversal(LianLianOrderV1ReverseDTO lianLianOrderV1ReverseDTO) {
        CardOrderDTO cardOrderDTO = cardOrderService.findByTradeId(lianLianOrderV1ReverseDTO.getOrderNo(), TransactionTypeEnum.PRE_AUTH.getKey());
        if (cardOrderDTO == null) {
            return false;
        }
        CardOrderDTO cardOrderDTOForRelease = cardOrderService.findByTradeId(lianLianOrderV1ReverseDTO.getOrderNo(), TransactionTypeEnum.PRE_AUTH_RELEASE.getKey());
        if (cardOrderDTOForRelease != null) {
            return true;
        }
        CardDTO cardDTO = cardService.cardDetailByFxCardId(cardOrderDTO.getFxCardId());
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(),cardDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        BigDecimal balanceAfterUnfreezen = cardDTO.getBalance().add(cardOrderDTO.getBillTradeAmount());
        BigDecimal freezenBalanceAfterUnfreezen = cardDTO.getFreezenBalance().subtract(cardOrderDTO.getBillTradeAmount());
        BigDecimal operationFreezenAmount = cardOrderDTO.getBillTradeAmount();
        BigDecimal operationAmount = cardOrderDTO.getBillTradeAmount();
        //冻结余额最小为0
        if (BigDecimalUtils.hasNoPrice(freezenBalanceAfterUnfreezen)){
            freezenBalanceAfterUnfreezen = BigDecimal.ZERO;
            operationFreezenAmount = cardDTO.getFreezenBalance();
            //只能全额解冻
            balanceAfterUnfreezen = cardDTO.getBalance().add(cardDTO.getFreezenBalance());
            operationAmount = cardDTO.getFreezenBalance();
        }
        //2.解冻(避免解冻超额,验证下解冻记录和金额) 并落单
        unfrozenBalance(cardDTO.getId(), balanceAfterUnfreezen, freezenBalanceAfterUnfreezen);
        //记录流水
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = buildBankCardFlowAddReqDTO(cardOrderDTO.getBizNo(), cardModel, cardDTO);
        bankCardFlowAddReqDTO.setOperationType(CardFlowOperationTypeEnum.UNFROZEN.getCode());
        bankCardFlowAddReqDTO.setCurrentAmount(cardDTO.getBalance());
        bankCardFlowAddReqDTO.setOperationAmount(operationAmount);
        bankCardFlowAddReqDTO.setBalance(balanceAfterUnfreezen);
        bankCardFlowAddReqDTO.setOperationFreezenAmount(operationFreezenAmount);
        bankCardFlowAddReqDTO.setFreezenBalance(freezenBalanceAfterUnfreezen);
        bankCardFlowAddReqDTO.setCurrentFreezenAmount(cardDTO.getFreezenBalance());
        bankCardFlowService.add(bankCardFlowAddReqDTO);
        CardOrderAddReqDTO cardOrderAddReqDTO = buildCardOrderAddReqDTO(cardDTO,cardOrderDTO,TransactionTypeEnum.PRE_AUTH_RELEASE.getKey());
        //人工设置为预定释放
        cardOrderAddReqDTO.setType(AirwallexTransactionTypeEnum.REVERSAL.getTransactionTypeEnum().getKey());
        cardOrderAddReqDTO.setBillTradeAmount(cardOrderDTO.getBillTradeAmount());
        cardOrderAddReqDTO.setBillTradeCurrency(cardOrderDTO.getBillTradeCurrency());
        cardOrderAddReqDTO.setBizNo(cardOrderDTO.getBizNo());
        cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
        cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
        //待核销相关金额与交易金额及其币种保持一致
        cardOrderAddReqDTO.setNeedNotCheckAmount(cardOrderAddReqDTO.getTradeAmount());
        cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
        cardOrderAddReqDTO.setTradeCnyExchangeRate(cardOrderDTO.getTradeCnyExchangeRate());
        cardOrderAddReqDTO.setCnyTradeAmount(cardOrderDTO.getBillTradeAmount());
        cardOrderAddReqDTO.setOrderShow(0);
        cardOrderService.add(cardOrderAddReqDTO);
        //同时将预授权交易设置为不展示
        CardOrderListReqDTO cardOrderListReqDTO = new CardOrderListReqDTO();
        cardOrderListReqDTO.setBizNo(cardOrderDTO.getBizNo());
        cardOrderListReqDTO.setType(TransactionTypeEnum.PRE_AUTH.getKey());
        CardOrderListResDTO cardOrderListResDTO = cardOrderService.listOne(cardOrderListReqDTO);
        if (cardOrderListResDTO != null) {
            cardOrderService.updateOrderShow(String.valueOf(cardOrderListResDTO.getId()));
        }
        return true;
    }
}
