# 分贝外汇卡片管理系统 (FenBei FX Card)

## 项目简介

分贝外汇卡片管理系统是一个企业级的国际卡片管理平台，主要为企业提供海外支付卡片的申请、管理、交易、额度控制等全流程服务。系统支持多种卡片平台（如 Airwallex、连连支付等），为企业员工提供便捷的海外消费解决方案。

## 核心功能

### 🏦 卡片管理
- **卡片申请**: 支持虚拟卡和实体卡申请
- **卡片状态管理**: 激活、冻结、挂失、注销等状态控制
- **多平台支持**: 集成 Airwallex、连连支付等多个发卡平台
- **卡片信息查询**: 余额查询、交易记录、卡片详情等

### 👤 持卡人管理
- **持卡人信息维护**: 个人信息、身份验证、地址管理
- **KYC 流程**: 身份证件验证、合规性检查
- **持卡人状态管理**: 审核、启用、禁用等状态控制

### 💰 额度管理
- **额度申请**: 支持企业为员工申请卡片额度
- **额度分配**: 灵活的额度分配和回收机制
- **额度监控**: 实时监控额度使用情况
- **预算控制**: 与企业预算系统集成

### 💳 交易管理
- **交易授权**: 实时交易授权处理
- **交易记录**: 完整的交易历史记录
- **交易核销**: 支持交易后的费用核销流程
- **风控管理**: 交易风险控制和限额管理

### 📊 企业管理
- **多企业支持**: 支持多企业租户模式
- **权限管理**: 基于角色的权限控制
- **配置管理**: 企业级配置和模式设置
- **报表统计**: 交易统计、使用情况分析

## 技术架构

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 前端层    │    │   移动端 APP    │    │   第三方系统    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                        API 网关层                               │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 控制器    │    │   RPC 服务      │    │   定时任务      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                        业务服务层                               │
│  卡片服务 │ 持卡人服务 │ 额度服务 │ 交易服务 │ 企业服务        │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                        数据访问层                               │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │     Redis       │    │   第三方 API    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈

#### 后端技术
- **框架**: Spring Boot 2.x
- **微服务**: Apache Dubbo
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **ORM**: MyBatis Plus
- **消息队列**: Apache Kafka
- **认证授权**: 自研 FinhubAuth 框架

#### 开发工具
- **构建工具**: Maven 3.x
- **Java 版本**: JDK 8+
- **代码简化**: Lombok
- **API 文档**: Swagger
- **日志框架**: Logback

#### 第三方集成
- **发卡平台**: Airwallex、连连支付
- **支付系统**: 分贝支付系统
- **用户中心**: 分贝用户中心
- **费用管理**: 费用管理系统
- **国际化**: MLS 多语言系统

#### 国际化支持
- **多语言**: 支持中文、英文、日文等多种语言
- **多币种**: 支持美元、欧元、日元等主要货币
- **时区处理**: 自动适应不同地区的时区设置
- **本地化**: 根据地区自动调整日期、货币格式

## 模块结构

```
fenbei-fx-card/
├── fenbei-fx-card-api/          # API 接口定义
├── fenbei-fx-card-client/       # 客户端 SDK
├── fenbei-fx-card-common/       # 公共组件
├── fenbei-fx-card-dao/          # 数据访问层
├── fenbei-fx-card-rpc/          # RPC 服务实现
├── fenbei-fx-card-server/       # 服务启动模块
├── fenbei-fx-card-service/      # 业务服务层
└── fenbei-fx-card-web/          # Web 控制器层
```

### 模块说明

- **api**: 定义对外提供的 RPC 接口和 DTO
  - 包含卡片管理、持卡人管理、额度管理等核心接口定义
  - 定义了各种请求和响应的数据传输对象
  - 包含系统中使用的枚举类型定义

- **client**: 提供给其他系统调用的客户端 SDK
  - 封装了 RPC 调用的细节
  - 提供了简化的 API 调用方式

- **common**: 公共常量、枚举、工具类等
  - 包含系统中使用的常量定义
  - 提供通用的工具类和辅助方法
  - 定义了系统中使用的异常类型

- **dao**: 数据库实体类、Mapper 接口
  - 定义了与数据库表对应的实体类
  - 包含 MyBatis Mapper 接口
  - 实现了数据访问层的逻辑

- **rpc**: Dubbo RPC 服务的具体实现
  - 实现了在 api 模块中定义的接口
  - 处理 RPC 调用的业务逻辑
  - 与服务层交互完成业务处理

- **server**: 应用启动类和配置
  - 包含应用的主启动类
  - 定义了系统的配置信息
  - 管理应用的生命周期

- **service**: 核心业务逻辑实现
  - 实现了卡片管理、持卡人管理、额度管理等核心业务逻辑
  - 包含各种服务的实现类和管理器
  - 处理复杂的业务规则和流程

- **web**: HTTP API 控制器
  - 提供 RESTful API 接口
  - 处理 HTTP 请求和响应
  - 与服务层交互完成业务处理

## 核心业务流程

### 卡片申请流程
1. **持卡人申请** → 员工提交持卡人信息
2. **企业审核** → 企业管理员审核持卡人信息
3. **卡片申请** → 通过审核后申请卡片
4. **平台处理** → 调用第三方平台创建卡片
5. **卡片激活** → 实体卡需要激活后使用

### 额度管理流程
1. **额度申请** → 员工或管理员申请卡片额度
2. **预算检查** → 检查企业预算是否充足
3. **额度发放** → 从企业账户转移额度到卡片
4. **额度使用** → 卡片消费时扣减额度
5. **额度回收** → 未使用额度可以回收到企业账户

### 交易处理流程
1. **交易授权** → 实时处理交易授权请求
2. **风控检查** → 检查交易限额和风控规则
3. **余额扣减** → 扣减卡片可用余额
4. **交易记录** → 记录完整的交易信息
5. **费用核销** → 支持后续的费用核销流程

## 核心代码结构

### 卡片管理核心类
- `CardService`: 卡片服务接口，定义了卡片的基本操作
- `CardManager`: 卡片管理器，实现卡片的核心业务逻辑
- `UserCardManager`: 用户卡片管理器，处理用户卡片相关的业务逻辑

### 持卡人管理核心类
- `CardholderService`: 持卡人服务接口，定义了持卡人的基本操作
- `CardholderManager`: 持卡人管理器，实现持卡人的核心业务逻辑
- `CardholderApplyService`: 持卡人申请服务，处理持卡人申请相关的业务逻辑

### 额度管理核心类
- `CardCreditManagerService`: 卡片额度管理服务接口，定义了额度的基本操作
- `CardCreditManagerManager`: 卡片额度管理器，实现额度的核心业务逻辑
- `UserCardCreditManager`: 用户卡片额度管理器，处理用户额度相关的业务逻辑

### 交易管理核心类
- `CardOrderService`: 卡片订单服务接口，定义了交易的基本操作
- `CardAuthorizeService`: 卡片授权服务，处理交易授权相关的业务逻辑
- `BankCardFlowService`: 银行卡流水服务，处理交易流水相关的业务逻辑

### 错误处理与异常管理
- `GlobalExceptionHandler`: 全局异常处理器，统一处理系统异常
- `BusinessException`: 业务异常类，封装业务错误信息
- `ErrorCodeEnum`: 错误码枚举，定义系统错误码
- `ResponseResult`: 统一响应结果封装类

## 快速开始

### 环境要求
- JDK 8 或更高版本
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/fenbeitong/fenbei-fx-card.git
cd fenbei-fx-card
```

2. **配置数据库**
```sql
CREATE DATABASE fenbei_fx_card DEFAULT CHARACTER SET utf8mb4;
```

3. **配置环境变量**
```bash
export SPRING_PROFILES_ACTIVE=dev
```

4. **启动应用**
```bash
mvn clean install
java -jar fenbei-fx-card-server/target/fenbei-fx-card-server.jar
```

### 配置说明

主要配置文件位于 `fenbei-fx-card-server/src/main/resources/`：
- `application.yaml`: 基础配置
- `application-{env}.yaml`: 环境特定配置

## API 文档

启动应用后，可以通过以下地址访问 API 文档：
- Swagger UI: `http://localhost:8080/swagger-ui.html`

## 主要 API 接口

### 卡片管理
- `GET /fxcard/app/card/list` - 查询卡片列表
- `POST /fxcard/app/card/apply` - 申请卡片
- `PUT /fxcard/app/card/status` - 更新卡片状态
- `GET /fxcard/app/card/balance` - 查询卡片余额

### 持卡人管理
- `POST /fxcard/app/cardholder/apply` - 申请持卡人
- `PUT /fxcard/app/cardholder/update` - 更新持卡人信息
- `GET /fxcard/app/cardholder/detail` - 查询持卡人详情

### 额度管理
- `POST /fxcard/app/credit/apply` - 申请额度
- `POST /fxcard/app/credit/return` - 退回额度
- `GET /fxcard/app/credit/list` - 查询额度记录

## 数据库设计

### 核心表结构

- `fx_card`: 卡片信息表
- `fx_cardholder`: 持卡人信息表
- `fx_card_apply`: 卡片申请表
- `fx_card_credit_manager`: 额度管理表
- `fx_card_order`: 交易订单表
- `fx_bank_card_flow`: 卡片流水表

## 部署说明

### Docker 部署
```bash
# 构建镜像
docker build -t fenbei-fx-card .

# 运行容器
docker run -d -p 8080:8080 -e SPRING_PROFILES_ACTIVE=prod fenbei-fx-card
```

### 生产环境部署
1. 配置生产环境数据库连接
2. 设置环境变量 `SPRING_PROFILES_ACTIVE=prod`
3. 配置负载均衡和监控
4. 设置日志收集和告警

## 监控和运维

### 健康检查
- 应用健康检查: `/actuator/health`
- 数据库连接检查: `/actuator/db`
- 系统资源监控: `/actuator/metrics`

### 日志配置
- 日志级别可通过配置文件调整
- 支持按模块设置不同的日志级别
- 生产环境建议使用 ELK 进行日志收集

## 安全措施

### 数据安全
- 敏感数据加密存储
- 传输数据使用 HTTPS/TLS 加密
- 卡号和CVV等敏感信息脱敏展示

### 访问控制
- 基于角色的权限控制 (RBAC)
- API 访问鉴权与限流
- 多因素认证支持

### 合规性
- 符合 PCI-DSS 支付卡行业数据安全标准
- 遵循 GDPR 数据保护规范
- 定期安全审计和渗透测试

## 性能优化

### 缓存策略
- 多级缓存设计：本地缓存 + Redis 分布式缓存
- 热点数据预加载
- 缓存失效策略：LRU + 定时刷新

### 数据库优化
- 读写分离
- 分库分表设计
- 索引优化
- SQL 性能监控

### 高并发处理
- 异步处理非核心流程
- 限流与熔断机制
- 线程池隔离与管理

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request
5. 通过 CI/CD 流水线检查

## 许可证

本项目采用 [MIT License](LICENSE)

## 联系方式

- 项目维护者: 分贝科技开发团队
- 邮箱: <EMAIL>
- 文档: [https://docs.fenbeitong.com/fx-card/](https://docs.fenbeitong.com/fx-card/)

---

*最后更新时间: 2025年8月20日*