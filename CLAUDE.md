# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FenBei FX Card (分贝外汇卡片管理系统) is an enterprise-level international card management platform that provides comprehensive card application, management, transaction, and credit control services for overseas payment cards. The system supports multiple card platforms (Airwallex, LianLian Pay) and provides convenient overseas payment solutions for enterprise employees.

## Build & Development Commands

### Maven Commands
```bash
# Build the project
mvn clean install

# Compile without running tests
mvn clean compile -DskipTests

# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=CardholderApplyManagerTest

# Run specific test method
mvn test -Dtest=CardholderApplyManagerTest#testCheckAndValidateCardholderForCreation_Success

# Start the application (from server module)
java -jar fenbei-fx-card-server/target/fenbei-fx-card-server.jar
```

### Test Execution Scripts
```bash
# Run complete test suite with detailed reporting
./run-tests.sh

# Run simplified test validation
./run-simple-tests.sh

# Run individual test class
./run-single-test.sh <TestClassName>
```

### Required Environment Setup
- Set `SPRING_PROFILES_ACTIVE=dev` environment variable
- Ensure MySQL 8.0+ and Redis 6.0+ are available
- JDK 8+ and Maven 3.6+ required

## Architecture & Module Structure

### Multi-Module Maven Architecture
```
fenbei-fx-card/
├── fenbei-fx-card-api/          # RPC interface definitions & DTOs
├── fenbei-fx-card-client/       # Client SDK for external systems
├── fenbei-fx-card-common/       # Shared constants, enums, utilities
├── fenbei-fx-card-dao/          # Data access layer (MyBatis)
├── fenbei-fx-card-rpc/          # Dubbo RPC service implementations
├── fenbei-fx-card-server/       # Application bootstrap & configuration
├── fenbei-fx-card-service/      # Core business logic layer
└── fenbei-fx-card-web/          # HTTP API controllers (RESTful)
```

### Key Technology Stack
- **Framework**: Spring Boot 2.x with Apache Dubbo for microservices
- **Database**: MySQL 8.0 with MyBatis Plus ORM
- **Cache**: Redis for distributed caching
- **Message Queue**: Apache Kafka for asynchronous processing
- **Auth**: Custom FinhubAuth framework
- **I18n**: MLS multilingual system support

## Core Business Domain Architecture

### Primary Business Entities & Services
- **Card Management**: `CardService`, `UserCardManager` - Core card operations
- **Cardholder Management**: `CardholderService`, `CardholderApplyService` - KYC and cardholder lifecycle
- **Credit Management**: `CardCreditManagerService`, `UserCardCreditManager` - Credit allocation and monitoring
- **Transaction Processing**: `CardOrderService`, `CardAuthorizeService` - Real-time transaction authorization
- **Notification System**: Various `*NoticeManager` classes for multi-channel notifications

### Manager Pattern Implementation
The codebase extensively uses the Manager pattern for business logic encapsulation:
- `CardholderApplyManager` - Manages cardholder application workflows
- `CardApplyManager` - Handles card application processes  
- `UserCardHomePageManager` - Manages user card dashboard display logic
- `CardApplyNoticeManager` - Orchestrates notification sending

### Integration Patterns
- **Third-party Platforms**: Airwallex, LianLian Pay integration via dedicated service layers
- **External Systems**: User Center, Payment System, Expense Management via RPC clients
- **Async Processing**: Kafka consumers for transaction processing and notifications

## Database & Configuration Patterns

### Entity Naming Convention
- All database entities end with `PO` (Persistent Object)
- DAO classes provide typed access to `*Mapper.xml` MyBatis configurations
- Located in `/src/main/resources/sqlMapper/` directory

### Configuration Management
- Main config: `fenbei-fx-card-server/src/main/resources/application.yaml`
- Environment-specific configs use Spring profiles (`application-{env}.yaml`)
- Custom logback config: `custom_logback.xml`

### Key Configuration Requirements
- Database connection via environment-specific profiles
- Redis connection for caching and session management
- Kafka configuration for async message processing
- Third-party API credentials (Airwallex, LianLian) via secure configuration

## Testing Strategy & Patterns

### Comprehensive Test Suite Architecture
The project implements a multi-layered testing approach:

```
Test Architecture:
├── Unit Tests (CardholderApplyManagerTest, CardApplyManagerTest)
├── Component Tests (UserCardHomePageManagerTest) 
├── Workflow Tests (StepByStepCardCreationTest)
├── Integration Tests (CardholderApplyIntegrationTest)
└── Test Suites (CardholderApplyTestSuite)
```

### Test Execution Patterns
- Use provided shell scripts for consistent test execution
- Mock external dependencies using Mockito
- Test private methods via Spring ReflectionTestUtils when needed
- Comprehensive test coverage targets: >90% line, >85% branch, >95% method

### Critical Test Scenarios
- Step-by-step card creation workflow validation
- Notification mechanism reliability testing
- Status display accuracy for employee portal
- Exception handling and rollback verification

## Development Guidelines

### Code Organization Principles
- Business logic concentrated in `*Manager` classes within service layer
- RPC services act as thin facades over business managers
- Web controllers handle HTTP-specific concerns only
- Extensive use of enums for type safety (`CardStatusEnum`, `CardholderApplyStatusEnum`, etc.)

### Error Handling Patterns
- Custom `FxCardException` for business exceptions
- Centralized error codes in `GlobalCoreResponseCode`
- Consistent response wrapping via `ResponseResult` pattern

### Integration Considerations
- Multi-language support via MLS system - always use I18n utilities
- Async processing patterns for non-critical operations
- Distributed caching strategies using Redis
- Transaction management for complex workflows

## Important File Locations

### Configuration Files
- `fenbei-fx-card-server/src/main/resources/application.yaml` - Main application config
- `fenbei-fx-card-server/src/main/resources/custom_logback.xml` - Logging configuration
- `pom.xml` - Maven dependency and version management

### Key Business Logic
- `fenbei-fx-card-service/src/main/java/com/fenbei/fx/card/service/cardholderapply/manager/` - Cardholder workflow managers
- `fenbei-fx-card-service/src/main/java/com/fenbei/fx/card/service/cardapply/manager/` - Card application managers
- `fenbei-fx-card-service/src/main/java/com/fenbei/fx/card/common/enums/` - Business enums and constants

### Test Resources
- `fenbei-fx-card-service/src/test/java/com/fenbei/fx/card/service/cardholderapply/` - Primary test suite location
- `run-tests.sh`, `run-simple-tests.sh`, `run-single-test.sh` - Test execution scripts

This project represents a mature, production-grade financial services platform with comprehensive testing, multi-platform integration, and enterprise-level architecture patterns.