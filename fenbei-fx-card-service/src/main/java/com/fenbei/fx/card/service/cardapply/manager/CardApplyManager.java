package com.fenbei.fx.card.service.cardapply.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.constants.RedisKeyConstant;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.dao.cardapply.CardApplyDAO;
import com.fenbei.fx.card.dao.cardapply.po.CardApplyPO;
import com.fenbei.fx.card.dao.cardholder.po.CardholderPO;
import com.fenbei.fx.card.dao.cardholderapply.po.CardholderApplyPO;
import com.fenbei.fx.card.dao.cardmail.po.CardMailPO;
import com.fenbei.fx.card.service.card.dto.CardCanOperationDTO;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.CardPageReqDTO;
import com.fenbei.fx.card.service.card.dto.CardPageResDTO;
import com.fenbei.fx.card.service.card.manager.CardManager;
import com.fenbei.fx.card.service.cardapply.converter.CardApplyConverter;
import com.fenbei.fx.card.service.cardapply.domain.CardApplyDO;
import com.fenbei.fx.card.service.cardapply.dto.*;
import com.fenbei.fx.card.service.cardchargingnotice.CardChargingNoticeService;
import com.fenbei.fx.card.service.cardchargingnotice.dto.CardChargingNoticeDTO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderDTO;
import com.fenbei.fx.card.service.cardholder.manager.CardholderManager;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyDTO;
import com.fenbei.fx.card.service.cardholderapply.manager.CardholderApplyManager;
import com.fenbei.fx.card.service.cardmail.dto.CardMailDTO;
import com.fenbei.fx.card.service.cardmail.manager.CardMailManager;
import com.fenbei.fx.card.service.redis.RedissonService;
import com.fenbei.fx.card.util.*;
import com.fenbeitong.dech.api.model.dto.airwallex.*;
import com.fenbeitong.dech.api.model.dto.lianlian.card.req.*;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.*;
import com.fenbeitong.dech.api.service.airwallex.IAirWallexCardService;
import com.fenbeitong.dech.api.service.lianlian.ILianLianCardService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOverseaRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.trade.AcctOverseaCardService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.fxpay.api.enums.FxCompanyAccountSubType;
import com.fenbeitong.fxpay.api.enums.charging.ChargingEventType;
import com.fenbeitong.fxpay.api.interfaces.ICompanyAcctService;
import com.fenbeitong.fxpay.api.vo.CompanyAcctRes;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.fxpay.api.vo.acct.CompanyAcctInfoReq;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyNewDto;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeCompanyDto;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.swift.utils.ObjUtils;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.EncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.fenbei.fx.card.common.constant.CoreConstant.NATION_CODE_CHN;

/**
 * 国际卡操作申请 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Slf4j
@Component
public class CardApplyManager extends BaseManagerImpl<CardApplyDAO, CardApplyPO, CardApplyDTO, CardApplyConverter> {

    /**
     * 银行地址
     */
    @Value("${airwallex.postal-address:}")
    String postalAddress;
    @Value("${airwallex.city-name:}")
    String cityName;
    @Value("${airwallex.post-code:}")
    String postCode;
    @Value("${airwallex.country:}")
    String country;
    @Value("${airwallex.state:}")
    String state ;
    @Value("${airwallex.phone:}")
    String phone;

    @Value("${lianlian.card.template-id:}")
    String lianlianTemplateId;

    public static final String hostAddress = "{\"city\":{\"id\":\"72\",\"name\":\"朝阳区\",\"enName\":\"chao yang qu\",\"crCode\":\"CN\"},\"country\":{\"id\":\"1\",\"name\":\"中国\",\"enName\":\"China\",\"crCode\":\"CN\"},\"state\":{\"id\":\"1000001\",\"name\":\"北京市\",\"enName\":\"Beijing\",\"crCode\":\"CN\"},\"line1\":\"北京市朝阳区霄云里南街9号院5号楼华瑞大厦7层分贝通\",\"postcode\":\"100016\"}";

    @DubboReference
    private ILianLianCardService iLianLianCardService;

    @DubboReference
    private IAirWallexCardService airWallexCardService;

    @DubboReference
    private ICompanyAcctService iCompanyAcctService;

    @DubboReference
    private ICompanyService iCompanyService;
    @DubboReference
    private AcctOverseaCardService acctOverseaCardService;

    @Autowired
    protected RedissonService redissonService;

    @Autowired
    private CardholderManager cardholderManager;

    @Autowired
    private CardChargingNoticeService cardChargingNoticeService;
    @DubboReference
    private IBaseEmployeeExtService baseEmployeeExtService;

    @Autowired
    private CardApplyNoticeManager cardApplyNoticeManager;

    @Autowired
    private CardApplySuccessManager cardApplySuccessManager;

    public static CardApplyManager me() {
        return SpringUtil.getBean(CardApplyManager.class);
    }

    public List<CardApplyListResDTO> list(final CardApplyListReqDTO cardApplyListReqDTO) {
        CardApplyDTO paramsDTO = CardApplyDO.me().buildListParamsDTO(cardApplyListReqDTO);

        List<CardApplyDTO> cardApplyDTOList = super.findList(paramsDTO);

        return CardApplyDO.me().transferCardApplyListResDTOList(cardApplyDTOList);
    }

    public CardApplyListResDTO listOne(final CardApplyListReqDTO cardApplyListReqDTO) {
        CardApplyDTO paramsDTO = CardApplyDO.me().buildListParamsDTO(cardApplyListReqDTO);

        CardApplyDTO cardApplyDTO = super.findOne(paramsDTO);

        return CardApplyDO.me().transferCardApplyListResDTO(cardApplyDTO);
    }

    public Page<CardApplyPageResDTO> pagination(final CardApplyPageReqDTO cardApplyPageReqDTO, final Integer current, final Integer size) {
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getCompanyId())){
            queryWrapper.eq(CardApplyPO.DB_COL_COMPANY_ID, cardApplyPageReqDTO.getCompanyId());
        }

        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getCardPlatform())){
            queryWrapper.eq(CardApplyPO.DB_COL_CARD_PLATFORM, cardApplyPageReqDTO.getCardPlatform());
        }
        if (cardApplyPageReqDTO.getApplyStatus() != null) {
            CardApplyStatusEnum applyStatusEnum = CardApplyStatusEnum.getApplyStatus(cardApplyPageReqDTO.getApplyStatus());
            if (applyStatusEnum != null) {
                queryWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, applyStatusEnum.getStatus());
            }
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getCardPurpose())) {
            queryWrapper.eq(CardApplyPO.DB_COL_CARD_PURPOSE, cardApplyPageReqDTO.getCardPurpose());
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getBankCardNo())) {
            queryWrapper.eq(CardApplyPO.DB_COL_BANK_CARD_NO, cardApplyPageReqDTO.getBankCardNo());
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getPhone())) {
            queryWrapper.eq(CardApplyPO.DB_COL_APPLYER_PHONE, cardApplyPageReqDTO.getPhone());
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getNameOnCard())) {
            //queryWrapper.select(" where CONCAT(applyer_first_name,applyer_last_name)  = " + cardApplyPageReqDTO.getNameOnCard());
            /** 看看结果nameOnCard 是什么格式 不行就换sql where CONCAT(supplier_biz_id,company_supplier_name)  = '17963' */
            queryWrapper.like(CardApplyPO.DB_COL_NAME_ON_CARD, cardApplyPageReqDTO.getNameOnCard());
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getCarPublicTimeStart())) {
            queryWrapper.ge(CardApplyPO.DB_COL_CREATE_TIME, com.fenbeitong.finhub.common.utils.DateUtils.parseTime(cardApplyPageReqDTO.getCarPublicTimeStart()));
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getCarPublicTimeEnd())) {
            queryWrapper.le(CardApplyPO.DB_COL_CREATE_TIME, com.fenbeitong.finhub.common.utils.DateUtils.parseTime(cardApplyPageReqDTO.getCarPublicTimeEnd()));
        }
        queryWrapper.orderByDesc(CardApplyPO.DB_COL_UPDATE_TIME);

        Page<CardApplyDTO> cardApplyDTOPage = super.findPage(queryWrapper, current, size);

        return CardApplyDO.me().transferCardApplyPageResDTOPage(cardApplyDTOPage);
    }

    public String getPhone(String fxCardId){
        CardApplyDTO cardApplyDTO =  queryCardApplyByFxCardId(fxCardId);
        if (StringUtils.isNotBlank(cardApplyDTO.getFxCardholderId())){
            CardholderDTO cardholderDTO = CardholderManager.me().findByCardholderId(cardApplyDTO.getFxCardholderId());
            if (cardholderDTO != null){
                return cardholderDTO.getPhone();
            }
        }else {
            return cardApplyDTO.getApplyerPhone();
        }
        return null;
    }

    public List<String> getFxCardIdByPhoneInfo(String phone){
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_APPLYER_PHONE, phone);
        queryWrapper.eq(CardApplyPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        List<CardApplyDTO> applyDTOS =  this.findList(queryWrapper);
        if (CollectionUtils.isEmpty(applyDTOS)){
            return null;
        }else {
            return applyDTOS.stream().map(CardApplyDTO::getFxCardId).collect(Collectors.toList());
        }
    }

    public Boolean add(final CardApplyAddReqDTO cardApplyAddReqDTO) {
        CardApplyDO.me().checkCardApplyAddReqDTO(cardApplyAddReqDTO);

        CardApplyDTO addCardApplyDTO = CardApplyDO.me().buildAddCardApplyDTO(cardApplyAddReqDTO);

        return super.saveDTO(addCardApplyDTO);
    }

    public Boolean addAllColumn(final CardApplyAddReqDTO cardApplyAddReqDTO) {
        CardApplyDO.me().checkCardApplyAddReqDTO(cardApplyAddReqDTO);

        CardApplyDTO addCardApplyDTO = CardApplyDO.me().buildAddCardApplyDTO(cardApplyAddReqDTO);

        return super.saveAllColumn(addCardApplyDTO);
    }

    public Boolean addBatchAllColumn(final List<CardApplyAddReqDTO> cardApplyAddReqDTOList) {
        CardApplyDO.me().checkCardApplyAddReqDTOList(cardApplyAddReqDTOList);

        List<CardApplyDTO> addBatchCardApplyDTOList = CardApplyDO.me().buildAddBatchCardApplyDTOList(cardApplyAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardApplyDTOList);
    }

    public CardApplyShowResDTO cardApplyDetail(final String applyId) {
        CardApplyDTO cardApplyDTO = queryCardApplyByApplyId(applyId);
        CardApplyShowResDTO resDTO = CardApplyDO.me().transferCardApplyShowResDTO(cardApplyDTO);
        CardCanOperationDTO canOperationDTO = CardStatusEnum.getCarCanOperateStatus(CardApplyStatusEnum.mergeCardStatus(cardApplyDTO.getApplyStatus()));
        if (StringUtils.isNotBlank(resDTO.getCardLimits())) {
            resDTO.setShowLimits(JSONObject.parseArray(resDTO.getCardLimits(), BaseAirwallexRpcDTO.Limit.class));
        }
        resDTO.setOperationDTO(canOperationDTO);

        /*添加持卡人信息*/
        if (CardFormFactorEnum.PHYSICAL.getCode() == resDTO.getCardFormFactor()
            && !CardPlatformEnum.isLianLian(cardApplyDTO.getCardPlatform())
        ) {
            CardholderDTO cardholderDTO = cardholderManager.findByCardholderId(resDTO.getFxCardholderId());
            resDTO.setNationCode(cardholderDTO.getNationCode());
            resDTO.setPhone(cardholderDTO.getPhone());
            resDTO.setPostalAddress(cardholderDTO.getPostalAddress());
            resDTO.setAddress(cardholderDTO.getAddress());
        }
        String cardPlatform = cardApplyDTO.getCardPlatform();
        CardPlatformEnum platform = CardPlatformEnum.getPlatform(cardPlatform);
        resDTO.setCardPlatformIcon(platform.getPlatformIcon());
        resDTO.setCardPlatformName(platform.getName());
        String cardBrand = cardApplyDTO.getCardBrand();
        CardBrandEnum brandAndNoBrand = CardBrandEnum.getBrandAndNoBrand(cardBrand);
        resDTO.setCardBrandIcon(brandAndNoBrand.getBrandIcon());

        return resDTO;
    }

    public List<CardApplyShowResDTO> showByIds(final List<String> ids) {
        CardApplyDO.me().checkIds(ids);

        List<CardApplyDTO> cardApplyDTOList = super.findBatchIds(ids);

        return CardApplyDO.me().transferCardApplyShowResDTOList(cardApplyDTOList);
    }

    public Boolean modify(final CardApplyModifyReqDTO cardApplyModifyReqDTO) {
        CardApplyDO.me().checkCardApplyModifyReqDTO(cardApplyModifyReqDTO);

        CardApplyDTO modifyCardApplyDTO = CardApplyDO.me().buildModifyCardApplyDTO(cardApplyModifyReqDTO);

        return super.modifyById(modifyCardApplyDTO);
    }

    public Boolean modifyAllColumn(final CardApplyModifyReqDTO cardApplyModifyReqDTO) {
        CardApplyDO.me().checkCardApplyModifyReqDTO(cardApplyModifyReqDTO);

        CardApplyDTO modifyCardApplyDTO = CardApplyDO.me().buildModifyCardApplyDTO(cardApplyModifyReqDTO);

        return super.modifyAllColumnById(modifyCardApplyDTO);
    }

    public Boolean removeByParams(final CardApplyRemoveReqDTO cardApplyRemoveReqDTO) {
        CardApplyDO.me().checkCardApplyRemoveReqDTO(cardApplyRemoveReqDTO);

        CardApplyDTO removeCardApplyDTO = CardApplyDO.me().buildRemoveCardApplyDTO(cardApplyRemoveReqDTO);

        return super.remove(removeCardApplyDTO);
    }

    public void dealPendingApplyInfo() {
        /**查询当前apply表中状态为审核中的单据  查询airwallet 终态更新*/
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.PENDING.getStatus());
        List<CardApplyPO> cardApplyPOS = this.list(queryWrapper);
        if (!CollectionUtils.isEmpty(cardApplyPOS)) {
            log.info("待审核卡列表数量为 = {}",cardApplyPOS.size());
            for (CardApplyPO cardApply : cardApplyPOS) {
                if(CardPlatformEnum.isAirwallex( cardApply.getCardPlatform())){
                    CardApplyManager.me().queryPendingResult(cardApply.getBankCardId());
                }else if(CardPlatformEnum.isLianLian(cardApply.getCardPlatform())){
                    CardApplyManager.me().queryPendingResult4LianLian(cardApply);
                }
            }
        } else {
            return;
        }
    }

    public Page<CardApplyPageResDTO> getAllStereoPendingCards( CardApplyPageReqDTO cardApplyPageReqDTO, Integer pageNo, Integer pageSize) {
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getCompanyId())){
            queryWrapper.eq(CardApplyPO.DB_COL_COMPANY_ID, cardApplyPageReqDTO.getCompanyId());
        }
        queryWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.STEREO_PENDING.getStatus());
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getName())) {
            queryWrapper.like(CardApplyPO.DB_COL_NAME_ON_CARD, cardApplyPageReqDTO.getName());
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getPhone())) {
            queryWrapper.like(CardApplyPO.DB_COL_APPLYER_PHONE, cardApplyPageReqDTO.getPhone());
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getBankCardNo())) {
            queryWrapper.like(CardApplyPO.DB_COL_BANK_CARD_NO, cardApplyPageReqDTO.getBankCardNo());
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getCarPublicTimeStart())) {
            queryWrapper.ge(CardApplyPO.DB_COL_CREATE_TIME, com.fenbeitong.finhub.common.utils.DateUtils.parseTime(cardApplyPageReqDTO.getCarPublicTimeStart()));
        }
        if (StringUtils.isNotBlank(cardApplyPageReqDTO.getCarPublicTimeEnd())) {
            queryWrapper.le(CardApplyPO.DB_COL_CREATE_TIME, com.fenbeitong.finhub.common.utils.DateUtils.parseTime(cardApplyPageReqDTO.getCarPublicTimeEnd()));
        }
        if (StringUtils.isNotEmpty(cardApplyPageReqDTO.getCardPlatform())){
            queryWrapper.eq(CardApplyPO.DB_COL_CARD_PLATFORM,cardApplyPageReqDTO.getCardPlatform());
        }
        queryWrapper.orderByDesc(CardApplyPO.DB_COL_CREATE_TIME);
        //赋值邮寄+联系地址 + 转寄标志位
        Page<CardApplyDTO> cardApplyDTOPage = super.findPage(queryWrapper, pageNo, pageSize);
        Page<CardApplyPageResDTO> result = CardApplyDO.me().transferCardApplyPageResDTOPage(cardApplyDTOPage);
        addPostInfo4Query(result);
        return result;
    }


    private void addPostInfo4Query(Page<CardApplyPageResDTO> result){
        result.getRecords().forEach(apply -> {
            apply.setBankCardNo(MaskUtils.leftData(apply.getBankCardNo(),4));
            if (CardFormFactorEnum.PHYSICAL.getCode().equals(apply.getCardFormFactor()) && StringUtils.isNotBlank(apply.getFxCardholderId())){
                CardholderDTO cardholderDTO = CardholderManager.me().findByCardholderId(apply.getFxCardholderId());
                if (cardholderDTO != null){
                    //国内国外都邮寄到分贝通,都需要转寄
                    AddressDto postAddress = JSONObject.parseObject(hostAddress,AddressDto.class);
                    CardMailDTO cardMailDTO = CardMailManager.me().queryOneByfxCardId(apply.getFxCardId());
                    if(ObjectUtils.isNotEmpty(cardMailDTO)&&StringUtils.isNotBlank(cardMailDTO.getAxPostAddress())){
                        apply.setPostalAddressDto(JsonUtils.toObj(cardMailDTO.getAxPostAddress(), AddressDto.class));
                    }else{
                        apply.setPostalAddressDto(postAddress);
                    }
                    //持卡人地址
                    AddressDto addressDto = JsonUtils.toObj(cardholderDTO.getPostalAddress(), AddressDto.class);
                    apply.setUserPostalAddressDto(addressDto);
                    apply.setForwardFlag(1);
                }
            }
            if (CardPlatformEnum.LIANLIAN.getCode().equals(apply.getCardPlatform())){
                if (CardFormFactorEnum.PHYSICAL.getCode().equals(apply.getCardFormFactor()) ){
                    AddressDto addressDto = JsonUtils.toObj(apply.getPostalAddress(), AddressDto.class);
                    if (addressDto!= null) {
                        if (addressDto.getCountry().getCrCode().equals("CN") || addressDto.getCountry().getCrCode().equals("HK")) {
                            apply.setPostalAddressDto(addressDto);
                            apply.setUserPostalAddressDto(addressDto);
                            apply.setForwardFlag(0);
                        } else {
                            AddressDto postAddress = JSONObject.parseObject(hostAddress, AddressDto.class);
                            apply.setPostalAddressDto(postAddress);
                            apply.setUserPostalAddressDto(addressDto);
                            apply.setForwardFlag(1);
                        }
                    }
                }else {
                    apply.setPostalAddressDto(null);
                    apply.setUserPostalAddressDto(null);
                    apply.setForwardFlag(null);
                }
            }
            CompanyNewDto companyNewDto = iCompanyService.queryCompanyNewByCompanyId(apply.getCompanyId());
            apply.setCompanyName(companyNewDto.getCompanyName());
            CardApplyDTO cardApplyDTO =  CardApplyManager.me().queryCardApplyByFxCardId(apply.getFxCardId());
            apply.setApplyerPhone(cardApplyDTO.getApplyerPhone());
        });
    }


    public Page<CardApplyPageResDTO> getAllCardInfo4Stereo( CardApplyPageReqDTO cardPendingPageReqVO, Integer pageNo, Integer pageSize) {
        log.info("stereo 查询卡申请记录  req = {}",JSON.toJSONString(cardPendingPageReqVO));
        String routeTable = routeCardInfo(cardPendingPageReqVO);
        Page<CardApplyPageResDTO> stereoRes = new Page<>();
        if (routeTable != null){
            if (routeTable.equals(QueryTableEnum.CARD_TABLE.getTable())){
                CardPageReqDTO cardPageReqDTO = CopyUtils.convert(cardPendingPageReqVO,CardPageReqDTO.class);
                Page<CardPageResDTO> pageResDTOPage = CardManager.me().pagination(cardPageReqDTO,pageNo,pageSize);
                stereoRes = new Page<>(pageResDTOPage.getCurrent(),pageResDTOPage.getSize(),pageResDTOPage.getTotal());
                if (!CollectionUtils.isEmpty(pageResDTOPage.getRecords())){
                    List<CardApplyPageResDTO> resDTOList = CopyUtils.copyList(pageResDTOPage.getRecords(),CardApplyPageResDTO.class);
                    stereoRes.setRecords(resDTOList);
                }
            }
            if (routeTable.equals(QueryTableEnum.CARD_APPLY_TABLE.getTable())){
                cardPendingPageReqVO.setApplyStatus(cardPendingPageReqVO.getCardStatus());
                stereoRes = pagination(cardPendingPageReqVO,pageNo,pageSize);
                setStatusStrInfo(stereoRes);
            }
        }else {
            stereoRes = pagination(cardPendingPageReqVO,pageNo,pageSize);
            setStatusStrInfo(stereoRes);
        }
        addPostInfo4Query(stereoRes);
        addLine2Info(stereoRes);
        log.info("stereo 查询卡申请记录  req = {} , res = {}",JSON.toJSONString(cardPendingPageReqVO),JSON.toJSONString(stereoRes));
        return stereoRes;
    }

    private void setStatusStrInfo (Page<CardApplyPageResDTO> applyListResDTOS){
        if (!CollectionUtils.isEmpty(applyListResDTOS.getRecords())) {
            for (CardApplyPageResDTO cardApplyListResDTO : applyListResDTOS.getRecords()) {

                CardApplyStatusEnum cardApplyStatusEnum = CardApplyStatusEnum.getRelationEnum(cardApplyListResDTO.getApplyStatus());

                cardApplyListResDTO.setCardStatusStr(cardApplyStatusEnum.getName());
                if (CardApplyStatusEnum.PASS.getStatus().equals(cardApplyStatusEnum.getStatus())){
                    CardDTO byfxCardId = CardManager.me().getByfxCardId(cardApplyListResDTO.getFxCardId());
                    CardStatusEnum cardStatusEnum = CardStatusEnum.getEnumByStatus(byfxCardId.getCardStatus());
                    CardShowStatusEnum cardShowStatusEnum = CardShowStatusEnum.get(cardStatusEnum.getShowStatus());
                    cardApplyListResDTO.setCardStatusStr(cardShowStatusEnum.getName());
                }
                if (CardApplyStatusEnum.FAILED.getStatus().equals(cardApplyStatusEnum.getStatus())){
                    cardApplyListResDTO.setCardStatusStr(CardShowStatusEnum.OPER_FAILED.getName());
                }
                if (CardApplyStatusEnum.REFUSE.getName().equals(cardApplyListResDTO.getCardStatusStr())){
                    cardApplyListResDTO.setCardStatusStr(CardShowStatusEnum.REFUSE.getName());
                }
                cardApplyListResDTO.setCardStatus(cardApplyStatusEnum.getStatus());
            }
        }
    }

    private void addLine2Info(Page<CardApplyPageResDTO> result){
        result.getRecords().forEach(apply -> {
            if (CardFormFactorEnum.PHYSICAL.getCode().equals(apply.getCardFormFactor())){
                //实体卡添加line2序列号信息
                CardMailDTO cardMailDTO = CardMailManager.me().queryOneByfxCardId(apply.getFxCardId());
                if (cardMailDTO != null){
                    apply.setCardMailNo(cardMailDTO.getCardMailNo());
                }
            }
        });
    }


    private String routeCardInfo(CardApplyPageReqDTO cardPendingPageReqVO){
        if (StringUtils.isNotBlank(cardPendingPageReqVO.getBankCardNo())){
            return QueryTableEnum.CARD_TABLE.getTable();
        }
/*        if (StringUtils.isNotBlank(cardPendingPageReqVO.getCarPublicTimeStart()) && StringUtils.isNotBlank(cardPendingPageReqVO.getCarPublicTimeEnd())){
            return QueryTableEnum.CARD_TABLE.getTable();
        }*/
        if (cardPendingPageReqVO.getCardStatus() != null){
            if (CardApplyStatusEnum.getApplyStatus(cardPendingPageReqVO.getCardStatus()) != null) {
                return QueryTableEnum.CARD_APPLY_TABLE.getTable();
            }else {
                return QueryTableEnum.CARD_TABLE.getTable();
            }
        }
        return null;
    }


    public void queryPendingResult(String bankCardId) {
        if(StringUtils.isBlank(bankCardId)){
            log.info("查询卡审核结果 bankCardId为空，无需查询 = {} ",bankCardId);
            return;
        }
        try {
            /**查询当前卡片是否为终态   终态成功：更新覆盖card表    然后更新apply*/
            log.info("查询卡审核结果 req = {} ",bankCardId);
            AirCardDetailsRpcRespDTO detailsRpcRespDTO = airWallexCardService.getCardDetails(bankCardId);
            log.info("查询卡审核结果 req = {} ,res = {}",bankCardId,JSON.toJSONString(detailsRpcRespDTO));
            if (detailsRpcRespDTO != null) {
                /**创建失败  更新失败*/
                if (CardStatusEnum.FAILED.getCode().equals(detailsRpcRespDTO.getCard_status())) {
                    UpdateWrapper<CardApplyPO> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.set(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.FAILED.getStatus());
                    updateWrapper.eq(CardApplyPO.DB_COL_BANK_CARD_ID, bankCardId);
                    updateWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.PENDING.getStatus());
                    this.update(updateWrapper);
                } else if (CardStatusEnum.PENDING.getCode().equals(detailsRpcRespDTO.getCard_status())) {
                    /**审批中*/
                    log.info("CardApplyManager queryPendingResult req = {}", bankCardId);
                } else {
                    CardApplyPO cardApplyPO = buildCardApplyInfo(bankCardId, detailsRpcRespDTO);
                    if (CardStatusEnum.ACTIVE.getCode().equals(detailsRpcRespDTO.getCard_status())) {
                        /**审批成功*/
                        this.saveOrUpdate(cardApplyPO);
                        if ("PHYSICAL".equals(detailsRpcRespDTO.getForm_factor())) {
                            List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(Lists.newArrayList(cardApplyPO.getCreateUserId()), cardApplyPO.getCompanyId());
                            EmployeeCompanyDto employeeOrgUnitDTO = employeeCompanyDtos.get(0);
                            //发送短信
                            String messageSms = "尊敬的客户：您已成功开通实体卡，可在分贝通App查询卡号、CVV和有效期，额度申请成功后即可使用。您可根据需要设置实体卡密码，设置密码需要登录Airwallex网站：https://www.airwallex.com/app/login 。因卡组会根据地区和金额决定刷卡时是否需要密码，为避免无法交易，建议您提前设置密码。";
                            cardApplySuccessManager.sendMsg4CreateCard(employeeOrgUnitDTO.getUserPhone(),"67569ddf59aac212705316a4",messageSms);
                            //发送邮件
                            String message = "您已成功开通实体卡，可在分贝通App查询卡号、CVV和有效期，额度申请成功后即可使用。您可根据需要设置实体卡密码，设置密码需要登录Airwallex网站：https://www.airwallex.com/app/login 。因卡组会根据地区和金额决定刷卡时是否需要密码，为避免无法交易，建议您提前设置密码。";
                            cardApplySuccessManager.sendMail4CreateCard(Collections.singleton(employeeOrgUnitDTO.getUserEmail()),message);
                            //发送PUSH
                            String messagePush = "您已成功开通实体卡，因卡组会根据地区和金额决定刷卡时是否需要密码，为避免无法交易，建议您提前设置密码，操作流程请查看您的短信或者邮件；";
                            cardApplySuccessManager.pushAlert4CreateCard(cardApplyPO.getCompanyId(),cardApplyPO.getCreateUserId(),bankCardId,messagePush,"海外卡开卡成功","系统通知");
                        }
                    }
                    /**更新card 冻结等状态  apply申请表不动*/
                    CardDTO cardDTO = CardManager.me().getByBankCardId(bankCardId);
                    log.info("CardManager.me().getByBankCardId resp{}", JsonUtils.toJson(cardDTO));
                    if(StringUtils.isBlank(cardDTO.getFxCardId())){
                        //首次开卡，计费
                        CardChargingNoticeDTO cardChargingNoticeDTO = buildCardChargingNotice(cardApplyPO);
                        cardChargingNoticeService.saveChargingNotice(cardChargingNoticeDTO);
                    }
                    BeanUtils.copyProperties(cardApplyPO, cardDTO, CopyUtils.getNullPropertyNames(cardApplyPO));
                    cardDTO.setCardStatus(CardStatusEnum.getAirWallexEnum(detailsRpcRespDTO.getCard_status()).getStatus());
                    CardPO po = CopyUtils.convert(cardDTO, CardPO.class);
                    po.setEmployeeId(cardDTO.getCreateUserId());
                    if ("ORGANISATION".equals(detailsRpcRespDTO.getIssue_to())){
                        po.setActiveStatus(CardActiveStatusEnum.NOT_REQUIRE.getStatus());
                    }else {
                        po.setActiveStatus(CardActiveStatusEnum.WAIT.getStatus());
                    }

                    UpdateWrapper<CardPO> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq(CardPO.DB_COL_BANK_CARD_ID,cardApplyPO.getBankCardId());
                    CardManager.me().saveOrUpdate(po ,updateWrapper);

                    //更新邮寄表数据
                    if ("PHYSICAL".equals(detailsRpcRespDTO.getForm_factor())) {
                        updateCardMailInfo(cardApplyPO);
                    }
                }
            }
        } catch (Exception es) {
            log.error("查询审核中单据终态异常 req = {}", bankCardId, es);
        }
    }


    public boolean queryPendingResult4LianLian(CardApplyPO cardApply) {
        try {
            /**查询当前卡片是否为终态   终态成功：更新覆盖card表    然后更新apply*/
            String bankCardId = cardApply.getBankCardId();
            String mchId = cardApply.getBankMchId();
            log.info("查询卡开通结果 mchId = {} , bankCardId = {} ",mchId,bankCardId);
            LianLianCardDetailReqDTO lianLianCardDetailReqDTO = new LianLianCardDetailReqDTO();
            lianLianCardDetailReqDTO.setMchId(mchId);
            lianLianCardDetailReqDTO.setAccountNo(bankCardId);
            LianLianCardDetailRespDTO detailsRpcRespDTO = iLianLianCardService.detail(lianLianCardDetailReqDTO);
            log.info("查询卡开通结果 req = {} ,res = {}",bankCardId,JSON.toJSONString(detailsRpcRespDTO));
            if (detailsRpcRespDTO != null) {
                if(detailsRpcRespDTO.success()){
                    /**创建失败  更新失败*/
                    if (CardStatusEnum.FAILED.getLianLianCode().equals(detailsRpcRespDTO.getAccountStatus())) {
                        UpdateWrapper<CardApplyPO> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.set(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.FAILED.getStatus());
                        updateWrapper.set(CardApplyPO.DB_COL_REFUSE_REASON, detailsRpcRespDTO.getRetMsg());
                        updateWrapper.eq(CardApplyPO.DB_COL_BANK_CARD_ID, bankCardId);
                        updateWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.PENDING.getStatus());
                        this.update(updateWrapper);
                    } else if (CardStatusEnum.PENDING.getLianLianCode().equals(detailsRpcRespDTO.getAccountStatus())) {
                        /**审批中*/
                        log.info("CardApplyManager queryPendingResult req = {}", bankCardId);
                    } else {
                        CardApplyPO cardApplyPO;
                        if (cardApply.getCardFormFactor() == 1){
                            cardApplyPO = buildCardApplyInfo4LianLianPhysical(bankCardId, detailsRpcRespDTO);
                        }else {
                            cardApplyPO = buildCardApplyInfo4LianLian(bankCardId, detailsRpcRespDTO);
                        }
                        if (CardStatusEnum.ACTIVE.getLianLianCode().equals(detailsRpcRespDTO.getAccountStatus())) {
                            String vccResult4LianLian = CardApplyManager.me().queryVCCResult4LianLian(cardApply);
                            cardApplyPO.setCardCvv(vccResult4LianLian);
                            /**审批成功*/
                            this.saveOrUpdate(cardApplyPO);
                        }
                        /**更新card 冻结等状态  apply申请表不动*/
                        CardDTO cardDTO = CardManager.me().getByBankCardId(bankCardId);
                        log.info("CardManager.me().getByBankCardId resp{}", JsonUtils.toJson(cardDTO));
                        BeanUtils.copyProperties(cardApplyPO, cardDTO, CopyUtils.getNullPropertyNames(cardApplyPO));
                        cardDTO.setCardStatus(CardStatusEnum.getLianLianCodeEnum(detailsRpcRespDTO.getAccountStatus()).getStatus());
                        if (cardApply.getCardFormFactor() == 1){
                            cardDTO.setActiveStatus(1);
                        }
                        CardPO po = CopyUtils.convert(cardDTO, CardPO.class);
                        po.setEmployeeId(cardDTO.getCreateUserId());
                        po.setBalance(BigDecimalUtils.yuan2fen(new BigDecimal(detailsRpcRespDTO.getAmtBalaval())));
                        po.setFreezenBalance(BigDecimalUtils.yuan2fen(new BigDecimal(detailsRpcRespDTO.getAmtBalfrz())));
                        UpdateWrapper<CardPO> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.eq(CardPO.DB_COL_BANK_CARD_ID,cardApplyPO.getBankCardId());
                        CardManager.me().saveOrUpdate(po ,updateWrapper);
                        return true;
                    }
                }else{
                    //{"retCode":"CM1002","retMsg":"账户不存在"},连连还没创建成功，继续轮训
                    if("CM1002".equalsIgnoreCase(detailsRpcRespDTO.getRetCode())){
                        /**审批中*/
                        log.info("CardApplyManager queryPendingResult req = {}, resp={}", bankCardId, JsonUtils.toJson(detailsRpcRespDTO));
                    }else{
                        UpdateWrapper<CardApplyPO> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.set(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.FAILED.getStatus());
                        updateWrapper.set(CardApplyPO.DB_COL_REFUSE_REASON, detailsRpcRespDTO.getRetMsg());
                        updateWrapper.eq(CardApplyPO.DB_COL_BANK_CARD_ID, bankCardId);
                        updateWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.PENDING.getStatus());
                        this.update(updateWrapper);
                    }
                }

            }
        } catch (Exception es) {
            log.error("查询审核中单据终态异常 req = {}", JsonUtils.toJson(cardApply), es);
        }
        return false;
    }

    /**
     * 异步获取CVV
     * @param cardApply
     */
    public String  queryVCCResult4LianLian(CardApplyPO cardApply){
            try {
                /**查询当前卡片的CVV  更新覆盖card表  然后更新apply*/
                String bankCardId = cardApply.getBankCardId();
                String mchId = cardApply.getBankMchId();
                log.info("查询卡CVV结果 mchId = {} , bankCardId = {} ",mchId,bankCardId);
                LianLianCardVccQueryReqDTO cardVccQueryReqDTO = new LianLianCardVccQueryReqDTO();
                cardVccQueryReqDTO.setMchId(mchId);
                cardVccQueryReqDTO.setAccountNo(bankCardId);
                LianLianCardVccQueryRespDTO vccQueryRespDTO = iLianLianCardService.queryVccInfo(cardVccQueryReqDTO);
                log.info("查询卡CVV结果 req = {} ,res = {}",bankCardId,JSON.toJSONString(vccQueryRespDTO));
                if (vccQueryRespDTO != null) {
                    if(vccQueryRespDTO.success()) {
                         return EncodeUtils.decodeBase64(vccQueryRespDTO.getSecurityCode());
                    }
                }else{
                    log.info("查询卡CVV结果失败 req = {} ,res = {}",bankCardId,JSON.toJSONString(vccQueryRespDTO));
                    return null;
                }
            }catch (Exception es) {
                log.error("查询卡CVV结果异常 req = {}", JsonUtils.toJson(cardApply), es);
            }
        return null;
    }

    private void updateCardMailInfo (CardApplyPO cardApplyPO) {
        CardMailDTO cardMailDTO = CardMailManager.me().queryOneByfxCardId(cardApplyPO.getFxCardId());
        cardMailDTO.setCardCvv(cardApplyPO.getCardCvv());
        cardMailDTO.setCardExpiryMonth(cardApplyPO.getCardExpiryMonth());
        cardMailDTO.setCardExpiryYear(cardApplyPO.getCardExpiryYear());
        cardMailDTO.setBankCardId(cardApplyPO.getBankCardId());
        cardMailDTO.setBankCardNo(cardApplyPO.getBankCardNo());
        cardMailDTO.setCardIssuanceTime(cardApplyPO.getCardPublicTime());
        if (cardMailDTO.getForwardFlag() == 0) { // 如果不需要转寄  那么审批完成及标记为寄出
            cardMailDTO.setForwardStatus(ForwardStatusEnum.SEND_OUT.getCode());
        }
        CardMailPO cardMailPO = CopyUtils.convert(cardMailDTO, CardMailPO.class);
        UpdateWrapper<CardMailPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardMailPO.DB_COL_FX_CARD_ID, cardMailDTO.getFxCardId());
        CardMailManager.me().update(cardMailPO, updateWrapper);
    }
    private void updateCardMailInfo4LianLian(CardApplyPO cardApplyPO) {
        CardMailDTO cardMailDTO = CardMailManager.me().queryOneByfxCardId(cardApplyPO.getFxCardId());
        cardMailDTO.setCardCvv(cardApplyPO.getCardCvv());
        cardMailDTO.setCardExpiryMonth(cardApplyPO.getCardExpiryMonth());
        cardMailDTO.setCardExpiryYear(cardApplyPO.getCardExpiryYear());
        cardMailDTO.setBankCardId(cardApplyPO.getBankCardId());
        cardMailDTO.setBankCardNo(cardApplyPO.getBankCardNo());
        cardMailDTO.setCardIssuanceTime(cardApplyPO.getCardPublicTime());
        if (cardMailDTO.getForwardFlag() == 0) { // 如果不需要转寄  那么审批完成及标记为寄出
            cardMailDTO.setForwardStatus(ForwardStatusEnum.SEND_OUT.getCode());
        }
        CardMailPO cardMailPO = CopyUtils.convert(cardMailDTO, CardMailPO.class);
        UpdateWrapper<CardMailPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardMailPO.DB_COL_FX_CARD_ID, cardMailDTO.getFxCardId());
        CardMailManager.me().update(cardMailPO, updateWrapper);
    }

    private CardChargingNoticeDTO buildCardChargingNotice(CardApplyPO cardApplyPO) {
        CardChargingNoticeDTO cardChargingNoticeDTO = new CardChargingNoticeDTO();
        cardChargingNoticeDTO.setRequestId(cardApplyPO.getApplyId());
        cardChargingNoticeDTO.setEmployeeId(cardApplyPO.getCreateUserId());
        cardChargingNoticeDTO.setCompanyId(cardApplyPO.getCompanyId());
        cardChargingNoticeDTO.setFxCardId(cardApplyPO.getFxCardId());
        if(CardFormFactorEnum.isPhysical(cardApplyPO.getCardFormFactor())){
            cardChargingNoticeDTO.setEventType(ChargingEventType.OPEN_PHYSICAL_FXCARD.getCode());
        }else{
            cardChargingNoticeDTO.setEventType(ChargingEventType.OPEN_VIRTUAL_FXARD.getCode());
        }
        cardChargingNoticeDTO.setTradeAmount(null);
        cardChargingNoticeDTO.setCardStatus(CardStatusEnum.ACTIVE.getStatus());
        return cardChargingNoticeDTO;
    }


    private CardApplyPO buildCardApplyInfo(String bankCardId, AirCardDetailsRpcRespDTO dto) {
        CardApplyDTO cardApplyDTO = queryCardApplyByBankCardId(bankCardId , CardApplyStatusEnum.PENDING.getStatus());
        cardApplyDTO.setApplyStatus(CardApplyStatusEnum.PASS.getStatus());
        cardApplyDTO.setBankCardId(dto.getCard_id());
        cardApplyDTO.setBankCardNo(dto.getCard_number());
        cardApplyDTO.setCardIssueTo("ORGANISATION".equals(dto.getIssue_to()) ? 1 : 2);
        cardApplyDTO.setCardFormFactor("PHYSICAL".equals(dto.getForm_factor()) ? 1 : 2);
        cardApplyDTO.setCardBrand(dto.getBrand());
        cardApplyDTO.setCardPublicTime(new Date());
        cardApplyDTO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
        BaseAirwallexRpcDTO.TransactionLimits limits = dto.getAuthorization_controls().getTransaction_limits();
        cardApplyDTO.setCurrency(limits.getCurrency());
        cardApplyDTO.setCardLimits(JSON.toJSONString(limits.getLimits()));

        /**查询卡敏感信息*/
        //if (CardFormFactorEnum.VIRTUAL.getCode().equals(cardApplyDTO.getCardFormFactor())){
        try {
            AirGetSensitiveCardDetailsRpcRespDTO airGetSensitiveCardDetailsRpcRespDTO = airWallexCardService.getSensitiveCardDetails(bankCardId);
            log.info("getSensitiveCardDetails req = {},res = {}",bankCardId,JSON.toJSONString(airGetSensitiveCardDetailsRpcRespDTO));
            cardApplyDTO.setCardCvv(airGetSensitiveCardDetailsRpcRespDTO.getCvv());
            cardApplyDTO.setBankCardNo(airGetSensitiveCardDetailsRpcRespDTO.getCard_number());
            //cardApplyDTO.setNameOnCard(airGetSensitiveCardDetailsRpcRespDTO.getName_on_card());
            cardApplyDTO.setCardExpiryMonth(airGetSensitiveCardDetailsRpcRespDTO.getExpiry_month());
            cardApplyDTO.setCardExpiryYear(airGetSensitiveCardDetailsRpcRespDTO.getExpiry_year());
        }catch (Exception e){
            log.warn("getSensitiveCardDetails 异常 bankCardId = {}" ,bankCardId ,e);
            throw new FxCardException(GlobalCoreResponseCode.CARD_QUERY_ERROR);
        }
        //}

        CardApplyPO po = new CardApplyPO();
        BeanUtils.copyProperties(cardApplyDTO, po);

        return po;
    }

    private CardApplyPO buildCardApplyInfo4LianLian(String bankCardId, LianLianCardDetailRespDTO detailsRpcRespDTO ) {
        CardApplyDTO cardApplyDTO = queryCardApplyByBankCardId(bankCardId , CardApplyStatusEnum.PENDING.getStatus());
        cardApplyDTO.setApplyStatus(CardApplyStatusEnum.PASS.getStatus());
        cardApplyDTO.setRefuseReason("恭喜您！开卡成功");
        cardApplyDTO.setBankCardId(detailsRpcRespDTO.getAccountNo());
        cardApplyDTO.setBankCardNo(detailsRpcRespDTO.getBankCardNo());
        cardApplyDTO.setCardIssueTo(CardIssueToEnum.ORGANISATION.getCode());
        cardApplyDTO.setCardFormFactor(CardFormFactorEnum.VIRTUAL.getCode());
        cardApplyDTO.setCardBrand(CardBrandEnum.MASTER.getCode());
        cardApplyDTO.setCardPublicTime(ObjUtils.isNull(detailsRpcRespDTO.getCreateTime())?new Date():DateUtils.parseDate(detailsRpcRespDTO.getCreateTime()));
        cardApplyDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
        cardApplyDTO.setCurrency(CardPlatformCaseEnum.LIANLIAN.getCurrencyEnum().getCurrencyCode());
        cardApplyDTO.setCardLimits("");
        String expireTime = detailsRpcRespDTO.getExpireTime();
        Date expireDate = DateUtils.parseDate(expireTime);
        cardApplyDTO.setCardExpiryYear(String.valueOf(DateUtils.getYear(expireDate)));
        cardApplyDTO.setCardExpiryMonth(String.valueOf(DateUtils.getMonth(expireDate)));

        CardApplyPO po = new CardApplyPO();
        BeanUtils.copyProperties(cardApplyDTO, po);

        return po;
    }

    private CardApplyPO buildCardApplyInfo4LianLianPhysical(String bankCardId, LianLianCardDetailRespDTO detailsRpcRespDTO ) {
        CardApplyDTO cardApplyDTO = queryCardApplyByBankCardId(bankCardId , CardApplyStatusEnum.WAIT_ACTIVE.getStatus());
        cardApplyDTO.setApplyStatus(CardApplyStatusEnum.PASS.getStatus());
        cardApplyDTO.setRefuseReason("恭喜您！开卡成功");
        cardApplyDTO.setBankCardId(detailsRpcRespDTO.getAccountNo());
        cardApplyDTO.setBankCardNo(detailsRpcRespDTO.getBankCardNo());
        cardApplyDTO.setCardIssueTo(CardIssueToEnum.INDIVIDUAL.getCode());
        cardApplyDTO.setCardFormFactor(CardFormFactorEnum.PHYSICAL.getCode());
        cardApplyDTO.setCardBrand(CardBrandEnum.MASTER.getCode());
        cardApplyDTO.setCardPublicTime(ObjUtils.isNull(detailsRpcRespDTO.getCreateTime())?new Date():DateUtils.parseDate(detailsRpcRespDTO.getCreateTime()));
        cardApplyDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
        cardApplyDTO.setCurrency(CardPlatformCaseEnum.LIANLIAN.getCurrencyEnum().getCurrencyCode());
        cardApplyDTO.setCardLimits("");
        String expireTime = detailsRpcRespDTO.getExpireTime();
        Date expireDate = DateUtils.parseDate(expireTime);
        cardApplyDTO.setCardExpiryYear(String.valueOf(DateUtils.getYear(expireDate)));
        cardApplyDTO.setCardExpiryMonth(String.valueOf(DateUtils.getMonth(expireDate)));

        CardApplyPO po = new CardApplyPO();
        BeanUtils.copyProperties(cardApplyDTO, po);

        return po;
    }


    public CardApplyDTO queryCardApplyByBankCardId(String bankCardId ,Integer status) {
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_BANK_CARD_ID, bankCardId);
        if (status != null){
            queryWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, status );
        }
        queryWrapper.eq(CardApplyPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findOne(queryWrapper);
    }

    public CardApplyDTO queryCardApplyByFxCardId(String fxCardId ) {
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_FX_CARD_ID, fxCardId);
        queryWrapper.eq(CardApplyPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findOne(queryWrapper);
    }

    public CardApplyDTO queryCardApplyByApplyId(String applyId) {
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_APPLY_ID, applyId);
        queryWrapper.eq(CardApplyPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findOne(queryWrapper);
    }

    @Override
    protected CardApplyPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardApplyPO();
        }

        return BeanUtil.toBean(map, CardApplyPO.class);
    }

    @Override
    protected CardApplyDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardApplyDTO();
        }

        return BeanUtil.toBean(map, CardApplyDTO.class);
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public CardApplyShowResDTO createCardApply(CreateCardApplyReqDTO createCardApplyReqDTO) {

        String employeeId = createCardApplyReqDTO.getEmployeeId();
        String companyId = createCardApplyReqDTO.getCompanyId();
        //1.幂等加锁
        String lockKey = RedisKeyConstant.CREATE_CARD_APPLY_LOCK + employeeId;
        CardholderDTO cardHolder = null;
        try {
            boolean b = redissonService.tryLock(lockKey);
            if (!b) {
                throw new FxCardException(GlobalCoreResponseCode.SERVER_REQUEST_FAST);
            }
            //2.参数校验
            createCardApplyParamVerification(createCardApplyReqDTO);

            CardPlatformCaseEnum cardPlatformCaseCode = CardPlatformCaseEnum.getEnumByCardPlatformCode(createCardApplyReqDTO.getCardPlatform());
            //3.检查企业余额是否满足创建卡
            CompanyAcctBaseDTO companyAcctBaseDTO = checkAccount(false , companyId,cardPlatformCaseCode);
            createCardApplyReqDTO.setCompanyAccountId(companyAcctBaseDTO.getAccountId());
            createCardApplyReqDTO.setBankMchId(companyAcctBaseDTO.getBankAcctId());

            //4.Aw开通个人账户校验持卡人信息
            if(CardPlatformEnum.isAirwallex(createCardApplyReqDTO.getCardPlatform())){
                if (CardFormFactorEnum.PHYSICAL.getCode().equals(createCardApplyReqDTO.getCardFormFactor())){
                    cardHolder = checkCardHolderById(createCardApplyReqDTO.getFxCardholderId());
                }
            }

            //5.开卡申请入库
            CardApplyShowResDTO showResDTO =  insertCreateCardApply(createCardApplyReqDTO, cardHolder);
            // 发送开卡申请消息通知
            try {
                cardApplyNoticeManager.sendNoticeMsgForCardApply(showResDTO);
            } catch (Exception e) {
                log.error("发送开卡申请消息通知失败，applyId = {}", showResDTO.getApplyId(), e);
            }
            return cardApplyDetail(showResDTO.getApplyId());

        } catch (FinhubException fe) {
            log.warn("createCardApply exception:{} ", fe);
            throw fe;
        } catch (Exception e) {
            log.error("createCardApply exception:{} ", e);
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION);
        } finally {
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                FinhubLogger.error("createCardApply unlock|err=", e);
            }
        }
    }

    private CompanyAcctBaseDTO checkAccount(Boolean checkBalance , String companyId, CardPlatformCaseEnum cardPlatformCaseEnum){
        CardPlatformEnum cardPlatformEnum = cardPlatformCaseEnum.getCardPlatformEnum();
        if(CardPlatformEnum.isLianLian(cardPlatformEnum.getCode())){
            return checkAccount4LianLian(checkBalance, companyId,cardPlatformCaseEnum);
        }else if(CardPlatformEnum.isAirwallex(cardPlatformEnum.getCode())){
            return checkAccount4AirWallex(checkBalance, companyId,cardPlatformCaseEnum);
        }else{
            log.error("createCardApply 暂不支持的渠道类型，cardPlatformEnum:{} ",JSON.toJSONString(cardPlatformEnum));
            throw new FxCardException(GlobalCoreResponseCode.COMPANY_ACCOUNT_NOT_EXIT);
        }

    }


    private CompanyAcctBaseDTO checkAccount4AirWallex(Boolean checkBalance , String companyId, CardPlatformCaseEnum cardPlatformCaseEnum) {
        // 检查企业账户是否存在
        CompanyAcctInfoReq req = new CompanyAcctInfoReq();
        req.setCompanyId(companyId);
        req.setChannel(cardPlatformCaseEnum.getFxAcctChannelEnum());
        req.setAccountSubType(FxCompanyAccountSubType.PETTY);
        req.setCurrency(cardPlatformCaseEnum.getCurrencyEnum());//美元主账户
        ResponseVo<List<CompanyAcctRes>> responseVo = iCompanyAcctService.queryCompanyAcctInfo(req);
        List<CompanyAcctRes> data = responseVo.getData();
        if (CollectionUtils.isEmpty(data)) {
            log.info("checkAccount  req = {} res = {}",companyId , data);
            throw new FxCardException(GlobalCoreResponseCode.COMPANY_ACCOUNT_NOT_EXIT);
        }
        CompanyAcctRes acct = data.get(0);
        // 检查企业账户状态
        if (acct.getStatus() == 2) {
            log.info("checkAccount  req = {}, error = {}",companyId , GlobalCoreResponseCode.COMPANY_ACCOUNT_STATUS_ERROR.getMessage());
            throw new FxCardException(GlobalCoreResponseCode.COMPANY_ACCOUNT_STATUS_ERROR);
        }
        // 检查企业余额是否满足创建卡
        if (checkBalance && acct.getAvailableBalance().compareTo(cardPlatformCaseEnum.getApplyLimitAmount()) < 0) {
            throw new FxCardException(GlobalCoreResponseCode.COMPANY_ACCOUNT_BALANCE_NOT_ENOUGH);
        }
        CompanyAcctBaseDTO companyAcctBaseDTO = new CompanyAcctBaseDTO();
        companyAcctBaseDTO.setAccountId(acct.getAccountId());
        companyAcctBaseDTO.setCompanyId(companyId);
        companyAcctBaseDTO.setAccountSubType(acct.getAccountSubType());
        companyAcctBaseDTO.setBankName(cardPlatformCaseEnum.getCardPlatformEnum().getCode());
        return companyAcctBaseDTO;
    }


    private CompanyAcctBaseDTO checkAccount4LianLian(Boolean checkBalance , String companyId, CardPlatformCaseEnum cardPlatformCaseEnum) {
        // 检查企业账户是否存在
        CompanyAcctBaseDTO companyAcctBaseDTO;
        try {
            List<AcctOverseaRespDTO> acctOverseaRespDTOS = acctOverseaCardService.queryCompanyOverseaAcctInfor(companyId,cardPlatformCaseEnum.getCardPlatformEnum().getCode(),cardPlatformCaseEnum.getCardPlatformEnum().getCode());
            if(!ObjUtils.isEmpty(acctOverseaRespDTOS)){
                AcctOverseaRespDTO acctOverseaRespDTO = acctOverseaRespDTOS.get(0);
                // 检查企业卡状态是否满足创建卡
                if(FundAcctActStatusEnum.isAct(acctOverseaRespDTO.getActiveStatus())
                    && FundAcctStatusEnum.isEnable(acctOverseaRespDTO.getAccountStatus())){
                    // 连连不检查企业余额
//                    if (checkBalance) {
//                        log.info("checkAccount4LianLian  req = {}, error = {}",companyId , GlobalCoreResponseCode.COMPANY_ACCOUNT_BALANCE_NOT_ENOUGH.getMessage());
//                        throw new FxCardException(GlobalCoreResponseCode.COMPANY_ACCOUNT_BALANCE_NOT_ENOUGH);
//                    }
                    companyAcctBaseDTO=new CompanyAcctBaseDTO();
                    companyAcctBaseDTO.setAccountId(acctOverseaRespDTO.getAccountId());
                    companyAcctBaseDTO.setCompanyId(companyId);
                    companyAcctBaseDTO.setAccountSubType(acctOverseaRespDTO.getAccountSubType());
                    companyAcctBaseDTO.setBankAcctId(acctOverseaRespDTO.getBankAcctId());
                    companyAcctBaseDTO.setBankAccountNo(acctOverseaRespDTO.getBankAccountNo());
                    companyAcctBaseDTO.setBankName(cardPlatformCaseEnum.getCardPlatformEnum().getCode());
                    return companyAcctBaseDTO;
                }else{
                    log.info("checkAccount4LianLian  req = {}, error = {}",companyId , GlobalCoreResponseCode.COMPANY_ACCOUNT_STATUS_ERROR.getMessage());
                    throw new FxCardException(GlobalCoreResponseCode.COMPANY_ACCOUNT_STATUS_ERROR);
                }
            }else{
                log.info("checkAccount4LianLian  req = {} res = {}",companyId , JSON.toJSONString(acctOverseaRespDTOS));
                throw new FxCardException(GlobalCoreResponseCode.COMPANY_ACCOUNT_NOT_EXIT);
            }
        }catch (Exception e){
            FinhubLogger.error("checkAccount4LianLian error:{}",e.getMessage());
            throw new FxCardException(GlobalCoreResponseCode.COMPANY_ACCOUNT_NOT_EXIT);
        }
    }

    private CardApplyShowResDTO insertCreateCardApply(CreateCardApplyReqDTO reqDTO, CardholderDTO cardHolder) {
        log.info("CardApplyManager insertCreateCardApply req = {}", JSON.toJSONString(reqDTO) );

        CardApplyPO cardApplyPO = new CardApplyPO();

        cardApplyPO.setNameOnCard(reqDTO.getApplyerLastName() + reqDTO.getApplyerFirstName());
        if (StringUtils.isNotBlank(reqDTO.getApplyId())){
            cardApplyPO.setApplyId(reqDTO.getApplyId());

            CardApplyDTO cardApplyDTO = queryCardApplyByApplyId(reqDTO.getApplyId());
            cardApplyPO.setNameOnCard(cardApplyDTO.getNameOnCard());
            if (!CardApplyStatusEnum.getCanReSubmitStatus().contains(cardApplyDTO.getApplyStatus())){
                throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO);
            }
        }else {
            cardApplyPO.setApplyId(BizIdUtils.getCardApplyIdPre());
            cardApplyPO.setFxCardId(BizIdUtils.getCardIdPre());
        }
        if (!CollectionUtils.isEmpty(reqDTO.getShowLimits())){
            cardApplyPO.setCardLimits(JSON.toJSONString(reqDTO.getShowLimits()));
        }else {
            List<BaseAirwallexRpcDTO.Limit> limitList = new ArrayList<>();
            BaseAirwallexRpcDTO.Limit limit = new BaseAirwallexRpcDTO.Limit();
            limit.setAmount(50000L);
            limit.setInterval(TradeLimitEnum.PER_TRANSACTION.getCode());
            limitList.add(limit);
            cardApplyPO.setCardLimits(JSON.toJSONString(limitList));
        }
        cardApplyPO.setNationCode(reqDTO.getNationCode());
        if (StringUtils.isBlank(reqDTO.getNationCode())){
            cardApplyPO.setNationCode(NATION_CODE_CHN);
        }
        cardApplyPO.setCompanyAccountId(reqDTO.getCompanyAccountId());
        cardApplyPO.setApplyType(ApplyTypeEnum.ADD.getCode());
        cardApplyPO.setApplyStatus(CardApplyStatusEnum.WAITCHECK.getStatus());
        if (CardFormFactorEnum.PHYSICAL.getCode().equals(reqDTO.getCardFormFactor())) {
            cardApplyPO.setCardIssueTo(CardIssueToEnum.INDIVIDUAL.getCode());
            cardApplyPO.setCardFormFactor(CardFormFactorEnum.PHYSICAL.getCode());
            //连连实体卡的信息
            if (CardPlatformEnum.isLianLian(reqDTO.getCardPlatform())){
                cardApplyPO.setFxCardholderId(reqDTO.getFxCardholderId());
                cardApplyPO.setApplyerPhone(reqDTO.getPhone());
                cardApplyPO.setApplyerFirstName(reqDTO.getApplyerFirstName());
                cardApplyPO.setApplyerLastName(reqDTO.getApplyerLastName());
                cardApplyPO.setNameOnCard(reqDTO.getApplyerLastName() + reqDTO.getApplyerFirstName());
            }
        } else if (CardFormFactorEnum.VIRTUAL.getCode().equals(reqDTO.getCardFormFactor())) {
            cardApplyPO.setCardIssueTo(CardIssueToEnum.ORGANISATION.getCode());
            cardApplyPO.setCardFormFactor(CardFormFactorEnum.VIRTUAL.getCode());
        } else {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        String  cardPlatform= reqDTO.getCardPlatform();
        if(StringUtils.isNotBlank(cardPlatform)){
            cardApplyPO.setCardPlatform(cardPlatform);
            if(CardPlatformEnum.isLianLian(cardPlatform)){
                cardApplyPO.setCardBrand(CardBrandEnum.MASTER.getCode());
                cardApplyPO.setCurrency(CardPlatformCaseEnum.LIANLIAN.getCurrencyEnum().getCurrencyCode());
            }else if(CardPlatformEnum.isAirwallex(cardPlatform)){
                cardApplyPO.setCardBrand(CardBrandEnum.VISA.getCode());
                cardApplyPO.setCurrency(CardPlatformCaseEnum.AIRWALLEX.getCurrencyEnum().getCurrencyCode());
            }
        }else{
            cardApplyPO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
            cardApplyPO.setCardBrand(CardBrandEnum.VISA.getCode());
            cardApplyPO.setCurrency(CurrencyEnum.USD.getCurrencyCode());
        }
        cardApplyPO.setApplyerPhone(reqDTO.getApplyerPhone());
        cardApplyPO.setApplyerFirstName(reqDTO.getApplyerFirstName());
        cardApplyPO.setApplyerLastName(reqDTO.getApplyerLastName());
        cardApplyPO.setApplyerEmail(reqDTO.getApplyerEmail());
        if (cardHolder != null){
            cardApplyPO.setFxCardholderId(cardHolder.getFxCardholderId());
            cardApplyPO.setApplyerPhone(cardHolder.getPhone());
            cardApplyPO.setApplyerFirstName(cardHolder.getFirstName());
            cardApplyPO.setApplyerLastName(cardHolder.getLastName());
            cardApplyPO.setNameOnCard(cardHolder.getLastName() + cardHolder.getFirstName());
        }

        cardApplyPO.setCardPurpose(reqDTO.getCardPurpose());
        cardApplyPO.setCreateUserId(reqDTO.getEmployeeId());
        cardApplyPO.setCompanyId(reqDTO.getCompanyId());
        cardApplyPO.setBankMchId(reqDTO.getBankMchId());
        cardApplyPO.setPostalAddress(JsonUtils.toJson(reqDTO.getPostalAddressDto()));
        UpdateWrapper<CardApplyPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardApplyPO.DB_COL_APPLY_ID,cardApplyPO.getApplyId());
        log.info("CardApplyManager insertCreateCardApply req = {} ,cardApplyPO = {} ", reqDTO , JSON.toJSONString(cardApplyPO) );
        saveOrUpdate(cardApplyPO,updateWrapper);

        return converter.convertToCardApplyShowResDTO(converter.poToDTO(cardApplyPO));
    }

    private CardholderDTO checkCardHolderById(String fxCardholderId) {
        QueryWrapper<CardholderPO> cardholderQueryWrapper = new QueryWrapper<>();
        cardholderQueryWrapper.eq(CardholderPO.DB_COL_FX_CARDHOLDER_ID, fxCardholderId);
        cardholderQueryWrapper.eq(CardholderPO.DB_COL_HOLDER_STATUS,CardholderStatusEnum.EFFECTIVE.getKey());
        CardholderDTO cardholder = CardholderManager.me().findOne(cardholderQueryWrapper);
        if (ObjectUtils.isEmpty(cardholder)) {

            QueryWrapper<CardholderApplyPO> cardholderApplyDTOQueryWrapper = new QueryWrapper<>();
            cardholderApplyDTOQueryWrapper.eq(CardholderApplyPO.DB_COL_FX_CARDHOLDER_ID, fxCardholderId);
            List<CardholderApplyDTO> cardholderApplyDTOS = CardholderApplyManager.me().findList(cardholderApplyDTOQueryWrapper);
            if (CollectionUtils.isEmpty(cardholderApplyDTOS)) {
                throw new FxCardException(GlobalCoreResponseCode.CARD_HOLDER_NOT_EXIT);
            }else {
                throw new FxCardException(GlobalCoreResponseCode.CARDHOLDERAPPLY_UPDATEING_TOAST);
            }

        }
        return cardholder;
    }

    private void createCardApplyParamVerification(CreateCardApplyReqDTO reqDto) {

        if (ObjectUtils.isEmpty(reqDto.getCardFormFactor())) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "cardFormFactor "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
        }
        // 实体卡必须有持卡人(连连不需要)
        if (CardFormFactorEnum.PHYSICAL.getCode().equals(reqDto.getCardFormFactor()) && !CardPlatformEnum.isLianLian(reqDto.getCardPlatform())) {
            if (StringUtils.isEmpty(reqDto.getFxCardholderId())) {
                throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "fxCardholderId "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
            }
        }else {
            //虚拟卡需要出惹怒手机号姓名   实体卡从持卡人提取
            if (StringUtils.isEmpty(reqDto.getApplyerPhone())) {
                if (StringUtils.isNotBlank(reqDto.getPhone()+"")){
                    reqDto.setApplyerPhone(reqDto.getPhone()+"");
                }else {
                    throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "applyerPhone "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
                }
            }
            if (StringUtils.isEmpty(reqDto.getApplyerFirstName())) {
                throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "applyerFirstName "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
            }
            if (StringUtils.isEmpty(reqDto.getApplyerLastName())) {
                throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "applyerLastName "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
            }
        }
        if (StringUtils.isEmpty(reqDto.getCardPurpose())) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "cardPurpose "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
        }
    }

    public List<CardApplyDTO> userCreateProgressApplys(String employeeId, String companyId) {
        if (StringUtils.isAnyBlank(employeeId, companyId)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.eq(CardApplyPO.DB_COL_CREATE_USER_ID, employeeId);
        queryWrapper.eq(CardApplyPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        queryWrapper.in(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.getProgressApplyStatus());
        //queryWrapper.eq(CardApplyPO.DB_COL_APPLY_TYPE, ApplyTypeEnum.ADD.getCode());
        queryWrapper.orderByDesc(CardApplyPO.DB_COL_CREATE_TIME);
        return super.findList(queryWrapper);

    }

    /**
     * 排除银行成功的申请单
     * @param employeeId
     * @param companyId
     * @return
     */
    public List<CardApplyDTO> userApplysExcludeSuc(String employeeId, String companyId) {
        if (StringUtils.isAnyBlank(employeeId, companyId)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.eq(CardApplyPO.DB_COL_CREATE_USER_ID, employeeId);
        queryWrapper.eq(CardApplyPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        queryWrapper.ne(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.PASS.getStatus());
        queryWrapper.orderByDesc(CardApplyPO.DB_COL_CREATE_TIME);
        return super.findList(queryWrapper);

    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public Boolean approvedCardApply(UpdateCardApplyReqDTO reqDto) {

        String employeeId = reqDto.getEmployeeId();
        String companyId = reqDto.getCompanyId();
        //1.幂等加锁
        String lockKey = RedisKeyConstant.CREATE_CARD_APPROVE_LOCK + employeeId;
        try {
            boolean b = redissonService.tryLock(lockKey);
            if (!b) {
                throw new FxCardException(GlobalCoreResponseCode.SERVER_REQUEST_FAST);
            }
            //2.参数校验
            createCardApprovedParamVerification(reqDto);

            //3.检查是否有审批中的记录
            checkApplyIng(reqDto.getFxCardId(), reqDto.getApplyId());


            CardApplyDTO cardApplyDTO = queryCardApplyByApplyId(reqDto.getApplyId());
            CardPlatformCaseEnum cardPlatformCaseCode = CardPlatformCaseEnum.getEnumByCardPlatformCode(cardApplyDTO.getCardPlatform());
            boolean checkBalance=false;
            if (!reqDto.getCardShowStatus().equals(CardApplyStatusEnum.PASS.getStatus())){
                checkBalance=false;
            }
            //4.检查企业余额是否满足创建卡
            CompanyAcctBaseDTO companyAcctBaseDTO = checkAccount(checkBalance, companyId, cardPlatformCaseCode);
            //5.更新/创建审批信息 管控规则：频率，币种，金额
            CardApplyPO cardApply = buildCardApply(reqDto,cardApplyDTO);
            cardApply.setBankMchId(companyAcctBaseDTO.getBankAcctId());
            //6.同意后，提交airwallex/lianlian
            if (!cardApply.getApplyStatus().equals(CardApplyStatusEnum.REFUSE.getStatus())) {
                submitCardApply(reqDto, cardApply);
            }
            //7. 保存更新审批
            log.info("更新卡审批信息 req = {}",JSON.toJSONString(cardApply));
            saveOrUpdate(cardApply);

            syncQueryResult(cardApply);

            return true;
        } catch (FinhubException fe) {
            log.warn("approvedCardApply exception:{} ", fe);
            throw fe;
        } catch (Exception e) {
            log.error("approvedCardApply exception:{} ", e);
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION);
        } finally {
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                FinhubLogger.error("approvedCardApply unlock|err=", e);
            }
        }


    }

    private void syncQueryResult(CardApplyPO cardApply){
        if (!cardApply.getApplyStatus().equals(CardApplyStatusEnum.REFUSE.getStatus())
            && cardApply.getApplyStatus() != CardApplyStatusEnum.FAILED.getStatus()
            && cardApply.getApplyStatus() != CardApplyStatusEnum.STEREO_PENDING.getStatus() ) {
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                if(CardPlatformEnum.isAirwallex(cardApply.getCardPlatform())){
                    CardApplyManager.me().queryPendingResult(cardApply.getBankCardId());
                }else if(CardPlatformEnum.isLianLian(cardApply.getCardPlatform())){
                    CardApplyManager.me().queryPendingResult4LianLian(cardApply);
                }

            });
        }
    }


    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public Boolean submitCard(String applyId) {
        //1.幂等加锁
        String lockKey = RedisKeyConstant.CREATE_CARD_STEREO_APPROVE_LOCK + applyId;
        try {
            log.info("stereo终审信息  req = {}",JSON.toJSONString(applyId));
            CardApplyDTO cardApplyDTO = queryCardApplyByApplyId(applyId);
//            if(CardPlatformEnum.isLianLian(cardApplyDTO.getCardPlatform())){
//                log.info("stereo终审信息,lianlian无实体卡，so无需FBT运营审核，so无Stereo审批开卡，此接口对连连无效req = {}",JSON.toJSONString(applyId));
//                return false;
//            }
            boolean b = redissonService.tryLock(lockKey);
            if (!b) {
                throw new FxCardException(GlobalCoreResponseCode.SERVER_REQUEST_FAST);
            }

            checkStereoApplyIng(cardApplyDTO.getFxCardId(), cardApplyDTO.getApplyId());

            CardPlatformCaseEnum cardPlatformCaseCode = CardPlatformCaseEnum.getEnumByCardPlatformCode(cardApplyDTO.getCardPlatform());

            //3.检查企业余额是否满足创建卡
            checkAccount(false , cardApplyDTO.getCompanyId(),cardPlatformCaseCode);

            //5.更新/创建审批信息 管控规则：频率，币种，金额
            CardApplyPO cardApply = stereoBuildCardApply(cardApplyDTO);

            //6.提交airwallex/lianlian
            stereoSubmit(cardApply);

            //7. 保存更新审批
            log.info("stereo终审信息  req = {}",JSON.toJSONString(cardApply));
            saveOrUpdate(cardApply);

            syncQueryResult(cardApply);
            return true;
        } catch (FinhubException fe) {
            log.warn("approvedCardApply exception:{} ", fe.getMessage() , fe);
            throw fe;
        } catch (Exception e) {
            log.error("approvedCardApply exception:{} ", e.getMessage() , e);
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION);
        } finally {
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                FinhubLogger.error("approvedCardApply unlock|err=", e);
            }
        }
    }

    private void stereoSubmit(CardApplyPO cardApplyPO){

        if (CardPlatformEnum.isLianLian(cardApplyPO.getCardPlatform())){
            stereoSubmitLianLian(cardApplyPO);
        }else {
            stereoSubmitAirwallex(cardApplyPO);
        }
    }
    private void stereoSubmitAirwallex(CardApplyPO cardApplyPO){
        AirCreateCardRpcReqDTO createReq = new AirCreateCardRpcReqDTO();
        // 授权控制
        BaseAirwallexRpcDTO.AuthorizationControls authorizationControls = new BaseAirwallexRpcDTO.AuthorizationControls();
        authorizationControls.setAllowed_transaction_count("MULTIPLE");
        BaseAirwallexRpcDTO.TransactionLimits limits = new BaseAirwallexRpcDTO.TransactionLimits();
        if (StringUtils.isBlank(cardApplyPO.getCardLimits())){
            limits.setLimits(setDefaultLimit());
        }else {
            List<BaseAirwallexRpcDTO.Limit> parseLimit = JSONObject.parseArray(cardApplyPO.getCardLimits(), BaseAirwallexRpcDTO.Limit.class);
            if (parseLimit != null && parseLimit.size() > 0){
                limits.setLimits(parseLimit);
            }else {
                limits.setLimits(setDefaultLimit());
            }
        }
        limits.setCurrency(cardApplyPO.getCurrency());
        authorizationControls.setTransaction_limits(limits);
        createReq.setAuthorization_controls(authorizationControls);

        createReq.setCreated_by(cardApplyPO.getApplyerLastName() + cardApplyPO.getApplyerFirstName());
        createReq.setForm_factor(CardFormFactorEnum.getFormFactor(cardApplyPO.getCardFormFactor()).getAirCode());
        createReq.setIssue_to(CardIssueToEnum.getCardIssueTo(cardApplyPO.getCardIssueTo()).getAirCode());
        if (cardApplyPO.getCardIssueTo() == CardIssueToEnum.ORGANISATION.getCode() ){
            createReq.setPurpose(cardApplyPO.getCardPurpose());
        }
        // 实体卡需要传递邮寄地址  持卡人  激活状态等
        if (CardFormFactorEnum.PHYSICAL.getCode().equals(cardApplyPO.getCardFormFactor())) {
            createReq.setActivate_on_issue(true);//发行时激活
            //持卡人信息
            CardholderDTO cardHolder = checkCardHolderById(cardApplyPO.getFxCardholderId());
            if (ObjectUtils.isEmpty(cardHolder)) {
                throw new FxCardException(GlobalCoreResponseCode.CARD_HOLDER_NOT_EXIT);
            }
            // 构建地址
            // 客户填写邮寄地址
            AddressDto addressDto4CardHolder = JsonUtils.toObj(cardHolder.getPostalAddress(), AddressDto.class);
            //实际邮寄到AW地址
            AddressDto awPostAddressDto;
            BaseAirwallexRpcDTO.Address airwallexRpcDTOAddress = new BaseAirwallexRpcDTO.Address();
            String mailNo = null;
            //国内国外都邮寄到分贝通
//            if (addressDto4CardHolder.getCountry().getCrCode().equals("CN") || addressDto4CardHolder.getCountry().getCrCode().equals("HK")){
//                awPostAddressDto=addressDto4CardHolder;
//                airwallexRpcDTOAddress.setCity(awPostAddressDto.getCity().getName());
//                airwallexRpcDTOAddress.setCountry(awPostAddressDto.getCountry().getCrCode());
//                airwallexRpcDTOAddress.setLine1(awPostAddressDto.getLine1());
//                airwallexRpcDTOAddress.setState(awPostAddressDto.getState().getName());
//                airwallexRpcDTOAddress.setPostcode(awPostAddressDto.getPostcode());
//            }else {
//            }
            awPostAddressDto = JSONObject.parseObject(hostAddress,AddressDto.class);
            airwallexRpcDTOAddress.setCity(cityName);
            airwallexRpcDTOAddress.setCountry(country);
            airwallexRpcDTOAddress.setLine1(postalAddress);
            airwallexRpcDTOAddress.setState(state);
            airwallexRpcDTOAddress.setPostcode(postCode);
            mailNo = IdUtils.getId().toString() ;
            airwallexRpcDTOAddress.setLine2(mailNo + phone );
            awPostAddressDto.setLine2(mailNo + phone );

            /**插入邮寄表数据*/
            insertIntoCarMailInfo(cardApplyPO, mailNo, awPostAddressDto, cardHolder);
            createReq.setPostal_address(airwallexRpcDTOAddress);
            createReq.setCardholder_id(cardHolder.getBankCardholderId());
        }

        createReq.setRequest_id(BizIdUtils.getAirRequestId());
        try {
            log.info("airWallexCardService stereoSubmit req = {}", JSON.toJSONString(createReq));
            AirCardDetailsRpcRespDTO airCardDetailsRpcRespDTO = airWallexCardService.createCard(createReq);
            log.info("airWallexCardService stereoSubmit resp = {}", JSON.toJSONString(airCardDetailsRpcRespDTO));
            // 更新cardId
            cardApplyPO.setBankCardId(airCardDetailsRpcRespDTO.getCard_id());
        } catch (Exception e) {
            log.error("【卡创建失败】：{}", e);
            cardApplyPO.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
            cardApplyPO.setRefuseReason(e.getMessage());
        }
    }

    private List<BaseAirwallexRpcDTO.Limit>  setDefaultLimit(){
        List<BaseAirwallexRpcDTO.Limit> limitList = new ArrayList<>();
        BaseAirwallexRpcDTO.Limit limit = new BaseAirwallexRpcDTO.Limit();
        limit.setAmount(50000L);
        limit.setInterval(TradeLimitEnum.PER_TRANSACTION.getCode());
        limitList.add(limit);
        return limitList ;
    }


    private void insertIntoCarMailInfo (CardApplyPO cardApplyPO ,String mailNo  ,AddressDto addressDto, CardholderDTO cardholderDTO){
        CardMailPO cardMailPO = new CardMailPO();
        BeanUtils.copyProperties(cardApplyPO,cardMailPO);
        cardMailPO.setCardOwnerType(cardApplyPO.getCardIssueTo());
        cardMailPO.setEmployeeId(cardApplyPO.getCreateUserId());
        cardMailPO.setEmployeePhone(cardholderDTO.getPhone());
        cardMailPO.setEmployeeName(cardApplyPO.getNameOnCard());
        cardMailPO.setAxPostAddress(JsonUtils.toJson(addressDto));
        cardMailPO.setOriginalAddress(cardholderDTO.getPostalAddress());
        cardMailPO.setCardMailNo(mailNo);
        cardMailPO.setForwardFlag(StringUtils.isNotBlank(mailNo) ? 1 : 0);
        log.info("insertIntoCarMailInfo req = {}",JSON.toJSONString(cardMailPO));
        QueryWrapper<CardMailPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardMailPO.DB_COL_BANK_CARD_ID,cardApplyPO.getBankCardId());
        log.error("【创建/更新邮寄信息 req = {}】", JSON.toJSONString(cardMailPO) );

        CardMailManager.me().saveOrUpdate(cardMailPO ,queryWrapper);
    }


    /**
     * 创建修改卡片
     */
    private void submitCardApply(UpdateCardApplyReqDTO reqDto, CardApplyPO cardApply) {
        // 创建卡片
        CardDTO cardDTO = CardManager.me().getByfxCardId(cardApply.getFxCardId());
        if (cardDTO == null ) {
            if(CardPlatformEnum.isAirwallex( cardApply.getCardPlatform())){
                applyCard4Airwallex(reqDto, cardApply);
            }else if(CardPlatformEnum.isLianLian( cardApply.getCardPlatform())){
                applyCard4LianLian(reqDto, cardApply);
            }
        } else {
            if(CardPlatformEnum.isAirwallex( cardApply.getCardPlatform())){
                updateCard4Airwallex(reqDto, cardApply);
            }else if(CardPlatformEnum.isLianLian( cardApply.getCardPlatform())){
                //更新状态
                updateCard4LianLian(reqDto, cardApply);
            }

        }
    }

    private void updateCard4Airwallex(UpdateCardApplyReqDTO reqDto, CardApplyPO cardApply) {
        // 更新卡片(通过设置包含的参数的值来更新卡详细信息。未包含的参数将保持不变。)
        AirUpdateCardRpcReqDTO updateReq = new AirUpdateCardRpcReqDTO();
        // 授权控制
        BaseAirwallexRpcDTO.AuthorizationControls authorizationControls = new BaseAirwallexRpcDTO.AuthorizationControls();
        authorizationControls.setAllowed_transaction_count("MULTIPLE");
        BaseAirwallexRpcDTO.TransactionLimits limits = new BaseAirwallexRpcDTO.TransactionLimits();
        limits.setLimits(reqDto.getCardLimits());
        limits.setCurrency(reqDto.getCurrency());
        authorizationControls.setTransaction_limits(limits);
        updateReq.setAuthorization_controls(authorizationControls);
        try {
            log.info("airWallexCardService updateCard req", JSON.toJSONString(updateReq));
            AirCardDetailsRpcRespDTO airCardDetailsRpcRespDTO = airWallexCardService.updateCard(cardApply.getBankCardId(), updateReq);
            log.info("airWallexCardService updateCard resp", JSON.toJSONString(airCardDetailsRpcRespDTO));
        } catch (Exception e) {
            log.error("【卡更新失败】：{}", e);
            cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
            cardApply.setRefuseReason(e.getMessage());
        }
    }


    private void updateCard4LianLian(UpdateCardApplyReqDTO reqDto, CardApplyPO cardApply) {
        boolean lianLianDealStatus = CardStatusEnum.isLianLianDealStatus(reqDto.getCardShowStatus());
        CardStatusEnum lianLianCodeEnumByShowStatus;
        if(lianLianDealStatus){
            lianLianCodeEnumByShowStatus = CardStatusEnum.getLianLianCodeEnumByShowStatus(reqDto.getCardShowStatus());
            if(Objects.isNull(lianLianCodeEnumByShowStatus)){
                log.error(GlobalCoreResponseCode.CARD_UPDATE_ERROR_LIAN_NO.getMessage(),JSON.toJSONString(reqDto));
                throw new FxCardException(GlobalCoreResponseCode.CARD_UPDATE_ERROR_LIAN_NO);
            }
        }else{
            log.error(GlobalCoreResponseCode.CARD_UPDATE_ERROR_LIAN_NO.getMessage(),JSON.toJSONString(reqDto));
            throw new FxCardException(GlobalCoreResponseCode.CARD_UPDATE_ERROR_LIAN_NO);
        }
        LianLianCardModifyRpcReqDTO lianLianCardModifyRpcReqDTO = new LianLianCardModifyRpcReqDTO();
        lianLianCardModifyRpcReqDTO.setAccountNo(cardApply.getBankCardId());
        lianLianCardModifyRpcReqDTO.setMchId(cardApply.getBankMchId());
        lianLianCardModifyRpcReqDTO.setAccountStatus(lianLianCodeEnumByShowStatus.getLianLianCode());
        try {
            log.info("LianLianCardService updateCard req", JSON.toJSONString(lianLianCardModifyRpcReqDTO));
            LianLianCardModifyRpcRespDTO modify = iLianLianCardService.modify(lianLianCardModifyRpcReqDTO);
            log.info("LianLianCardService updateCard resp", JSON.toJSONString(modify));
        } catch (Exception e) {
            log.error("【连连卡更新失败】：{}", e);
            cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
            cardApply.setRefuseReason(e.getMessage());
        }
    }


    /**
     * LianLian申请卡之前推送员工信息，获取连连UserId
     * LianLian申请卡，信息包装与请求
     * @param reqDto
     * @param cardApply
     */
    private void applyCard4LianLian(UpdateCardApplyReqDTO reqDto, CardApplyPO cardApply) {
        // 实体卡需要传递邮寄地址  持卡人  激活状态等
        if (CardFormFactorEnum.PHYSICAL.getCode().equals(cardApply.getCardFormFactor())) {
            //实体卡需要stereo审核,所以
            return;
        }
        //LianLian申请卡之前推送员工信息，UserId为我司UserId
        applyCardHolder4LianLian(cardApply);
        applyCardOfVirtual(reqDto,cardApply);
    }
    private void stereoSubmitLianLian(CardApplyPO cardApply){
        LianLianPhysicalCardApplyRpcReqDTO lianLianPhysicalCardApplyRpcReqDTO = new LianLianPhysicalCardApplyRpcReqDTO();
        lianLianPhysicalCardApplyRpcReqDTO.setMchId(cardApply.getBankMchId());
        lianLianPhysicalCardApplyRpcReqDTO.setOutBatchNo(BizIdUtils.getLianLianRequestId());
        lianLianPhysicalCardApplyRpcReqDTO.setTemplateId(lianlianTemplateId);
        lianLianPhysicalCardApplyRpcReqDTO.setCurrency(ObjUtils.isNull(cardApply.getCurrency())?CurrencyEnum.CNY.getCurrencyCode():cardApply.getCurrency());
        List<LianLianPhysicalCardApplyList> list = new ArrayList<>();
        LianLianPhysicalCardApplyList cardApplyList = new LianLianPhysicalCardApplyList();
        cardApplyList.setUserId(cardApply.getCreateUserId());
        LianLianPhysicalCardListUser lianLianPhysicalCardListUser = new LianLianPhysicalCardListUser();
        lianLianPhysicalCardListUser.setPhone(cardApply.getApplyerPhone());
        lianLianPhysicalCardListUser.setEmail(cardApply.getApplyerEmail());
        lianLianPhysicalCardListUser.setName(cardApply.getApplyerFirstName() + cardApply.getApplyerFirstName());
        cardApplyList.setUserInfo(lianLianPhysicalCardListUser);
        cardApplyList.setOutOrderNo(BizIdUtils.getLianLianRequestId());
        cardApplyList.setCardHolder(cardApply.getApplyerFirstName().toUpperCase() + cardApply.getApplyerLastName().toUpperCase());
        Date yearAddOne = DateUtils.addMonth(new Date(), 47);//连连卡4年8个月失效，暂定4年
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(yearAddOne);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        String lastDay = sdf.format(calendar.getTime());
        cardApplyList.setExpireTime(lastDay);
        cardApplyList.setRemark("实体卡开卡申请");
        list.add(cardApplyList);
        lianLianPhysicalCardApplyRpcReqDTO.setCardList(list);
        LianLianPhysicalCardApplyShippingAddress lianLianPhysicalCardApplyShippingAddress = new LianLianPhysicalCardApplyShippingAddress();
        AddressDto addressDto = JsonUtils.toObj(cardApply.getPostalAddress(), AddressDto.class);
        /**
         * {
         *     "city": {
         *         "crCode": "CN",
         *         "enName": "Fuzhou City",
         *         "id": "1303",
         *         "name": "福州市"
         *     },
         *     "country": {
         *         "crCode": "CN",
         *         "enName": "China",
         *         "id": "0",
         *         "name": "中国"
         *     },
         *     "line1": "123",
         *     "postcode": "123",
         *     "state": {
         *         "crCode": "CN",
         *         "enName": "Fujian Province",
         *         "id": "16",
         *         "name": "福建省"
         *     }
         * }
         */
        // 构建地址
//        BaseAirwallexRpcDTO.Address address = new BaseAirwallexRpcDTO.Address();
        String mailNo = null;
        if (addressDto.getCountry().getCrCode().equals("CN") || addressDto.getCountry().getCrCode().equals("HK")){
        }else {
            mailNo = IdUtils.getId().toString() ;
        }
        CardholderDTO cardHolder = new CardholderDTO();
        cardHolder.setPhone(cardApply.getApplyerPhone());
        cardHolder.setPostalAddress(cardApply.getPostalAddress());
        AddressDto awPostAddressDto=JsonUtils.toObj(cardHolder.getPostalAddress(), AddressDto.class);
        insertIntoCarMailInfo(cardApply,mailNo ,awPostAddressDto, cardHolder);
        lianLianPhysicalCardApplyShippingAddress.setProvince(addressDto.getCountry().getName());
        lianLianPhysicalCardApplyShippingAddress.setCity(addressDto.getCity().getName());
        lianLianPhysicalCardApplyShippingAddress.setDistrict(addressDto.getState().getName());
        lianLianPhysicalCardApplyShippingAddress.setAddress(addressDto.getLine1());
        lianLianPhysicalCardApplyShippingAddress.setReceiver(cardApply.getApplyerFirstName().toUpperCase() + cardApply.getApplyerLastName().toUpperCase());
        lianLianPhysicalCardApplyShippingAddress.setContactMobile(cardApply.getApplyerPhone());
        lianLianPhysicalCardApplyShippingAddress.setPostCode(addressDto.getPostcode());
        lianLianPhysicalCardApplyRpcReqDTO.setShippingAddress(lianLianPhysicalCardApplyShippingAddress);
        try {
            log.info("lianLianCardService stereoSubmitLianLian req = {}", JSON.toJSONString(lianLianPhysicalCardApplyRpcReqDTO));
            LianLianPhysicalCardApplyRpcRespDTO lianLianPhysicalCardApplyRpcRespDTO =  iLianLianCardService.physicalApply(lianLianPhysicalCardApplyRpcReqDTO);
            log.info("lianLianCardService stereoSubmitLianLian resp = {}", JSON.toJSONString(lianLianPhysicalCardApplyRpcRespDTO));
            if (lianLianPhysicalCardApplyRpcRespDTO.success()){
                List<LianLianPhysicalCardListResp> cardListResps = lianLianPhysicalCardApplyRpcRespDTO.getCardLists();
                cardApply.setBankCardId(cardListResps.get(0).getAccountNo());
                cardApply.setRefuseReason("开卡申请成功，耐心等待开卡结果");
                cardApply.setOutBatchNo(lianLianPhysicalCardApplyRpcRespDTO.getOutBatchNo());
                cardApply.setApplyStatus(CardApplyStatusEnum.WAIT_ACTIVE.getStatus());
            }else {
                cardApply.setApplyStatus(CardApplyStatusEnum.STEREO_PENDING.getStatus());
            }

        }catch (Exception e){
            log.error("lianLianCardService applyCardOfPhysical【submitCardApply 卡创建失败】：{}",JSON.toJSONString(lianLianPhysicalCardApplyRpcReqDTO), e);
            cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
            cardApply.setRefuseReason("开卡申请失败");
        }

    }
    private void applyCardOfVirtual(UpdateCardApplyReqDTO reqDto, CardApplyPO cardApply){
        LianLianCardApplyRpcReqDTO createReqDTO = new LianLianCardApplyRpcReqDTO();
        createReqDTO.setUserId(cardApply.getCreateUserId());
        createReqDTO.setMchId(cardApply.getBankMchId());
        createReqDTO.setTemplateId(lianlianTemplateId);
        createReqDTO.setOutOrderNo(BizIdUtils.getLianLianRequestId());
        Date yearAddOne = DateUtils.addYear(new Date(), 4);//连连卡4年8个月失效，暂定4年
        createReqDTO.setExpireTime(DateUtil.formatDate(yearAddOne));
        if ("******************".equals(cardApply.getBankMchId())){
            yearAddOne = DateUtils.addYear(new Date(), 3);//测试环境改成3年有效期
            createReqDTO.setExpireTime(DateUtil.formatDate(yearAddOne));
        }
        createReqDTO.setCurrency(ObjUtils.isNull(reqDto.getCurrency())?CurrencyEnum.CNY.getCurrencyCode():reqDto.getCurrency());
        createReqDTO.setRemark("开卡申请");
        try {
            log.info("lianLianCardService submitCardApply req = {}", JSON.toJSONString(createReqDTO));
            LianLianCardApplyRpcRespDTO applyRpcRespDTO = iLianLianCardService.apply(createReqDTO);
            log.info("lianLianCardService submitCardApply resp = {}", JSON.toJSONString(applyRpcRespDTO));
            // 更新cardId
            if(ObjUtils.isNull(applyRpcRespDTO)){
                log.error("lianLianCardService【submitCardApply 卡创建失败】：{}", JSON.toJSONString(applyRpcRespDTO));
                cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
                cardApply.setRefuseReason("连连未返回结果");
            }else{
                if(applyRpcRespDTO.success()){
                    cardApply.setBankCardId(applyRpcRespDTO.getAccountNo());
                    cardApply.setRefuseReason("开卡申请成功，耐心等待开卡结果");
                }else{
                    log.error("lianLianCardService【submitCardApply 卡创建失败】：{}", JSON.toJSONString(applyRpcRespDTO));
                    cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
                    cardApply.setRefuseReason(applyRpcRespDTO.getRetMsg());
                }
            }
        } catch (Exception e) {
            log.error("lianLianCardService【submitCardApply 卡创建失败】：{}",JSON.toJSONString(createReqDTO), e);
            cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
            cardApply.setRefuseReason("开卡申请失败"+e.getMessage());
        }
    }

    private void applyCardHolder4LianLian(CardApplyPO cardApply) {
        LianLianCreateUserRpcReqDTO createReqDTO = new LianLianCreateUserRpcReqDTO();

        createReqDTO.setMchId(cardApply.getBankMchId());
        List<LianLianCreateUser> createUsers = new ArrayList<>();
        LianLianCreateUser createUser = new LianLianCreateUser();
        createUser.setUserId(cardApply.getCreateUserId());
        createUser.setName(cardApply.getNameOnCard());
        createUser.setPhone(cardApply.getApplyerPhone());
        createUser.setEmail(cardApply.getApplyerEmail());
        createUser.setEnable("ENABLE");
        createUsers.add(createUser);
        createReqDTO.setUserList(createUsers);
        try {
            log.info("lianLianCardService submitCreateUser req = {}", JSON.toJSONString(createReqDTO));
            LianLianCreateUserRpcRespDTO createUserRpcRespDTO = iLianLianCardService.createUser(createReqDTO);
            log.info("lianLianCardService submitCreateUser resp = {}", JSON.toJSONString(createUserRpcRespDTO));
            // 更新cardId
            if(ObjUtils.isNull(createUserRpcRespDTO)){
                log.error("lianLianCardService【submitCreateUser 卡创建提交员工失败】：{}", JSON.toJSONString(createUserRpcRespDTO));
                cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
                cardApply.setRefuseReason("连连未返回结果");
            }else{
                if(createUserRpcRespDTO.success()){
                    List<LianLianFailList> failList = createUserRpcRespDTO.getFailList();
                    if(!ObjUtils.isEmpty(failList)){
                        log.error("lianLianCardService【submitCardApply 卡创建提交员工失败】：{}", JSON.toJSONString(createUserRpcRespDTO));
                        cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
                        cardApply.setRefuseReason(failList.get(0).getFailReason());
                    }
                }else{
                    log.error("lianLianCardService【submitCardApply 卡创建提交员工失败】：{}", JSON.toJSONString(createUserRpcRespDTO));
                    cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
                    cardApply.setRefuseReason(createUserRpcRespDTO.getRetMsg());
                }
            }
        } catch (Exception e) {
            log.error("lianLianCardService【submitCardApply 卡创建失败】：{}", e);
            cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
            cardApply.setRefuseReason(e.getMessage());
        }
    }


    /**
     * Airwallex申请卡，信息包装与请求
     * @param reqDto
     * @param cardApply
     */
    private void applyCard4Airwallex(UpdateCardApplyReqDTO reqDto, CardApplyPO cardApply) {
        AirCreateCardRpcReqDTO createReq = new AirCreateCardRpcReqDTO();
        // 授权控制
        BaseAirwallexRpcDTO.AuthorizationControls authorizationControls = new BaseAirwallexRpcDTO.AuthorizationControls();
        authorizationControls.setAllowed_transaction_count("MULTIPLE");
        BaseAirwallexRpcDTO.TransactionLimits limits = new BaseAirwallexRpcDTO.TransactionLimits();
        if (CollectionUtils.isEmpty(reqDto.getCardLimits())){
            List<BaseAirwallexRpcDTO.Limit> limitList = new ArrayList<>();
            BaseAirwallexRpcDTO.Limit limit = new BaseAirwallexRpcDTO.Limit();
            limit.setAmount(50000L);
            limit.setInterval(TradeLimitEnum.PER_TRANSACTION.getCode());
            limitList.add(limit);
            reqDto.setCardLimits(limitList);
        }
        limits.setLimits(reqDto.getCardLimits());
        limits.setCurrency(reqDto.getCurrency());
        authorizationControls.setTransaction_limits(limits);
        createReq.setAuthorization_controls(authorizationControls);

        createReq.setCreated_by(cardApply.getApplyerLastName() + cardApply.getApplyerFirstName());
        createReq.setForm_factor(CardFormFactorEnum.getFormFactor(cardApply.getCardFormFactor()).getAirCode());
        createReq.setIssue_to(CardIssueToEnum.getCardIssueTo(cardApply.getCardIssueTo()).getAirCode());
        // 实体卡需要传递邮寄地址  持卡人  激活状态等
        if (CardFormFactorEnum.PHYSICAL.getCode().equals(cardApply.getCardFormFactor())) {
            //实体卡需要stereo审核
            return;
        }
        if (cardApply.getCardIssueTo() == CardIssueToEnum.ORGANISATION.getCode() ){
            createReq.setPurpose(cardApply.getCardPurpose());
        }
        createReq.setRequest_id(BizIdUtils.getAirRequestId());
        try {
            log.info("airWallexCardService submitCardApply req = {}", JSON.toJSONString(createReq));
            AirCardDetailsRpcRespDTO airCardDetailsRpcRespDTO = airWallexCardService.createCard(createReq);
            log.info("airWallexCardService submitCardApply resp = {}", JSON.toJSONString(airCardDetailsRpcRespDTO));
            // 更新cardId
            cardApply.setBankCardId(airCardDetailsRpcRespDTO.getCard_id());
            cardApply.setRefuseReason("");
        } catch (Exception e) {
            log.error("airWallexCardService【submitCardApply 卡创建失败】：{}", e);
            cardApply.setApplyStatus(CardApplyStatusEnum.FAILED.getStatus());
            cardApply.setRefuseReason(e.getMessage());
        }
    }

    /**
     * 检查是否有进行中的审批
     *
     * @param fxCardId
     * @param applyId
     */
    private void checkApplyIng(String fxCardId, String applyId) {
        if (StringUtils.isEmpty(fxCardId)) {
            return;
        }
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_FX_CARD_ID, fxCardId);
        queryWrapper.ne(StringUtils.isNotEmpty(applyId), CardApplyPO.DB_COL_APPLY_ID, applyId);
        queryWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.PENDING.getStatus());
        CardApplyDTO apply = findOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(apply)) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_APPLY_EXIT);
        }
    }

    private void checkStereoApplyIng(String fxCardId, String applyId) {
        if (StringUtils.isEmpty(fxCardId)) {
            return;
        }
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_FX_CARD_ID, fxCardId);
        queryWrapper.ne(StringUtils.isNotEmpty(applyId), CardApplyPO.DB_COL_APPLY_ID, applyId);
        queryWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.STEREO_PENDING.getStatus());
        CardApplyDTO apply = findOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(apply)) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_APPLY_EXIT);
        }
    }

    /**
     * 创建审批信息
     */
    private CardApplyPO buildCardApply(UpdateCardApplyReqDTO reqDto,CardApplyDTO cardApplyDTO) {
        CardApplyPO applyPO = new CardApplyPO();
        // 更新审批管控信息
        BeanUtils.copyProperties(cardApplyDTO, applyPO);
        // 创建新的审批信息
        CardDTO card = CardManager.me().getByfxCardId(cardApplyDTO.getFxCardId());
        log.info("CardApplyManager buildCardApply req = {},卡信息 = {}", JSON.toJSONString(reqDto) ,  JSON.toJSONString(card) );
        if (card != null){
            // 如果已经有卡就是更新
            applyPO.setApplyType(ApplyTypeEnum.UPDATE.getCode());
        }else {
            applyPO.setApplyType(ApplyTypeEnum.ADD.getCode());
        }

        // 设置管控规则
        applyPO.setCardLimits(JSON.toJSONString(reqDto.getCardLimits()));
        if (reqDto.getCardShowStatus().equals(CardApplyStatusEnum.PASS.getStatus())){
            if (CardFormFactorEnum.PHYSICAL.getCode().equals(applyPO.getCardFormFactor())){
                //判断是更新还是创建
                if (card != null){
                    applyPO.setApplyStatus(CardApplyStatusEnum.PENDING.getStatus());
                }else {
                    applyPO.setApplyStatus(CardApplyStatusEnum.STEREO_PENDING.getStatus());
                }
            }else {
                applyPO.setApproveTime(new Date());
                applyPO.setApplyStatus(CardApplyStatusEnum.PENDING.getStatus());
            }
        }else {
            applyPO.setApplyStatus(CardApplyStatusEnum.REFUSE.getStatus());
        }
        applyPO.setApproveUserId(reqDto.getEmployeeId());
        applyPO.setApproveUserName(reqDto.getEmployeeName());
        log.info("CardApplyManager buildCardApply res = {}", JSON.toJSONString(applyPO) );

        return applyPO;
    }

    private CardApplyPO stereoBuildCardApply(CardApplyDTO cardApplyDTO) {
        CardApplyPO applyPO = new CardApplyPO();
        BeanUtils.copyProperties(cardApplyDTO, applyPO);

        // 设置管控规则
        applyPO.setApplyStatus(CardApplyStatusEnum.PENDING.getStatus());
        return applyPO;
    }

    private static void createCardApprovedParamVerification(UpdateCardApplyReqDTO reqDto) {
        if (ObjectUtils.isEmpty(reqDto.getCardShowStatus())) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "cardShowStatus "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
        }
        if (ObjectUtils.isEmpty(CardShowStatusEnum.get(reqDto.getCardShowStatus()))) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "cardShowStatus "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
        }
        if (StringUtils.isEmpty(reqDto.getApplyId()) && StringUtils.isEmpty(reqDto.getFxCardId())) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "applyId、fxCardId "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
        }
        if (!CollectionUtils.isEmpty(reqDto.getCardLimits())) {
            if (StringUtils.isNotBlank(reqDto.getCurrency())) {
                throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "cardLimits.currency "+ I18nUtils.getMessage("CAM_NOT_EMPTY"));
            }
        }
    }

    public void queryOrder(){
        List<CardApplyPO> applyDTOS = queryWaitActive();
        if (applyDTOS != null) {
            for (CardApplyPO cardApplyDTO:applyDTOS) {
                LianLianPhysicalCardOrderQueryRpcReqDTO lianLianPhysicalCardOrderQueryRpcReqDTO = new LianLianPhysicalCardOrderQueryRpcReqDTO();
                lianLianPhysicalCardOrderQueryRpcReqDTO.setMchId(cardApplyDTO.getBankMchId());
                lianLianPhysicalCardOrderQueryRpcReqDTO.setOutBatchNo(cardApplyDTO.getOutBatchNo());
                log.info("lianlian queryOrder req = {}", JSON.toJSONString(lianLianPhysicalCardOrderQueryRpcReqDTO));
                LianLianPhysicalCardOrderQueryRpcRespDTO lianLianPhysicalCardOrderQueryRpcRespDTO =  iLianLianCardService.physicalApplyOrderQuery(lianLianPhysicalCardOrderQueryRpcReqDTO);
                log.info("lianlian queryOrder resp = {}", JSON.toJSONString(lianLianPhysicalCardOrderQueryRpcRespDTO));
                if (lianLianPhysicalCardOrderQueryRpcRespDTO.success()){
                    UpdateWrapper<CardApplyPO> updateWrapper = new UpdateWrapper<>();
                    if (lianLianPhysicalCardOrderQueryRpcRespDTO.getDeliveryLogistics() != null) {
                        updateWrapper.set(CardApplyPO.DB_COL_LOGISTICS_NO, lianLianPhysicalCardOrderQueryRpcRespDTO.getDeliveryLogistics().getLogisticsNo());
                        updateWrapper.set(CardApplyPO.DB_COL_LOGISTICS_COMPANY, lianLianPhysicalCardOrderQueryRpcRespDTO.getDeliveryLogistics().getLogisticsCompany());
                    }
                    if ("FAIL".equals(lianLianPhysicalCardOrderQueryRpcRespDTO.getOrderStatus())){
                        updateWrapper.set(CardApplyPO.DB_COL_APPLY_STATUS,CardApplyStatusEnum.FAILED.getStatus());
                    }
                    //APPLY -> FAIL/ PROCESSING -> AWAIT_DELIVERY -> SUCCESS
                    if ("APPLY".equals(lianLianPhysicalCardOrderQueryRpcRespDTO.getOrderStatus())
                        || "PROCESSING".equals(lianLianPhysicalCardOrderQueryRpcRespDTO.getOrderStatus())
                        ||"AWAIT_DELIVERY".equals(lianLianPhysicalCardOrderQueryRpcRespDTO.getOrderStatus())
                        ||"SUCCESS".equals(lianLianPhysicalCardOrderQueryRpcRespDTO.getOrderStatus())
                    ){
                        boolean isCreated = queryPendingResult4LianLian(cardApplyDTO);
                        if (isCreated) {
                            updateWrapper.set(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.PASS.getStatus());
                        }
                        if (Objects.equals(CardFormFactorEnum.PHYSICAL.getCode(), cardApplyDTO.getCardFormFactor())) {
                            //更新邮寄信息
                            updateCardMailInfo4LianLian(cardApplyDTO);
                        }
                    }
                    updateWrapper.set(CardApplyPO.DB_COL_APPLY_ORDER_STATUS,lianLianPhysicalCardOrderQueryRpcRespDTO.getOrderStatus());
                    updateWrapper.eq(CardApplyPO.DB_COL_FX_CARD_ID, cardApplyDTO.getFxCardId());
                    this.update(updateWrapper);
                }
            }
        }
    }

    public List<CardApplyPO> queryWaitActive(){
        QueryWrapper<CardApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.WAIT_ACTIVE.getStatus());
        queryWrapper.eq(CardApplyPO.DB_COL_CARD_FORM_FACTOR,1);
        queryWrapper.eq(CardApplyPO.DB_COL_CARD_PLATFORM, CardPlatformEnum.LIANLIAN.getCode());
        List<CardApplyPO> cardApplyPOS = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(cardApplyPOS)){
            return null;
        }else {
            return cardApplyPOS;
        }
    }

    public void updateCardApply(String bankCardId,String failReason){
        UpdateWrapper<CardApplyPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.FAILED.getStatus());
        updateWrapper.set(CardApplyPO.DB_COL_REFUSE_REASON, failReason);
        updateWrapper.eq(CardApplyPO.DB_COL_BANK_CARD_ID, bankCardId);
        updateWrapper.eq(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.PENDING.getStatus());
        this.update(updateWrapper);
    }


    /**
     * 更新申请状态
     */
    public void updateApplyStatus(String cardApplyId, String reason) {
        try {
            log.info("更新申请状态，cardApplyId={}, reason={}", cardApplyId, reason);

            UpdateWrapper<CardApplyPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set(CardApplyPO.DB_COL_APPLY_STATUS, CardApplyStatusEnum.FAILED.getStatus());
            updateWrapper.set(CardApplyPO.DB_COL_REFUSE_REASON, reason);
            updateWrapper.set(CardApplyPO.DB_COL_UPDATE_TIME, new Date());
            updateWrapper.eq(CardApplyPO.DB_COL_APPLY_ID, cardApplyId);

            this.update(updateWrapper);

        } catch (Exception e) {
            log.error("更新申请状态异常，cardApplyId={}", cardApplyId, e);
        }
    }

}
