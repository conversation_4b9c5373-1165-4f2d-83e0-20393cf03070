package com.fenbei.fx.card.service.cardapply.impl;

import com.fenbei.fx.card.service.cardapply.dto.*;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardapply.po.CardApplyPO;
import com.fenbei.fx.card.service.cardapply.CardApplyService;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 国际卡操作申请 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Service
public class CardApplyServiceImpl extends BaseServiceImpl<CardApplyManager, CardApplyPO, CardApplyDTO> implements CardApplyService {

    @Override
    public CardApplyListResDTO listOne(final CardApplyListReqDTO cardApplyListReqDTO) {
        return manager.listOne(cardApplyListReqDTO);
    }

    @Override
    public Page<CardApplyPageResDTO> pagination(final CardApplyPageReqDTO cardApplyPageReqDTO, final Integer current,
                                                final Integer size) {
        return manager.pagination(cardApplyPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardApplyAddReqDTO cardApplyAddReqDTO) {
        return manager.add(cardApplyAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardApplyAddReqDTO cardApplyAddReqDTO) {
        return manager.addAllColumn(cardApplyAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardApplyAddReqDTO> cardApplyAddReqDTOList) {
        return manager.addBatchAllColumn(cardApplyAddReqDTOList);
    }

    @Override
    public CardApplyShowResDTO cardApplyDetail(final String applyId) {
        return manager.cardApplyDetail(applyId);
    }

    @Override
    public List<CardApplyShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardApplyModifyReqDTO cardApplyModifyReqDTO) {
        return manager.modify(cardApplyModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardApplyModifyReqDTO cardApplyModifyReqDTO) {
        return manager.modifyAllColumn(cardApplyModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardApplyRemoveReqDTO cardApplyRemoveReqDTO) {
        return manager.removeByParams(cardApplyRemoveReqDTO);
    }

    @Override
    public CardApplyShowResDTO createCardApply(CreateCardApplyReqDTO createCardApplyReqDTO) {
        return manager.createCardApply(createCardApplyReqDTO);
    }

    @Override
    public Boolean approvedCardApply(UpdateCardApplyReqDTO updateCardApplyReqDTO) {
        return manager.approvedCardApply(updateCardApplyReqDTO);
    }


    /**
     * Stereo运营审核，后续自动化，忽略此步骤
     * @param cardApplyId
     * @return
     */
    @Override
    public Boolean approvedCardApplyByStereo(String cardApplyId) {
        return manager.submitCard(cardApplyId);
    }

}
