package com.fenbei.fx.card.service.cardorder.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fenbei.fx.card.util.*;
import com.fenbeitong.expense.management.api.financeCostInfo.FinanceCostInfoService;
import com.fenbeitong.expense.management.api.financeCostInfo.dto.ReimburseCostInfoRpcDTO;
import com.fenbeitong.fxpay.api.enums.ExchangeType;
import com.fenbeitong.fxpay.api.interfaces.IExchangeRateService;
import com.fenbeitong.fxpay.api.vo.ExchangeRateReq;
import com.fenbeitong.fxpay.api.vo.ExchangeRateRes;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.saasplus.api.model.bank.vo.KeyValueBaseVO;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.common.constant.CheckStatusEnum;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.entity.KeyValueVO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.dao.cardorder.CardOrderDAO;
import com.fenbei.fx.card.dao.cardorder.po.CardOrderPO;
import com.fenbei.fx.card.dao.cardverificationflow.po.CardVerificationFlowPO;
import com.fenbei.fx.card.service.card.manager.CardManager;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.cardorder.converter.CardOrderConverter;
import com.fenbei.fx.card.service.cardorder.domain.CardOrderDO;
import com.fenbei.fx.card.service.cardorder.dto.*;
import com.fenbei.fx.card.service.cardverificationflow.dto.CardVerificationFlowDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderAddReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderListReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderListResDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderModifyReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderRemoveReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderShowResDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoAppPageReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoAppPageResDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoAppRemarkReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoAppShowResDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoWebPageReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoWebPageResDTO;
import com.fenbei.fx.card.service.cardorder.dto.FxVerificationReqDTO;
import com.fenbei.fx.card.service.cardverificationflow.manager.CardVerificationFlowManager;
import com.fenbei.fx.card.service.usercard.dto.FxWrongPaidRefundDTO;
import com.fenbei.fx.card.service.usercard.dto.TotalPrice;
import com.fenbei.fx.card.service.usercard.dto.UserCardTradeInfoDTO;
import com.fenbei.fx.card.service.usercard.dto.UserCardTradeInfosDTO;
import com.fenbei.fx.card.util.BizIdUtils;
import com.fenbei.fx.card.util.DateFormatUtil;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbei.fx.card.util.I18nUtils;
import com.fenbei.fx.card.util.MoneyNumberFormatUtil;
import com.fenbeitong.expense.management.api.virtual.IVirtualCardBudgetRpcService;
import com.fenbeitong.expense.management.api.virtual.dto.TradeRefundRpcDTO;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 国际卡订单 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class CardOrderManager extends BaseManagerImpl<CardOrderDAO, CardOrderPO, CardOrderDTO, CardOrderConverter> {
    @DubboReference
    IVirtualCardBudgetRpcService iVirtualCardBudgetRpcService;

    @DubboReference
    private FinanceCostInfoService financeCostInfoService ;
    @DubboReference
    private IExchangeRateService iExchangeRateService;

    public static CardOrderManager me() {
        return SpringUtil.getBean(CardOrderManager.class);
    }

    public List<CardOrderListResDTO> list(final CardOrderListReqDTO cardOrderListReqDTO) {
        CardOrderDTO paramsDTO = CardOrderDO.me().buildListParamsDTO(cardOrderListReqDTO);

        List<CardOrderDTO> cardOrderDTOList = super.findList(paramsDTO);

        return CardOrderDO.me().transferCardOrderListResDTOList(cardOrderDTOList);
    }

    public CardOrderListResDTO listOne(final CardOrderListReqDTO cardOrderListReqDTO) {
        CardOrderDTO paramsDTO = CardOrderDO.me().buildListParamsDTO(cardOrderListReqDTO);

        CardOrderDTO cardOrderDTO = super.findOne(paramsDTO);

        return CardOrderDO.me().transferCardOrderListResDTO(cardOrderDTO);
    }

    public Page<CardTradeInfoWebPageResDTO> pagination(final CardTradeInfoWebPageReqDTO cardTradeInfoWebPageReqDTO, final Integer current, final Integer size) {

        // 1. [Card] 查询条件：持卡人 卡号 卡类型
        QueryWrapper<CardPO> cardPOQueryWrapper = new QueryWrapper<>();
        if (Func.isNotBlank(cardTradeInfoWebPageReqDTO.getCardHolderName())) {
            cardPOQueryWrapper.like(CardPO.DB_COL_NAME_ON_CARD, cardTradeInfoWebPageReqDTO.getCardHolderName());
        }
        if (Func.isNotBlank(cardTradeInfoWebPageReqDTO.getMaskedCardNumber())) {
            cardPOQueryWrapper.like(CardPO.DB_COL_BANK_CARD_NO, cardTradeInfoWebPageReqDTO.getMaskedCardNumber());
        }
        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getBankCardType())) {
            cardPOQueryWrapper.eq(CardPO.DB_COL_CARD_FORM_FACTOR, cardTradeInfoWebPageReqDTO.getBankCardType());
        }

        List<String> fxCardIdList = null;
        List<CardPO> cardPOList = null;
        if (cardPOQueryWrapper.nonEmptyOfNormal()) {
            cardPOQueryWrapper.eq(CardPO.DB_COL_COMPANY_ID, UserAuthHolder.getCurrentUser().getCompany_id());
            cardPOList = CardManager.me().list(cardPOQueryWrapper);
            if (Func.isEmpty(cardPOList)) {
                return Page.empty(current, size);
            }
            fxCardIdList = cardPOList.stream().map(CardPO::getFxCardId).collect(Collectors.toList());
        }

        // 2. [CardOrder] 分页查询：消费类型 交易时间 交易币种 交易金额 核销状态
        QueryWrapper<CardOrderPO> webPageQueryWrapper = CardOrderDO.me().buildWebPageQueryWrapper(fxCardIdList, cardTradeInfoWebPageReqDTO);
        Page<CardOrderDTO> cardOrderDTOPage = super.findPage(webPageQueryWrapper, current, size);
        if (cardOrderDTOPage.isEmpty()) {
            return Page.empty(current, size);
        }

        // 3. [Card] 查询构建 fxCardIdAndCardPOMap
        Map<String, CardPO> fxCardIdAndCardPOMap = Maps.newHashMap();
        if (cardPOQueryWrapper.isEmptyOfNormal() ) {
            fxCardIdList = cardOrderDTOPage.getRecords().stream().map(CardOrderDTO::getFxCardId).collect(Collectors.toList());
            cardPOList = CardManager.me().list(new QueryWrapper<CardPO>().in(CardPO.DB_COL_FX_CARD_ID, fxCardIdList));
        }
        cardPOList.forEach(cardPO -> fxCardIdAndCardPOMap.put(cardPO.getFxCardId(), cardPO));

        return CardOrderDO.me().transferCardTradeInfoWebPageResDTOPage(cardOrderDTOPage, fxCardIdAndCardPOMap);
    }

    public Page<CardTradeInfoAppPageResDTO> pagination(final CardTradeInfoAppPageReqDTO cardTradeInfoAppPageReqDTO, final Integer current, final Integer size) {

        // [CardOrder] 分页查询：交易时间 交易金额 核销状态
        LambdaQueryWrapper<CardOrderPO> webPageQueryWrapper = CardOrderDO.me().buildAppPageQueryWrapper(cardTradeInfoAppPageReqDTO);
        Page<CardOrderDTO> cardOrderDTOPage = super.findPage(webPageQueryWrapper, current, size);
        if (cardOrderDTOPage.isEmpty()) {
            return Page.empty(current, size);
        }
        Map<String, CardPO> fxCardIdAndCardPOMap = getFxCardIdAndCardPOMap();
        return CardOrderDO.me().transferCardTradeInfoAppPageResDTOPage(cardOrderDTOPage, fxCardIdAndCardPOMap);
    }

    public Page<CardTradeInfoStereoPageResDTO> pagination(final CardTradeInfoStereoPageReqDTO reqDTO, final Integer current, final Integer size) {

        // 2. [CardOrder] 分页查询：消费类型 交易时间 交易币种 交易金额 核销状态
        QueryWrapper<CardOrderPO> webPageQueryWrapper = CardOrderDO.me().buildStereoPageQueryWrapper(reqDTO);
        Page<CardOrderDTO> cardOrderDTOPage = super.findPage(webPageQueryWrapper, current, size);
        if (cardOrderDTOPage.isEmpty()) {
            return Page.empty(current, size);
        }

        // 3. [Card] 查询构建 fxCardIdAndCardPOMap
        Map<String, CardPO> fxCardIdAndCardPOMap = Maps.newHashMap();

         List<String>  fxCardIdList = cardOrderDTOPage.getRecords().stream().map(CardOrderDTO::getFxCardId).collect(Collectors.toList());
         List<CardPO>  cardPOList = CardManager.me().list(new QueryWrapper<CardPO>().in(CardPO.DB_COL_FX_CARD_ID, fxCardIdList));

        cardPOList.forEach(cardPO -> fxCardIdAndCardPOMap.put(cardPO.getFxCardId(), cardPO));

        return CardOrderDO.me().transferCardTradeInfoStereoPageResDTOPage(cardOrderDTOPage, fxCardIdAndCardPOMap);
    }

    private Map<String, CardPO> getFxCardIdAndCardPOMap() {
        // [Card] 查询条件：company_id & employee_id
        QueryWrapper<CardPO> cardPOQueryWrapper = new QueryWrapper<>();
        cardPOQueryWrapper.eq(CardPO.DB_COL_COMPANY_ID, UserAuthHolder.getCurrentUser().getCompany_id());
        cardPOQueryWrapper.eq(CardPO.DB_COL_EMPLOYEE_ID, UserAuthHolder.getCurrentUser().getUser_id());

        // [Card] 查询构建 fxCardIdAndCardPOMap
        Map<String, CardPO> fxCardIdAndCardPOMap = Maps.newHashMap();
        List<CardPO> cardPOList = CardManager.me().list(cardPOQueryWrapper);
        cardPOList.forEach(cardPO -> fxCardIdAndCardPOMap.put(cardPO.getFxCardId(), cardPO));
        return fxCardIdAndCardPOMap;
    }

    public Boolean add(final CardOrderAddReqDTO cardOrderAddReqDTO) {
        CardOrderDO.me().checkCardOrderAddReqDTO(cardOrderAddReqDTO);

        CardOrderDTO addCardOrderDTO = CardOrderDO.me().buildAddCardOrderDTO(cardOrderAddReqDTO);

        return super.saveDTO(addCardOrderDTO);
    }

    public Boolean addAllColumn(final CardOrderAddReqDTO cardOrderAddReqDTO) {
        CardOrderDO.me().checkCardOrderAddReqDTO(cardOrderAddReqDTO);

        CardOrderDTO addCardOrderDTO = CardOrderDO.me().buildAddCardOrderDTO(cardOrderAddReqDTO);

        return super.saveAllColumn(addCardOrderDTO);
    }

    public Boolean addBatchAllColumn(final List<CardOrderAddReqDTO> cardOrderAddReqDTOList) {
        CardOrderDO.me().checkCardOrderAddReqDTOList(cardOrderAddReqDTOList);

        List<CardOrderDTO> addBatchCardOrderDTOList = CardOrderDO.me().buildAddBatchCardOrderDTOList(cardOrderAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardOrderDTOList);
    }

    public CardOrderShowResDTO show(final String id) {
        CardOrderDTO cardOrderDTO = super.findById(id);

        return CardOrderDO.me().transferCardOrderShowResDTO(cardOrderDTO);
    }

    public CardTradeInfoAppShowResDTO appShow(String bizNo,Integer transactionType) {
        CardOrderDTO params = new CardOrderDTO();
        params.setBizNo(bizNo);
        params.setType(transactionType);

        List<CardOrderDTO> cardOrderDTOList = super.findList(params);
        if (Func.isEmpty(cardOrderDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        Map<String, CardPO> fxCardIdAndCardPOMap = getFxCardIdAndCardPOMap();
        CardTradeInfoAppShowResDTO cardTradeInfoAppShowResDTO = CardOrderDO.me().transferCardTradeInfoAppShowResDTO(cardOrderDTOList.get(0), fxCardIdAndCardPOMap);
        //查询当前交易核销状态
        if (TransactionTypeEnum.CONSUME.getKey() == cardTradeInfoAppShowResDTO.getTransactionTypeCode()){
            if (cardTradeInfoAppShowResDTO.getCheckStatusCode() != 0 ) {
                cardTradeInfoAppShowResDTO.setHasWriteOffEntry(true);
                //查询费控接口  查询当前核销状态
                setWriteOffInfo(cardTradeInfoAppShowResDTO);
            }else {
                cardTradeInfoAppShowResDTO.setHasWriteOffEntry(false);
            }
        }else {
            cardTradeInfoAppShowResDTO.setHasWriteOffEntry(false);
        }

        return cardTradeInfoAppShowResDTO;
    }

    private void setWriteOffInfo(CardTradeInfoAppShowResDTO result){
        //添加查询核销状态
        if (result.getHasWriteOffEntry()){
            log.info("交易详情 查询单据核销信息  req = {}",result.getBizNo());
            List<ReimburseCostInfoRpcDTO> reimburseCostInfoRpcDTOS = financeCostInfoService.getReimburseCostInfoByBizId(Collections.singletonList(result.getBizNo()),5);
            log.info("交易详情 查询单据核销信息  req = {} , res = {}",result.getBizNo() , reimburseCostInfoRpcDTOS);
            if (!CollectionUtils.isEmpty(reimburseCostInfoRpcDTOS)){
                ReimburseCostInfoRpcDTO reimburseCostInfoRpcDTO = reimburseCostInfoRpcDTOS.get(0);
                if (reimburseCostInfoRpcDTO != null){
                    if (StringUtils.isNotBlank(reimburseCostInfoRpcDTO.getApplyId())){
                        result.setApplyId(reimburseCostInfoRpcDTO.getApplyId());
                        //存在核销单ID   已创建单据
                        if (reimburseCostInfoRpcDTO.getState() == 1){
                            result.setWriteOffStatus(getKeyValue(WriteOffShowEnum.NO_COMMIT, null));
                        }else  if (reimburseCostInfoRpcDTO.getState() == 2){
                            result.setWriteOffStatus(getKeyValue(WriteOffShowEnum.PENDING, null));
                        }else  if (reimburseCostInfoRpcDTO.getState() == 4){
                            result.setWriteOffStatus(getKeyValue(WriteOffShowEnum.PASS, null));
                        }else {
                            result.setWriteOffStatus(getKeyValue(WriteOffShowEnum.NO_COMMIT, null));
                        }
                    }else if (reimburseCostInfoRpcDTO.getCostId() != null){
                        result.setCostId(reimburseCostInfoRpcDTO.getCostId());
                        result.setWriteOffStatus(getKeyValue(WriteOffShowEnum.BIND_COST, reimburseCostInfoRpcDTO.getCostCategory()));
                    }
                }else {
                    result.setWriteOffStatus(getKeyValue(WriteOffShowEnum.NO_COST, null));
                }
            }else {
                result.setWriteOffStatus(getKeyValue(WriteOffShowEnum.NO_COST, null));
            }
            checkWriteOffStatus(result);
        }
    }

    private void checkWriteOffStatus(CardTradeInfoAppShowResDTO result){
        if (result.getCheckStatusCode() != null && result.getCheckStatusCode() == CheckStatusEnum.SUCCEED.getKey() && StringUtils.isBlank(result.getApplyId())){
            log.info("交易详情 查询单据核销信息 核销成功单据 费控无applyId 取消入口  result = {}",JSON.toJSONString(result));
            result.setHasWriteOffEntry(false);
        }
        if (result.getCheckStatusCode() != null && result.getCheckStatusCode() == CheckStatusEnum.CHECKING.getKey() && StringUtils.isBlank(result.getApplyId())){
            log.info("交易详情 查询单据核销信息 核销中单据 费控无applyId 取消入口  result = {}",JSON.toJSONString(result));
            result.setHasWriteOffEntry(false);
        }
        if (result.getCheckStatusCode() != null && result.getCheckStatusCode() == CheckStatusEnum.NONE.getKey()){
            log.info("交易详情 查询单据核销信息 无需核销 取消入口  result = {}",JSON.toJSONString(result));
            result.setHasWriteOffEntry(false);
        }
    }

    private KeyValueBaseVO getKeyValue (WriteOffShowEnum showEnum , String str){
        if (StringUtils.isNotBlank(str)){
            return new KeyValueBaseVO(showEnum.getKey(), showEnum.getValue() + "(" + str + ")" );
        }else {
            return new KeyValueBaseVO(showEnum.getKey(), showEnum.getValue());
        }
    }

    public List<CardOrderShowResDTO> showByIds(final List<String> ids) {
        CardOrderDO.me().checkIds(ids);

        List<CardOrderDTO> cardOrderDTOList = super.findBatchIds(ids);

        return CardOrderDO.me().transferCardOrderShowResDTOList(cardOrderDTOList);
    }

    public Boolean modify(final CardOrderModifyReqDTO cardOrderModifyReqDTO) {
        CardOrderDO.me().checkCardOrderModifyReqDTO(cardOrderModifyReqDTO);

        CardOrderDTO modifyCardOrderDTO = CardOrderDO.me().buildModifyCardOrderDTO(cardOrderModifyReqDTO);

        return super.modifyById(modifyCardOrderDTO);
    }

    public Boolean modifyAllColumn(final CardOrderModifyReqDTO cardOrderModifyReqDTO) {
        CardOrderDO.me().checkCardOrderModifyReqDTO(cardOrderModifyReqDTO);

        CardOrderDTO modifyCardOrderDTO = CardOrderDO.me().buildModifyCardOrderDTO(cardOrderModifyReqDTO);

        return super.modifyAllColumnById(modifyCardOrderDTO);
    }

    public Boolean removeByParams(final CardOrderRemoveReqDTO cardOrderRemoveReqDTO) {
        CardOrderDO.me().checkCardOrderRemoveReqDTO(cardOrderRemoveReqDTO);

        CardOrderDTO removeCardOrderDTO = CardOrderDO.me().buildRemoveCardOrderDTO(cardOrderRemoveReqDTO);

        return super.remove(removeCardOrderDTO);
    }

    @Override
    protected CardOrderPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardOrderPO();
        }

        return BeanUtil.toBean(map, CardOrderPO.class);
    }

    @Override
    protected CardOrderDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardOrderDTO();
        }

        return BeanUtil.toBean(map, CardOrderDTO.class);
    }

    public List<UserCardTradeInfoDTO> latestTradeInfo(final String fxCardId){
        List<CardOrderDTO> trade = getByFxCardId(fxCardId);
        List<UserCardTradeInfoDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trade)){
            for (CardOrderDTO cardOrder: trade) {
                list.add(convertToUserCardTradeInfoDTO(cardOrder));
            }
        }
        return list;
    }

    public List<UserCardTradeInfoDTO> latestTradeInfoByEmployeeId(final String employeeId){
        List<CardOrderDTO> trade = getByEmployeeId4Latest(employeeId);
        List<UserCardTradeInfoDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trade)){
            for (CardOrderDTO cardOrder: trade) {
                list.add(convertToUserCardTradeInfoDTO(cardOrder));
            }
        }
        return list;
    }

    public UserCardTradeInfosDTO queryUnCheckInfo(String employeeId){
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardOrderPO.DB_COL_CHECK_STATUS,CheckStatusEnum.UNCHECK.getKey());
        List<CardOrderDTO> cardOrderPOS = this.findList(queryWrapper);
        UserCardTradeInfosDTO userCardTradeInfosDTO =  convertToUserCardTradeInfoDTOList(cardOrderPOS);
        List<UserCardTradeInfoDTO> list = this.latestTradeInfoByEmployeeId(employeeId);
        userCardTradeInfosDTO.setTradeInfos(list);
        return userCardTradeInfosDTO;
    }

    public UserCardTradeInfosDTO queryUnCheckInfoByFxCard(String fxCardId){
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_FX_CARD_ID, fxCardId);
        queryWrapper.eq(CardOrderPO.DB_COL_CHECK_STATUS,CheckStatusEnum.UNCHECK.getKey());
        List<CardOrderDTO> cardOrderPOS = this.findList(queryWrapper);
        Integer count = this.count(queryWrapper);
        UserCardTradeInfosDTO userCardTradeInfosDTO =  convertToUserCardTradeInfoDTOList(cardOrderPOS);
        List<UserCardTradeInfoDTO> list = this.latestTradeInfo(fxCardId);
        userCardTradeInfosDTO.setTradeInfos(list);
        userCardTradeInfosDTO.setTotalCount(count);
        return userCardTradeInfosDTO;
    }

    public UserCardTradeInfosDTO convertToUserCardTradeInfoDTOList(List<CardOrderDTO> cardOrderPOS){
        UserCardTradeInfosDTO userCardTradeInfosDTO = new UserCardTradeInfosDTO();
        if (!CollectionUtils.isEmpty(cardOrderPOS)){
            BigDecimal uncheckConsume = cardOrderPOS.stream().map(CardOrderDTO::getUncheckedAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
            CardOrderDTO first = cardOrderPOS.stream().findFirst().get();
            CurrencyEnum currency = CurrencyEnum.getCurrencyByCodeIgnoreCase(first.getTradeCurrency());
            if(ObjectUtils.isEmpty(currency)){
                currency=CurrencyEnum.USD;
            }
            userCardTradeInfosDTO.setUncheckConsume(uncheckConsume);
            userCardTradeInfosDTO.setUncheckConsumeDesc(convertToTotalPrice(uncheckConsume,currency));
            userCardTradeInfosDTO.setUncheckUse(cardOrderPOS.size());
        }else {
            userCardTradeInfosDTO.setUncheckConsume(BigDecimal.ZERO);
            userCardTradeInfosDTO.setUncheckConsumeDesc(convertToTotalPrice(BigDecimal.ZERO,CurrencyEnum.USD));
            userCardTradeInfosDTO.setUncheckUse(0);
        }
        return userCardTradeInfosDTO;
    }
    public TotalPrice convertToTotalPrice(BigDecimal amount, CurrencyEnum currency) {
        if (ObjectUtils.isEmpty(currency)) {
            currency = CurrencyEnum.USD;
        }
        BigDecimal amountYuan = BigDecimalUtils.fen2yuan(amount);
        TotalPrice totalPrice = new TotalPrice();
        totalPrice.setPrice(amountYuan);
        totalPrice.setShowPrice(CurrencyNumberFormatUtil.moneyFormart(currency,amountYuan));
        totalPrice.setColor("#333333");
        return totalPrice;
    }
    public BigDecimal convertToAmount(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return NumberUtil.div(amount, BigDecimal.valueOf(100), 2);
    }

    private UserCardTradeInfoDTO convertToUserCardTradeInfoDTO(CardOrderDTO cardOrderDTO){
        UserCardTradeInfoDTO userCardTradeInfoDTO = new UserCardTradeInfoDTO();
        userCardTradeInfoDTO.setOrderId(cardOrderDTO.getBizNo());
        userCardTradeInfoDTO.setTradeAddress(cardOrderDTO.getTradeAddress());
        KeyValueVO checkStatus = new KeyValueVO();
        checkStatus.setKey(cardOrderDTO.getCheckStatus());
        checkStatus.setValue(I18nUtils.transferI18nMessage(CheckStatusEnum.getEnum(cardOrderDTO.getCheckStatus()).getMsgForClient()));
        //finhub 和 noc 的枚举不一致,以noc枚举为准
        if (CheckStatusEnum.getEnum(cardOrderDTO.getCheckStatus()).getKey() == CheckStatusEnum.UNCHECK.getKey()){
            checkStatus.setValue(I18nUtils.transferI18nMessage("待核销"));
        }
        userCardTradeInfoDTO.setCheckStatus(checkStatus);
        userCardTradeInfoDTO.setBankAccountNo(cardOrderDTO.getMaskedCardNumber());//此处不展示卡号
        userCardTradeInfoDTO.setBankAccountNoMasked(cardOrderDTO.getMaskedCardNumber());
        userCardTradeInfoDTO.setCreateTime(cardOrderDTO.getTradeTime());
        userCardTradeInfoDTO.setCreateTimeShow(DateFormatUtil.tradeDateFormat(cardOrderDTO.getTradeTime()));
//        userCardTradeInfoDTO.setMonthType();
        userCardTradeInfoDTO.setShopName(cardOrderDTO.getTradeName());

        //结算金额的折算币种
        TotalPrice tradeBillPrice = new TotalPrice();
        tradeBillPrice.setPrice(cardOrderDTO.getBillTradeAmount());
        tradeBillPrice.setShowPrice(TransactionTypeEnum.getPlusOrMinusValue(cardOrderDTO.getType()) + CurrencyEnum.getCurrencyByCode(cardOrderDTO.getBillTradeCurrency()).getSymbol() + MoneyNumberFormatUtil.formart(BigDecimalUtils.fen2yuan(cardOrderDTO.getBillTradeAmount())));
        tradeBillPrice.setColor("#333333");
        userCardTradeInfoDTO.setTotalPrice(tradeBillPrice);
        //消费金额的交易币种（消费币种）
        TotalPrice totalTradePrice = new TotalPrice();
        totalTradePrice.setPrice(cardOrderDTO.getTradeAmount());
        totalTradePrice.setShowPrice(TransactionTypeEnum.getPlusOrMinusValue(cardOrderDTO.getType()) + CurrencyEnum.getCurrencyByCode(cardOrderDTO.getTradeCurrency()).getSymbol() + MoneyNumberFormatUtil.formart(BigDecimalUtils.fen2yuan(cardOrderDTO.getTradeAmount())));
        totalTradePrice.setColor("#333333");
        userCardTradeInfoDTO.setTradePrice(totalTradePrice);

        if (CheckStatusEnum.UNCHECK.getKey() != cardOrderDTO.getCheckStatus()) {
            userCardTradeInfoDTO.setUncheckConsume(cardOrderDTO.getUncheckedAmount());
            userCardTradeInfoDTO.setUncheckUse(1);
        }
        KeyValueVO transactionType = new KeyValueVO();
        transactionType.setKey(cardOrderDTO.getType());
        transactionType.setValue(I18nUtils.transferI18nMessage(TransactionTypeEnum.getEnum(cardOrderDTO.getType()).getValue()));
        userCardTradeInfoDTO.setTransactionType(transactionType);
        return userCardTradeInfoDTO;
    }

    public List<CardOrderDTO> getByFxCardId(String cardId) {
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_FX_CARD_ID, cardId);
        queryWrapper.eq(CardOrderPO.DB_BASE_ORDER_SHOW, 1);
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        queryWrapper.last("limit 5");
        return this.findList(queryWrapper);
    }

    public List<CardOrderDTO> getByEmployeeId4Latest(String employeeId) {
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardOrderPO.DB_BASE_ORDER_SHOW, 1);
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        queryWrapper.last("limit 3");
        return this.findList(queryWrapper);
    }

    public List<CardOrderDTO> getUnchecksByEmployeeId(String employeeId) {
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE, TransactionTypeEnum.CONSUME.getKey());
        queryWrapper.eq(CardOrderPO.DB_COL_CHECK_STATUS, CheckStatusEnum.UNCHECK.getKey());
        queryWrapper.gt(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT, BigDecimal.ZERO);
        queryWrapper.eq(CardOrderPO.DB_BASE_APPLY_BIND, ApplyBindStatus.NO.getKey());
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        return this.findList(queryWrapper);
    }


    public List<CardOrderDTO> getUnchecksByOrderIds(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)){
            return new ArrayList<>();
        }
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(CardOrderPO.DB_COL_BIZ_NO, orderIds);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE, TransactionTypeEnum.CONSUME.getKey());
        queryWrapper.eq(CardOrderPO.DB_COL_CHECK_STATUS, CheckStatusEnum.UNCHECK.getKey());
        queryWrapper.gt(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT, BigDecimal.ZERO);
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        return this.findList(queryWrapper);
    }

    public List<CardOrderDTO> getOrderByOrderIds(List<Integer> allOrderType,List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)){
            return new ArrayList<>();
        }
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(CardOrderPO.DB_COL_BIZ_NO, orderIds);
        queryWrapper.in(CardOrderPO.DB_COL_TYPE, allOrderType);
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        return this.findList(queryWrapper);
    }

    public List<CardOrderDTO> getUnchecksByParams(UncheckOrderReqDTO rpcReqDTO) {
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_CHECK_STATUS, CheckStatusEnum.UNCHECK.getKey());
        queryWrapper.gt(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT, BigDecimal.ZERO);
        queryWrapper.eq(CardOrderPO.DB_BASE_APPLY_BIND, ApplyBindStatus.NO.getKey());
        addOtherCondition(rpcReqDTO, queryWrapper);
        return this.findList(queryWrapper);
    }

    public List<CardOrderDTO> getUnchecksByParamsAndCostId(UncheckOrderReqDTO rpcReqDTO) {
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_BASE_COST_ID, rpcReqDTO.getCostId());
        addOtherCondition(rpcReqDTO, queryWrapper);
        return this.findList(queryWrapper);
    }

    private void addOtherCondition(UncheckOrderReqDTO rpcReqDTO, QueryWrapper<CardOrderPO> queryWrapper) {
        queryWrapper.eq(CardOrderPO.DB_COL_EMPLOYEE_ID, rpcReqDTO.getEmployId());
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE, TransactionTypeEnum.CONSUME.getKey());
        queryWrapper.ge(Objects.nonNull(rpcReqDTO.getBeginCnyTradeAmount()), CardOrderPO.DB_BASE_CNY_TRADE_AMOUNT, rpcReqDTO.getBeginCnyTradeAmount());
        queryWrapper.le(Objects.nonNull(rpcReqDTO.getEndCnyTradeAmount()), CardOrderPO.DB_BASE_CNY_TRADE_AMOUNT, rpcReqDTO.getEndCnyTradeAmount());

        queryWrapper.eq(StringUtils.isNotBlank(rpcReqDTO.getBillTradeCurrency()), CardOrderPO.DB_COL_BILL_TRADE_CURRENCY, rpcReqDTO.getBillTradeCurrency());
        queryWrapper.ge(Objects.nonNull(rpcReqDTO.getBeginBillTradeAmount()), CardOrderPO.DB_COL_BILL_TRADE_AMOUNT, BigDecimalUtils.yuan2fen(rpcReqDTO.getBeginBillTradeAmount()));
        queryWrapper.le(Objects.nonNull(rpcReqDTO.getEndBillTradeAmount()), CardOrderPO.DB_COL_BILL_TRADE_AMOUNT, BigDecimalUtils.yuan2fen(rpcReqDTO.getEndBillTradeAmount()));

        queryWrapper.eq(StringUtils.isNotBlank(rpcReqDTO.getTradeCurrency()), CardOrderPO.DB_COL_TRADE_CURRENCY, rpcReqDTO.getTradeCurrency());
        queryWrapper.ge(Objects.nonNull(rpcReqDTO.getBeginTradeAmount()), CardOrderPO.DB_COL_TRADE_AMOUNT, BigDecimalUtils.yuan2fen(rpcReqDTO.getBeginTradeAmount()));
        queryWrapper.le(Objects.nonNull(rpcReqDTO.getEndTradeAmount()), CardOrderPO.DB_COL_TRADE_AMOUNT, BigDecimalUtils.yuan2fen(rpcReqDTO.getEndTradeAmount()));

        queryWrapper.ge(StringUtils.isNotBlank(rpcReqDTO.getBeginTradeTime()), CardOrderPO.DB_COL_TRADE_TIME, rpcReqDTO.getBeginTradeTime());
        queryWrapper.le(StringUtils.isNotBlank(rpcReqDTO.getEndTradeTime()), CardOrderPO.DB_COL_TRADE_TIME, rpcReqDTO.getEndTradeTime());

        queryWrapper.orderByDesc(CardOrderPO.DB_COL_TRADE_TIME);
    }

    public Boolean appRemark(CardTradeInfoAppRemarkReqDTO cardTradeInfoAppRemarkReqDTO) {
        UpdateWrapper<CardOrderPO> updateWrapper = new UpdateWrapper<>();
//        if (Func.isNotBlank(cardTradeInfoAppRemarkReqDTO.getTransactionId())) {
//            updateWrapper.eq(CardOrderPO.DB_COL_ID, cardTradeInfoAppRemarkReqDTO.getTransactionId());
//        }

        if (Func.isNotBlank(cardTradeInfoAppRemarkReqDTO.getOrderId())) {
            updateWrapper.eq(CardOrderPO.DB_COL_BIZ_NO, cardTradeInfoAppRemarkReqDTO.getOrderId());
//
        }
        if (cardTradeInfoAppRemarkReqDTO.getTransactionType() != null){
            updateWrapper.eq(CardOrderPO.DB_COL_TYPE, cardTradeInfoAppRemarkReqDTO.getTransactionType());
        }

        updateWrapper.set(CardOrderPO.DB_COL_TRADE_REMARK, cardTradeInfoAppRemarkReqDTO.getRemark());
        return super.update(updateWrapper);
    }

    /**
     * 错花还款更新
     * @return Boolean
     */
    public Boolean updateForWrongPaidRefund(String id,BigDecimal uncheckedAmount,BigDecimal needNotCheckAmount) {
        UpdateWrapper<CardOrderPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardOrderPO.DB_COL_ID, id);
        updateWrapper.set(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT, uncheckedAmount);
        updateWrapper.set(CardOrderPO.DB_BASE_NEED_NOT_CHECK_AMOUNT, needNotCheckAmount);
        if (uncheckedAmount.compareTo(BigDecimal.ZERO) <= 0){
            updateWrapper.set(CardOrderPO.DB_COL_CHECK_STATUS, CheckStatusEnum.NONE.getKey());
        }
        return super.update(updateWrapper);
    }

    public CardOrderDTO findByTradeId(String tradeId,Integer tradeType){
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_BASE_TRADE_ID, tradeId);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE,tradeType);
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        queryWrapper.last("limit 1");
        return super.findOne(queryWrapper);
    }

    public List<CardOrderDTO> findListByTradeId(String tradeId,Integer tradeType){
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_BASE_TRADE_ID, tradeId);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE,tradeType);
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        return super.findList(queryWrapper);
    }

    public CardOrderDTO findByTradeIdAndSub(String tradeId, String subTradeId, Integer tradeType) {
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_BASE_TRADE_ID, tradeId);
        queryWrapper.eq(CardOrderPO.DB_BASE_SUB_TRADE_ID, subTradeId);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE,tradeType);
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        queryWrapper.last("limit 1");
        return super.findOne(queryWrapper);
    }

    private List<CardOrderDTO> getOrderByBizNos(List<String> bizNos){
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(CardOrderPO.DB_COL_BIZ_NO, bizNos);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE, CardFlowOperationTypeEnum.CONSUME.getCode());
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        return this.findList(queryWrapper);
    }

    private CardOrderDTO selectOneByBizNo(String bizNo){
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_BIZ_NO, bizNo);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE, CardFlowOperationTypeEnum.CONSUME.getCode());
        return this.findOne(queryWrapper);
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void costBind(List<String> bizNos, Integer costId){
        log.info("====创建费用开始costBind:{}",bizNos);
        List<CardOrderDTO> cardOrderDTOS = getOrderByBizNos(bizNos);
        cardOrderDTOS.forEach(order -> {
            if (order.getApplyBind().equals(ApplyBindStatus.YES.getKey())) {
                throw new FinhubException(GlobalCoreResponseCode.EXCEPTION.getCode(), "该交易记录已绑定，请检查");
            }
            UpdateWrapper<CardOrderPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set(CardOrderPO.DB_BASE_APPLY_BIND,ApplyBindStatus.YES.getKey());
            updateWrapper.set(CardOrderPO.DB_BASE_COST_ID,costId);
            updateWrapper.eq(CardOrderPO.DB_COL_BIZ_NO,order.getBizNo());
            this.update(updateWrapper);
        });
        log.info("====创建费用结束costBind:{}",bizNos);
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void costUnBind(List<String> bizNos, Integer costId){
        log.info("====解绑费用开始costBind:{}",bizNos);
        List<CardOrderDTO> cardOrderDTOS = getOrderByBizNos(bizNos);
        cardOrderDTOS.forEach(order -> {
            if ( !costId.equals(Integer.valueOf(order.getCostId()))) {
                throw new FinhubException(GlobalCoreResponseCode.EXCEPTION.getCode(), "解绑费用id与绑定费用id不一致");
            }
            UpdateWrapper<CardOrderPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set(CardOrderPO.DB_BASE_APPLY_BIND,ApplyBindStatus.NO.getKey());
            updateWrapper.set(CardOrderPO.DB_BASE_COST_ID,null);
            updateWrapper.eq(CardOrderPO.DB_COL_BIZ_NO,order.getBizNo());
            this.update(updateWrapper);
        });
        log.info("====解绑费用开始costBind:{}",bizNos);
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public boolean applyInit(FxVerificationReqDTO initDTO){
        checkVerItemInfo(initDTO);
        BigDecimal allAmount = BigDecimal.ZERO;
        //计算审核中金额更新金额
        initDTO.getItems().stream().filter(p->TransactionTypeEnum.isConsume(p.getType())).forEach(order -> {
            CardOrderDTO cardOrderDTO = selectOneByBizNo(order.getBizNo());
            UpdateWrapper<CardOrderPO> updateWrapper = new UpdateWrapper<>();
            BigDecimal unCheckAmount = cardOrderDTO.getUncheckedAmount().subtract(BigDecimalUtils.yuan2fen(order.getAmount()));
            BigDecimal checkingAmount = cardOrderDTO.getCheckingAmount().add(BigDecimalUtils.yuan2fen(order.getAmount()));
            updateWrapper.set(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT,unCheckAmount);
            updateWrapper.set(CardOrderPO.DB_BASE_CHECKING_AMOUNT,checkingAmount);
            updateWrapper.set(CardOrderPO.DB_COL_CHECK_STATUS,CheckStatusEnum.CHECKING.getKey());
            updateWrapper.eq(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT,cardOrderDTO.getUncheckedAmount());
            updateWrapper.eq(CardOrderPO.DB_BASE_CHECKING_AMOUNT,cardOrderDTO.getCheckingAmount());
            updateWrapper.eq(CardOrderPO.DB_COL_ID,cardOrderDTO.getId());
            log.info( "更新审核中记录 bizNo = {} ,checkIngAmount = {},uncheckAmount = {}",order.getBizNo() ,unCheckAmount , checkingAmount);
            this.update(updateWrapper);
        });
        //处理额度申请单  需要拆分金额
        for (FxVerificationReqDTO.Item item : initDTO.getItems()){
            allAmount = allAmount.add(item.getAmount());
        }

        //申请单核销
        applyVerificationInfo(allAmount ,initDTO);
        return true ;
    }

    private CardVerificationFlowPO BuildVerificationFlow(CardCreditManagerDTO cardCreditManagerDTO ,Integer type ,BigDecimal checkAmount ,String verification){
        CardVerificationFlowPO cardVerificationFlowPO = new CardVerificationFlowPO();
        cardVerificationFlowPO.setVerificationId(verification);
        cardVerificationFlowPO.setCheckAmount(checkAmount);
        cardVerificationFlowPO.setCompanyId(cardCreditManagerDTO.getCompanyId());
        cardVerificationFlowPO.setCreateTime(new Date());
        cardVerificationFlowPO.setUpdateTime(new Date());
        cardVerificationFlowPO.setCreditId(cardCreditManagerDTO.getId());
        cardVerificationFlowPO.setFxCardId(cardCreditManagerDTO.getFxCardId());
        cardVerificationFlowPO.setType(type);
        cardVerificationFlowPO.setEmployeeId(cardCreditManagerDTO.getEmployeeId());
        return cardVerificationFlowPO;
    }

    private void applyVerificationInfo(BigDecimal verAllAmount , FxVerificationReqDTO initDTO){
        String employeeId = initDTO.getEmployeeId();
        String verificationId = initDTO.getVerificationId();
        List<String> applyTransNos = initDTO.getApplyItems().stream().map(p -> p.getApplyTransNo()).collect(Collectors.toList());
        List<CardCreditManagerDTO> cardCreditManagerDTOS = CardCreditManagerManager.me().queryByApplyTransNos(employeeId,applyTransNos);
        if(CollectionUtils.isEmpty(cardCreditManagerDTOS)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.CREDIT_NOT_EXIST_ERROR);
        }
        BigDecimal totalUncheckAmount = cardCreditManagerDTOS.stream().map(CardCreditManagerDTO::getUncheckedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimalUtils.yuan2fen(verAllAmount).compareTo(totalUncheckAmount) > 0){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.CREDIT_UNCHECK_AMOUNT_ERROR);
        }

        Map<String, CardCreditManagerDTO> collect = cardCreditManagerDTOS.stream().collect(Collectors.toMap(CardCreditManagerDTO::getApplyTransNo, Function.identity(), (key1, key2) -> key1));

        initDTO.getApplyItems().forEach(p->{
            BigDecimal verAmount = BigDecimalUtils.yuan2fen(p.getAmount());
            CardCreditManagerDTO creditManagerDTO = collect.get(p.getApplyTransNo());
            if (Objects.isNull(creditManagerDTO)){
                log.warn("当前单据不存在待核销的金额，applyTransNo={},verAmount={}", p.getApplyTransNo(), verAmount);
                throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXIST_CREDIT_UNCHECK_AMOUNT_ERROR);
            }
            if (verAmount.compareTo(creditManagerDTO.getUncheckedAmount()) > 0){
                log.warn("当前单据申请核销金额大于未核销金额，applyTransNo={},verAmount={},uncheckedAmount={}", p.getApplyTransNo(), verAmount, creditManagerDTO.getUncheckedAmount());
                throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXIST_CREDIT_UNCHECK_AMOUNT_ERROR);
            }
            CardCreditManagerManager.me().updateCheckAmount(creditManagerDTO.getId(),null, verAmount, CheckTypeEnum.SUBMIT.getCode());
            log.info("申请单更新核销中金额 amount = {} ,employeeId = {}, 查询到申请单信息 = {}",verAmount, employeeId,JSON.toJSONString(creditManagerDTO));
            //添加核销单核销记录
            CardVerificationFlowPO cardVerificationFlowPO = BuildVerificationFlow(creditManagerDTO,CheckTypeEnum.SUBMIT.getCode(),verAmount,verificationId);
            CardVerificationFlowManager.me().save(cardVerificationFlowPO);
        });
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public boolean applyDel(FxVerificationReqDTO delDTO){
        checkBaseVerItemInfo(delDTO);
        //解除核销中信息 1.查询是否存在核销单 2.解除消费订单核销中金额 3.关联申请单解除金额 4.添加流水
        List<CardVerificationFlowDTO> verificationFlowDTOS = CardVerificationFlowManager.me().queryCardVerificationInfoByFxCardIdAdVerificationId(delDTO.getVerificationId());
        if (CollectionUtils.isEmpty(verificationFlowDTOS)){
            throw new FinhubException(GlobalCoreResponseCode.VERIFICATION_NO_VERIFICATION_ORDER.getCode());
        }
        delDTO.getItems().stream().filter(p->TransactionTypeEnum.isConsume(p.getType())).forEach(order -> {
            CardOrderDTO cardOrderDTO = selectOneByBizNo(order.getBizNo());
            UpdateWrapper<CardOrderPO> updateWrapper = new UpdateWrapper<>();
            BigDecimal unCheckAmount = cardOrderDTO.getUncheckedAmount().add(cardOrderDTO.getCheckingAmount());
            BigDecimal checkingAmount = cardOrderDTO.getCheckingAmount().subtract(cardOrderDTO.getCheckingAmount());
            updateWrapper.set(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT,unCheckAmount);
            updateWrapper.set(CardOrderPO.DB_BASE_CHECKING_AMOUNT,checkingAmount);
            updateWrapper.set(CardOrderPO.DB_COL_CHECK_STATUS,CheckStatusEnum.UNCHECK.getKey());
            updateWrapper.eq(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT,cardOrderDTO.getUncheckedAmount());
            updateWrapper.eq(CardOrderPO.DB_BASE_CHECKING_AMOUNT,cardOrderDTO.getCheckingAmount());
            updateWrapper.eq(CardOrderPO.DB_COL_ID,cardOrderDTO.getId());
            log.info( "更新审核撤回/拒绝记录 bizNo = {} ,unCheckAmount = {},checkingAmount = {}",order.getBizNo() ,unCheckAmount , checkingAmount);
            this.update(updateWrapper);
        });
        verificationFlowDTOS.forEach( verOrder ->{
            CardCreditManagerManager.me().updateCheckAmount(verOrder.getCreditId(),null,verOrder.getCheckAmount() , CheckTypeEnum.REFUSE.getCode());
            //添加核销单核销记录
            CardCreditManagerDTO cardCreditManagerDTO = CardCreditManagerManager.me().getById(verOrder.getCreditId());
            CardVerificationFlowPO cardVerificationFlowPO = BuildVerificationFlow(cardCreditManagerDTO,CheckTypeEnum.REFUSE.getCode(),verOrder.getCheckAmount(),delDTO.getVerificationId());
            CardVerificationFlowManager.me().save(cardVerificationFlowPO);
        } );

        return true ;
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public boolean applyDisCardDel(FxVerificationReqDTO delDTO){
        checkBaseVerItemInfo(delDTO);
        List<CardVerificationFlowDTO> verificationFlowDTOS = CardVerificationFlowManager.me().queryCardVerificationInfoByFxCardIdAdVerificationId(delDTO.getVerificationId());
        if (CollectionUtils.isEmpty(verificationFlowDTOS)){
            throw new FinhubException(GlobalCoreResponseCode.VERIFICATION_NO_VERIFICATION_ORDER.getCode());
        }
        delDTO.getItems().stream().filter(p->TransactionTypeEnum.isConsume(p.getType())).forEach(order -> {
            CardOrderDTO cardOrderDTO = selectOneByBizNo(order.getBizNo());
            UpdateWrapper<CardOrderPO> updateWrapper = new UpdateWrapper<>();
            BigDecimal unCheckAmount = cardOrderDTO.getUncheckedAmount().add(cardOrderDTO.getCheckedAmount());
            BigDecimal checkedAmount = cardOrderDTO.getCheckedAmount().subtract(cardOrderDTO.getCheckedAmount());
            updateWrapper.set(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT,unCheckAmount);
            updateWrapper.set(CardOrderPO.DB_BASE_CHECKED_AMOUNT,checkedAmount);
            updateWrapper.set(CardOrderPO.DB_COL_CHECK_STATUS,CheckStatusEnum.UNCHECK.getKey());
            updateWrapper.eq(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT,cardOrderDTO.getUncheckedAmount());
            updateWrapper.eq(CardOrderPO.DB_BASE_CHECKED_AMOUNT,cardOrderDTO.getCheckedAmount());
            updateWrapper.eq(CardOrderPO.DB_COL_ID,cardOrderDTO.getId());
            log.info( "applyDisCardDel核销撤回记录 bizNo = {} ,unCheckAmount = {},checkedAmount = {}",order.getBizNo() ,unCheckAmount , checkedAmount);
            this.update(updateWrapper);
        });
        verificationFlowDTOS.forEach( verOrder ->{
            CardCreditManagerManager.me().updateCheckAmount(verOrder.getCreditId(),verOrder.getCheckAmount(),null , CheckTypeEnum.ROLLBACK.getCode());
            //添加核销单核销记录
            CardCreditManagerDTO cardCreditManagerDTO = CardCreditManagerManager.me().getById(verOrder.getCreditId());
            CardVerificationFlowPO cardVerificationFlowPO = BuildVerificationFlow(cardCreditManagerDTO,CheckTypeEnum.ROLLBACK.getCode(),verOrder.getCheckAmount(),delDTO.getVerificationId());
            CardVerificationFlowManager.me().save(cardVerificationFlowPO);
        } );
        return true;
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public boolean applyDoneNew(FxVerificationReqDTO doneDTO){
        checkBaseVerItemInfo(doneDTO);
        //核销完成  1：查询核销单 2：订单更新核销完成金额   3：申请单更新核销完成金额  4：添加流水
        List<CardVerificationFlowDTO> verificationFlowDTOS = CardVerificationFlowManager.me().queryCardVerificationInfoByFxCardIdAdVerificationId(doneDTO.getVerificationId());
        if (CollectionUtils.isEmpty(verificationFlowDTOS)){
            throw new FinhubException(GlobalCoreResponseCode.VERIFICATION_NO_VERIFICATION_ORDER.getCode());
        }
        doneDTO.getItems().stream().filter(p->TransactionTypeEnum.isConsume(p.getType())).forEach(order -> {
            CardOrderDTO cardOrderDTO = selectOneByBizNo(order.getBizNo());
            UpdateWrapper<CardOrderPO> updateWrapper = new UpdateWrapper<>();
            BigDecimal checkedAmount = cardOrderDTO.getCheckedAmount().add(cardOrderDTO.getCheckingAmount());
            BigDecimal checkingAmount = cardOrderDTO.getCheckingAmount().subtract(cardOrderDTO.getCheckingAmount());
            updateWrapper.set(CardOrderPO.DB_BASE_CHECKED_AMOUNT,checkedAmount);
            updateWrapper.set(CardOrderPO.DB_BASE_CHECKING_AMOUNT,checkingAmount);
            updateWrapper.set(CardOrderPO.DB_COL_CHECK_STATUS,CheckStatusEnum.SUCCEED.getKey());
            updateWrapper.eq(CardOrderPO.DB_BASE_CHECKED_AMOUNT,cardOrderDTO.getCheckedAmount());
            updateWrapper.eq(CardOrderPO.DB_BASE_CHECKING_AMOUNT,cardOrderDTO.getCheckingAmount());
            updateWrapper.eq(CardOrderPO.DB_COL_ID,cardOrderDTO.getId());
            log.info( "核销单审核通过 bizNo = {} ,checkedAmount = {},checkingAmount = {}",order.getBizNo() ,checkedAmount , checkingAmount);
            this.update(updateWrapper);
        });
        verificationFlowDTOS.forEach( verOrder ->{
            CardCreditManagerManager.me().updateCheckAmount(verOrder.getCreditId(),verOrder.getCheckAmount(),null , CheckTypeEnum.CHECK.getCode());
            //添加核销单核销记录
            CardCreditManagerDTO cardCreditManagerDTO = CardCreditManagerManager.me().getById(verOrder.getCreditId());
            CardVerificationFlowPO cardVerificationFlowPO = BuildVerificationFlow(cardCreditManagerDTO,CheckTypeEnum.CHECK.getCode(),verOrder.getCheckAmount(),doneDTO.getVerificationId());
            CardVerificationFlowManager.me().save(cardVerificationFlowPO);
        } );
        return true ;
    }

    /**
     * 校验核销申请的详细信息
     * 该方法用于在创建核销任务时对请求参数进行全面校验，确保数据的完整性和一致性
     *
     * @param fxVerificationReqDTO 核销申请请求对象，包含待核销的消费订单和申请单信息
     *
     * 校验逻辑包括：
     * 1. 基础参数校验：检查请求对象中的必要字段是否为空
     * 2. 消费订单校验：验证消费订单的存在性和完整性
     * 3. 申请单校验：验证申请单参数的有效性
     * 4. 金额一致性校验：确保申请单总金额与消费订单总金额一致
     * 5. 数据补全：为请求对象补充员工ID、外汇卡ID等关键信息
     *
     * @throws FinhubException 当校验失败时抛出业务异常
     */
    private void checkVerItemInfo(FxVerificationReqDTO fxVerificationReqDTO){
        log.info("核销创建任务开始 校验 checkVerItemInfo req  = {}",JSON.toJSONString(fxVerificationReqDTO));

        // 1. 校验基础参数 - 检查消费订单列表是否为空
        if (CollectionUtils.isEmpty(fxVerificationReqDTO.getItems())) {
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getType(), "参数为空");
        }

        // 1.1 校验申请单列表参数
        if (CollectionUtils.isEmpty(fxVerificationReqDTO.getApplyItems())) {
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getType(), "参数为空");
        }

        // 1.2 校验申请单的必要字段 - 申请交易号和金额不能为空
        fxVerificationReqDTO.getApplyItems().forEach(p->{
            if (StringUtils.isBlank(p.getApplyTransNo()) || Objects.isNull(p.getAmount())){
                throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getType(), "参数为空");
            }
        });

        // 2. 校验消费订单是否存在 - 根据订单编号列表获取所有订单
        List<String> allOrders = fxVerificationReqDTO.getItems().stream()
            .map(FxVerificationReqDTO.Item::getBizNo)
            .collect(Collectors.toList());
        List<Integer> allOrderType = fxVerificationReqDTO.getItems().stream()
            .map(FxVerificationReqDTO.Item::getType)
            .collect(Collectors.toList());

        List<CardOrderDTO> cardOrderDTOS4All =  getOrderByOrderIds(allOrderType,allOrders);

        if (CollectionUtils.isEmpty(cardOrderDTOS4All) || allOrders.size() != cardOrderDTOS4All.size()) {
            // 订单不存在或数量不匹配，说明有订单未找到
            throw new FinhubException(GlobalCoreResponseCode.VERIFICATION_ORDER_NOT_FUND.getCode(), GlobalCoreResponseCode.VERIFICATION_ORDER_NOT_FUND.getMessage());
        }

        // 3. 校验消费订单是否存在 - 根据订单编号列表获取所有订单
        List<CardOrderDTO> cardOrderDTOS4Consume = cardOrderDTOS4All.stream().filter(p->TransactionTypeEnum.isConsume(p.getType())).collect(Collectors.toList());  // 只处理消费类型的交易
        List<CardOrderDTO> cardOrderDTOS4Refund = cardOrderDTOS4All.stream().filter(p->TransactionTypeEnum.isRefund(p.getType())).collect(Collectors.toList());  // 只处理消费类型的交易
        // 校验是否存在消费订单
        if (CollectionUtils.isEmpty(cardOrderDTOS4Consume)){
            log.warn("没有消费的单据，fxVerificationReqDTO={}", JsonUtils.toJson(fxVerificationReqDTO));
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        // 4. 补全请求对象的关键信息 - 从第一个订单中获取员工ID和外汇卡ID
        fxVerificationReqDTO.setEmployeeId(cardOrderDTOS4Consume.get(0).getEmployeeId());
        fxVerificationReqDTO.setFxCardId(cardOrderDTOS4Consume.get(0).getFxCardId());


        // 7. 构建消费订单业务编号与未核销金额的映射关系
        Map<String, BigDecimal> maps = cardOrderDTOS4Consume.stream()
                .collect(Collectors.toMap(CardOrderDTO::getBizNo, CardOrderDTO::getUncheckedAmount, (e, n) -> n));

        // 8. 数据处理 - 只保留消费类型的订单，并设置对应的核销金额
        List<FxVerificationReqDTO.Item> items = fxVerificationReqDTO.getItems().stream()
                .filter(p -> TransactionTypeEnum.isConsume(p.getType()))  // 再次过滤确保只处理消费订单
                .collect(Collectors.toList());

        // 为每个消费订单设置其未核销金额（从分转换为元）
        items.forEach(p->{
            p.setAmount(BigDecimalUtils.fen2yuan(maps.get(p.getBizNo())));
        });
        fxVerificationReqDTO.setItems(items);
        //1-8以上都是为了为每个消费订单设置其未核销金额（从分转换为元）


        // 9. 金额一致性校验 - 确保申请单总金额与消费订单总金额一致
        // 计算申请单总金额（转换为分进行精确计算）
        BigDecimal applyTransAmount = BigDecimalUtils.yuan2fen(
                fxVerificationReqDTO.getApplyItems().stream()
                        .map(p -> p.getAmount())
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );

        // 计算消费订单总金额（已经是分单位）
        BigDecimal billTradeAmountConsume = cardOrderDTOS4Consume.stream()
                .map(p -> p.getBillTradeAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal billTradeAmountRefund = BigDecimal.ZERO;
        if(!CollectionUtils.isEmpty(cardOrderDTOS4Refund)){
            // 计算退款订单总金额（已经是分单位）
            billTradeAmountRefund = cardOrderDTOS4Refund.stream()
                .map(p -> p.getBillTradeAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        //应该计算消费+退款的折算币种的总金额
        BigDecimal billTradeAmount = billTradeAmountConsume.subtract(billTradeAmountRefund);

        // 校验两个金额是否一致
        if (BigDecimalUtils.differentPrice(applyTransAmount, billTradeAmount)){
            log.warn("消费单和申请单的金额不一致 applyTransAmount={},billTradeAmount={}", applyTransAmount, billTradeAmount);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT,"消费单和申请单的金额不一致");
        }
    }

    private void checkBaseVerItemInfo(FxVerificationReqDTO fxVerificationReqDTO){
        log.info("核销校验任务开始 校验 checkBaseVerItemInfo req  = {}",JSON.toJSONString(fxVerificationReqDTO));
        if (CollectionUtils.isEmpty(fxVerificationReqDTO.getItems())) {
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), "参数为空");
        }
        List<String> orders = fxVerificationReqDTO.getItems().stream().filter(p->TransactionTypeEnum.isConsume(p.getType())).map(FxVerificationReqDTO.Item::getBizNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orders)){
            log.warn("没有消费的单据，fxVerificationReqDTO={}", JsonUtils.toJson(fxVerificationReqDTO));
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        List<CardOrderDTO> cardOrderDTOS =  getOrderByBizNos(orders);
        if (CollectionUtils.isEmpty(cardOrderDTOS) || orders.size() != cardOrderDTOS.size()) {
            throw new FinhubException(GlobalCoreResponseCode.VERIFICATION_ORDER_NOT_FUND.getCode(), GlobalCoreResponseCode.VERIFICATION_ORDER_NOT_FUND.getMessage());
        }
        fxVerificationReqDTO.setEmployeeId(cardOrderDTOS.get(0).getEmployeeId());
        fxVerificationReqDTO.setFxCardId(cardOrderDTOS.get(0).getFxCardId());

        /*Map<String, BigDecimal> maps = cardOrderDTOS.stream().collect(Collectors.toMap(CardOrderDTO::getBizNo,CardOrderDTO::getUncheckedAmount,(e, n) -> n));
        //只保留消费的单据，复制消费单据要核销的金额
        List<FxVerificationReqDTO.Item> items = fxVerificationReqDTO.getItems().stream().filter(p -> TransactionTypeEnum.isConsume(p.getType())).collect(Collectors.toList());
        items.forEach(p->{
            p.setAmount(BigDecimalUtils.fen2yuan(maps.get(p.getBizNo())));
        });
        fxVerificationReqDTO.setItems(items);*/
    }

    public CardOrderDTO findByBizNoAndType(String bizNo,Integer tradeType){
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_BIZ_NO, bizNo);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE,tradeType);
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_CREATE_TIME);
        return super.findOne(queryWrapper);
    }

    public FxWrongPaidRefundDTO wrongPaidRefund(String orderId,Integer orderType, BigDecimal amount){
        FxWrongPaidRefundDTO fxWrongPaidRefundDTO = new FxWrongPaidRefundDTO();
        fxWrongPaidRefundDTO.setRefundResult(false);
        CardOrderDTO cardOrderDTO = findByBizNoAndType(orderId,orderType);
        if (cardOrderDTO == null){
            fxWrongPaidRefundDTO.setFailReason("未查询到此消费");
            return fxWrongPaidRefundDTO;
        }
        //未核销金额
        BigDecimal uncheckedAmount = cardOrderDTO.getUncheckedAmount();
        if (BigDecimalUtils.hasNoPrice(uncheckedAmount) ){
            fxWrongPaidRefundDTO.setFailReason("无待核销金额");
            return fxWrongPaidRefundDTO;
        }
        if (uncheckedAmount.compareTo(amount)<0){
            fxWrongPaidRefundDTO.setFailReason("错花还款金额大于待核销金额");
            return fxWrongPaidRefundDTO;
        }
        BigDecimal needNotCheckAmountAfterUpdate = cardOrderDTO.getNeedNotCheckAmount().add(amount);
        BigDecimal uncheckedAmountAfterUpdate = cardOrderDTO.getUncheckedAmount().subtract(amount);
        Boolean updateResult = updateForWrongPaidRefund(cardOrderDTO.getId(),uncheckedAmountAfterUpdate,needNotCheckAmountAfterUpdate);
        CardCreditManagerManager.me().reduceUncheckedAmount(cardOrderDTO.getFxCardId(),amount);
        if (updateResult){
            CardOrderAddReqDTO cardOrderAddReqDTO = new CardOrderAddReqDTO();
            cardOrderAddReqDTO.setFxCardId(cardOrderDTO.getFxCardId());
            cardOrderAddReqDTO.setTradeAddress(cardOrderDTO.getTradeAddress());
            cardOrderAddReqDTO.setTradeAmount(amount.abs());
            cardOrderAddReqDTO.setTradeCurrency(CurrencyEnum.USD.getCurrencyCode());
            cardOrderAddReqDTO.setTradeName(cardOrderDTO.getTradeName());
            cardOrderAddReqDTO.setTradeId(cardOrderDTO.getTradeId());
            cardOrderAddReqDTO.setSubTradeId(cardOrderDTO.getSubTradeId());
            cardOrderAddReqDTO.setTradeTime(new Date());
            cardOrderAddReqDTO.setTradeTimeZone("TODO");
            cardOrderAddReqDTO.setType(TransactionTypeEnum.WRONG_PAID.getKey());
            cardOrderAddReqDTO.setEmployeeId(cardOrderDTO.getEmployeeId());
            cardOrderAddReqDTO.setCompanyId(cardOrderDTO.getCompanyId());
            cardOrderAddReqDTO.setCreateTime(new Date());
            cardOrderAddReqDTO.setBillTradeAmount(amount.abs());
            cardOrderAddReqDTO.setBillTradeCurrency(CurrencyEnum.USD.getCurrencyCode());
            cardOrderAddReqDTO.setMaskedCardNumber(cardOrderDTO.getMaskedCardNumber());
            cardOrderAddReqDTO.setOriBizNo(cardOrderDTO.getBizNo());
            cardOrderAddReqDTO.setCheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setCheckingAmount(BigDecimal.ZERO);
            cardOrderAddReqDTO.setNeedNotCheckAmount(needNotCheckAmountAfterUpdate);
            cardOrderAddReqDTO.setCheckStatus(CheckStatusEnum.NONE.getKey());
            cardOrderAddReqDTO.setCardPlatform(CardPlatformEnum.AIRWALLEX.getCode());
            //交易币种兑人民币汇率计算，以及人民币金额计算
            cardOrderAddReqDTO.setTradeCnyExchangeRate(cardOrderDTO.getTradeCnyExchangeRate());
            cardOrderAddReqDTO.setCnyTradeAmount(cardOrderDTO.getCnyTradeAmount());
            String bizNo = BizIdUtils.getWrongPaidId();
            cardOrderAddReqDTO.setBizNo(bizNo);
            add(cardOrderAddReqDTO);
            TradeRefundRpcDTO tradeRefundRpcDTO = new TradeRefundRpcDTO();
            tradeRefundRpcDTO.setOrderId(cardOrderDTO.getBizNo());
            tradeRefundRpcDTO.setRefundOrderId(bizNo);
            tradeRefundRpcDTO.setCompanyId(cardOrderDTO.getCompanyId());
            tradeRefundRpcDTO.setType(TransactionTypeEnum.WRONG_PAID.getKey());
            iVirtualCardBudgetRpcService.fxTradeRefundAmount(tradeRefundRpcDTO);
            fxWrongPaidRefundDTO.setRefundResult(true);

        }else {
            fxWrongPaidRefundDTO.setFailReason("核销失败,稍后再试");
        }
        return fxWrongPaidRefundDTO;
    }

    public List<CardOrderDTO> findRefundByOriBizNos(List<String> oriBizNos){
        if (CollectionUtils.isEmpty(oriBizNos)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(CardOrderPO.DB_COL_ORI_BIZ_NO, oriBizNos);
        return super.findList(queryWrapper);
    }

    public CardOrderDTO findByBizNoAndCompanyId(String bizNo,String companyId, Integer type){
        if (StringUtils.isAnyBlank(bizNo, companyId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_BIZ_NO, bizNo);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE, type);
        queryWrapper.eq(CardOrderPO.DB_COL_COMPANY_ID, companyId);
        return super.findOne(queryWrapper);
    }

    public Integer getUnbindCostRecordCount(String employeeId,String companyId){
        if (StringUtils.isAnyBlank(employeeId, companyId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE, TransactionTypeEnum.CONSUME.getKey());
        queryWrapper.eq(CardOrderPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.eq(CardOrderPO.DB_COL_CHECK_STATUS, CheckStatusEnum.UNCHECK.getKey());
        queryWrapper.gt(CardOrderPO.DB_BASE_UNCHECKED_AMOUNT, BigDecimal.ZERO);
        queryWrapper.eq(CardOrderPO.DB_BASE_APPLY_BIND, ApplyBindStatus.NO.getKey());
        return super.findCount(queryWrapper);
    }

    public Integer getNotCheckedCount(String companyId){
        if (StringUtils.isAnyBlank(companyId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_TYPE, TransactionTypeEnum.CONSUME.getKey());
        queryWrapper.eq(CardOrderPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.in(CardOrderPO.DB_COL_CHECK_STATUS, Arrays.asList(CheckStatusEnum.UNCHECK.getKey(), CheckStatusEnum.CHECKING.getKey()));
        return super.findCount(queryWrapper);
    }

    public void updateExchangeRate(String id, BigDecimal rate){
        if (StringUtils.isAnyBlank(id) || Objects.isNull(rate)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        CardOrderShowResDTO showResDTO = this.show(id);
        if (Objects.isNull(showResDTO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
        CardOrderModifyReqDTO reqDTO = new CardOrderModifyReqDTO();
        reqDTO.setId(Long.parseLong(id));
        reqDTO.setTradeCnyExchangeRate(rate);
        reqDTO.setCnyTradeAmount(rate.multiply(showResDTO.getTradeAmount(), MathContext.UNLIMITED));
        this.modify(reqDTO);
    }

    public void updateOrderShow(String id){
        if (StringUtils.isAnyBlank(id)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        CardOrderShowResDTO showResDTO = this.show(id);
        if (Objects.isNull(showResDTO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
        CardOrderModifyReqDTO reqDTO = new CardOrderModifyReqDTO();
        reqDTO.setId(Long.parseLong(id));
        reqDTO.setOrderShow(0);
        this.modify(reqDTO);
    }

    public void updateExchangeRate2025(String id){
        CardOrderShowResDTO showResDTO = this.show(id);
        if (Objects.isNull(showResDTO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
        //应该按照日期获取对应的汇率
        BigDecimal currencyRate = queryCurrencyExchangeRate(showResDTO.getCreateTime());
        BigDecimal tradeRate = tradeCnyExchangeRateCal(showResDTO.getBillTradeAmount(), showResDTO.getTradeAmount(),currencyRate);

        CardOrderModifyReqDTO reqDTO = new CardOrderModifyReqDTO();
        reqDTO.setId(Long.parseLong(id));
        reqDTO.setTradeCnyExchangeRate(tradeRate);
        BigDecimal amount = getCnyTradeAmount(currencyRate, showResDTO.getTradeAmount());
        reqDTO.setCnyTradeAmount(amount);
        reqDTO.setBillTradeCnyExchangeRate(currencyRate);
        this.modify(reqDTO);
    }
    private BigDecimal getCnyTradeAmount(BigDecimal rate, BigDecimal tradeAmount) {
        return rate.multiply(tradeAmount, MathContext.UNLIMITED);
    }
    private BigDecimal tradeCnyExchangeRateCal(BigDecimal billTradeAmount, BigDecimal tradeAmount, BigDecimal usdToCnyRate) {
        BigDecimal divide = billTradeAmount.divide(tradeAmount, 8, RoundingMode.HALF_UP);
        return usdToCnyRate.multiply(divide, MathContext.UNLIMITED);
    }
    private BigDecimal queryCurrencyExchangeRate(Date date) {
        try {
            ExchangeRateReq rateReq = new ExchangeRateReq();
            rateReq.setExchangeType(ExchangeType.USD_CNY);
            rateReq.setQueryDate(date);
            log.info("美元兑换人民币汇率获取参数 rateReq={}", JsonUtils.toJson(rateReq));
            ResponseVo<ExchangeRateRes> usdToCnyRate = iExchangeRateService.queryExchangeRate(rateReq);
            log.info("美元兑换人民币汇率获取结果usdToCnyRate={}", JsonUtils.toJson(usdToCnyRate));
            if (Objects.isNull(usdToCnyRate)) {
                log.error("美元兑换人名币汇率获取为空");
                return null;
            }
            boolean success = usdToCnyRate.isSuccess();
            if (!success) {
                log.error("美元兑换人名币汇率获取为空失败");
                return null;
            }
            ExchangeRateRes data = usdToCnyRate.getData();
            return data.getRate();
        } catch (Exception e) {
            log.error("消费交易币种兑换人民币计算报错", e);
            return null;
        }
    }
}
