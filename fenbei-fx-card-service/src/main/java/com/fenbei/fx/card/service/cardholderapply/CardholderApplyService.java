package com.fenbei.fx.card.service.cardholderapply;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardholder.dto.CardholderDetailResDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.*;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import java.util.List;

/**
 * 持卡人操作申请 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
public interface CardholderApplyService extends BaseService<CardholderApplyDTO> {

    static CardholderApplyService me() {
        return SpringUtil.getBean(CardholderApplyService.class);
    }


    /**
     * 持卡人创建申请
     *
     * @param reqDTO
     * @return
     */
    CardholderDetailResDTO applyCreate(CardholderApplyAddReqDTO reqDTO);

    /**
     * 持卡人修改申请
     *
     * @param reqDTO
     * @return
     */
    CardholderDetailResDTO applyModify(CardholderApplyAddReqDTO reqDTO);

    Boolean approve(String id, Integer status, String reason);

    /**
     * 处理银行处理中的数据
     */
    void handleBankDealingData();

    /**
     * 根据申请单id删除
     * @param applyId
     * @return
     */
    Boolean removeByApplyId(String applyId);

}
