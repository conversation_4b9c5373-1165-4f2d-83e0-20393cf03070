package com.fenbei.fx.card.service.cardapply.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.service.cardapply.dto.*;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.util.*;
import com.fenbeitong.dech.api.model.dto.airwallex.*;
import com.fenbeitong.dech.api.model.dto.lianlian.card.req.*;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.*;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaPushMsg;
import com.fenbeitong.finhub.kafka.producer.impl.KafkaProducerPublisher;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * 开卡成功发送消息
 */
@Slf4j
@Component
public class CardApplySuccessManager {

    @Autowired
    public MessageService messageService;

    @Autowired
    private KafkaProducerPublisher kafkaProducerPublisher;

    public void sendMsg4CreateCard(String phone, String tempId , String message) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            phones.add(phone);
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", message);
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("海外卡额度发放失败,短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("海外卡额度发放失败，短信发送失败！！！");
            e2.printStackTrace();
        }
    }
    public void sendMail4CreateCard(Set<String> emailSet , String msg) {
        try {
            EmailContract emailContract = new EmailContract();
            Set<String> checkList = new HashSet<>();
            emailSet.forEach(email ->{
                if (EmailCheckUtils.emailFormat(email)){
                    checkList.add(email);
                }
            });
            if (com.luastar.swift.base.utils.CollectionUtils.isEmpty(checkList)){
                return ;
            }
            FinhubLogger.info("sendMail4CreateCard req = {},msg = {}", emailSet , msg);
            // 收件人
            emailContract.setToList(checkList);
            // 邮件标题
            String subject = "【分贝通】企业海外卡开通成功";
            emailContract.setSubject(subject);
            String text = "您好!" + "\n" + "感谢您使用分贝通的服务"+"\n"+ msg;
            emailContract.setText(text);
            // 发送邮件
            messageService.sendEmail(emailContract);
        }catch (Exception e) {
            FinhubLogger.error("发送邮件失败 email = {},msg = {}",emailSet,msg , e);
        }
    }

    public void pushAlert4CreateCard(String companyId , String employeeId , String bizNo, String msg, String title, String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", ApplyType.BankIndividual.getValue());
            msgInfo.put("order_type", String.valueOf(BizType.FxCardCreditApply.getCode()));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(520+"");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("海外卡开卡成功push消息参数kafkaPushMsg:{}",kafkaPushMsg);
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡开卡成功push消息参数msg失败:{}", msg,e);
        }
    }
}
