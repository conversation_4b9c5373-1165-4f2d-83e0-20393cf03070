package com.fenbei.fx.card.service.cardholderapply.manager;

import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyDTO;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.util.EmailCheckUtils;
import com.fenbei.fx.card.util.EmailContract;
import com.fenbeitong.finhub.common.constant.ApplyType;
import com.fenbeitong.finhub.common.constant.BizType;
import com.fenbeitong.finhub.common.constant.MessageType;
import com.fenbeitong.finhub.common.constant.SenderType;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaPushMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.fenbeitong.finhub.kafka.producer.impl.KafkaProducerPublisher;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeCompanyDto;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 持卡人操作申请 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Component
public class CardholderApplyNoticeManager{


    @Autowired
    private KafkaProducerPublisher kafkaProducerPublisher;

    @DubboReference
    private IBaseEmployeeExtService baseEmployeeExtService;

    @Autowired
    public MessageService messageService;

    /**
     * 发送管理员拒绝通知
     */
    public void sendAdminRefuseNotice(CardholderApplyDTO applyDTO, String reason) {
        try {
            log.info("发送管理员拒绝通知，applyId={}, reason={}", applyDTO.getApplyId(), reason);

            // 构建通知消息
            String message = String.format("您提交的海外卡持卡人申请已被管理员驳回",
                    StringUtils.isNotBlank(reason) ? reason : "");

            String title = "海外卡-海外卡持卡人申请被驳回";
            // 发送站内信通知
            sendWebPushNotice(applyDTO.getCompanyId(), applyDTO.getEmployeeId(),
                title, message);

            // 发送消息中心通知
            sendMsgCenterNotice(applyDTO.getCompanyId(), applyDTO.getEmployeeId(),
                    applyDTO.getApplyId(), title, message);

//            List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(Lists.newArrayList(applyDTO.getCreateUserId()), applyDTO.getCompanyId());
//            EmployeeCompanyDto employeeOrgUnitDTO = employeeCompanyDtos.get(0);
//            String subject = "【分贝通】海外卡-海外卡持卡人申请被驳回";
//            sendMail4CardHolderRefused(Collections.singleton(employeeOrgUnitDTO.getUserEmail()),subject, message);
//
//
//            pushAlert4CardHolderRefused(applyDTO.getCompanyId(), applyDTO.getEmployeeId(),
//                    applyDTO.getApplyId(), title, message, "系统通知");

        } catch (Exception e) {
            log.error("发送管理员拒绝通知异常，applyId={}", applyDTO.getApplyId(), e);
        }
    }





    /**
     * 发送Airwallex拒绝通知
     */
    public void sendAirwallexRefuseNotice(CardholderApplyDTO applyDTO) {
        try {
            log.info("发送Airwallex拒绝通知，applyId={}", applyDTO.getApplyId());

            // 构建通知消息
            String message = "您的持卡人申请已被Airwallex拒绝，请检查提交的信息是否准确完整";
            String title = "海外卡-持卡人申请被Airwallex拒绝";
            // 发送站内信通知
            sendWebPushNotice(applyDTO.getCompanyId(), applyDTO.getEmployeeId(),
                title, message);

            // 发送消息中心通知
            sendMsgCenterNotice(applyDTO.getCompanyId(), applyDTO.getEmployeeId(),
                    applyDTO.getApplyId(), title, message);


//            List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(Lists.newArrayList(applyDTO.getCreateUserId()), applyDTO.getCompanyId());
//            EmployeeCompanyDto employeeOrgUnitDTO = employeeCompanyDtos.get(0);
//            String subject = "【分贝通】持卡人申请被Airwallex拒绝";
//            sendMail4CardHolderRefused(Collections.singleton(employeeOrgUnitDTO.getUserEmail()),subject,message);
//
//
//            pushAlert4CardHolderRefused(applyDTO.getCompanyId(), applyDTO.getEmployeeId(),
//                applyDTO.getApplyId(), title, message, "系统通知");

        } catch (Exception e) {
            log.error("发送Airwallex拒绝通知异常，applyId={}", applyDTO.getApplyId(), e);
        }
    }


    public void sendMail4CardHolderRefused(Set<String> emailSet , String subject,String msg) {
        try {
            EmailContract emailContract = new EmailContract();
            Set<String> checkList = new HashSet<>();
            emailSet.forEach(email ->{
                if (EmailCheckUtils.emailFormat(email)){
                    checkList.add(email);
                }
            });
            if (com.luastar.swift.base.utils.CollectionUtils.isEmpty(checkList)){
                return ;
            }
            FinhubLogger.info("sendMail4CardHolderRefused req = {},msg = {}", emailSet , msg);
            // 收件人
            emailContract.setToList(checkList);
            // 邮件标题
            emailContract.setSubject(subject);

            StringBuffer textBufer= new StringBuffer();
            textBufer.append("您好！" + "\n");
            textBufer.append(msg+ "\n");
            textBufer.append("\n");
            textBufer.append("顺祝商祺！"+ "\n");
            textBufer.append("\n");
            textBufer.append("企业支出，就用分贝通！"+ "\n");
            textBufer.append("客服电话：400-817-6868"+ "\n");
            textBufer.append("公司官网：www.fenbeitong.com"+"\n");
            String text = textBufer.toString();
            emailContract.setText(text);
            // 发送邮件
            messageService.sendEmail(emailContract);
        }catch (Exception e) {
            FinhubLogger.error("发送邮件失败 email = {},msg = {}",emailSet,msg , e);
        }
    }


    public void pushAlert4CardHolderRefused(String companyId , String employeeId , String bizNo, String msg, String title, String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", ApplyType.BankIndividual.getValue());
            msgInfo.put("order_type", String.valueOf(BizType.FxCardCreditApply.getCode()));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(520+"");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("海外卡持卡人申请结果push消息参数kafkaPushMsg:{}",kafkaPushMsg);
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡持卡人申请结果push消息参数msg失败:{}", msg,e);
        }
    }


    /**
     * 发送站内信通知
     */
    private void sendWebPushNotice(String companyId, String employeeId, String title, String message) {
        try {
            sendWebPush4CardHolderApply(companyId, employeeId, message, title);
        } catch (Exception e) {
            log.error("发送站内信通知失败，companyId={}, employeeId={}", companyId, employeeId, e);
        }
    }

    /**
     * 发送消息中心通知
     */
    private void sendMsgCenterNotice(String companyId, String employeeId, String applyId, String title, String message) {
        try {
            sendMsgCenter4CardHolderApply(companyId, employeeId, applyId, message, title);
        } catch (Exception e) {
            log.error("发送消息中心通知失败，companyId={}, employeeId={}", companyId, employeeId, e);
        }
    }


    /**
     * 发送持卡人申请结果Web推送
     */
    public void sendWebPush4CardHolderApply(String companyId, String employeeId, String msg, String title) {
        try {
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setMsgSubType(BizType.Nothing.getCode());
            kafkaWebMessageMsg.setBizType(BizType.Nothing.getCode());
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setTitle(title);
            kafkaWebMessageMsg.setCompanyId(companyId);
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setReceiver(employeeId);
            kafkaWebMessageMsg.setSender(employeeId);
            FinhubLogger.info("海外卡持卡人申请结果通知推送Web消息 参数={}", JsonUtils.toJson(kafkaWebMessageMsg));
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡持卡人申请结果通知Web推送发送失败！！！",e);
        }
    }


    /**
     * 发送持卡人申请结果消息中心通知
     */
    public void sendMsgCenter4CardHolderApply(String companyId, String employeeId, String bizNo, String msg, String title) {
        try {
            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(BizType.Nothing.getCode());
            kafkaSaasMessageMsg.setBizOrder(bizNo);
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(companyId);
            kafkaSaasMessageMsg.setReceiver(employeeId);
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(employeeId);
            kafkaSaasMessageMsg.setTitle(title);
            FinhubLogger.info("海外卡持卡人申请结果通知推送 MsgCenter 参数={}", JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡持卡人申请结果通知消息中心发送失败！！！",e);
        }
    }

}
