package com.fenbei.fx.card.service.cardapply.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyShowResDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyDTO;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.util.EmailContract;
import com.fenbei.fx.card.util.SmsContract;
import com.fenbei.fx.card.util.EmailCheckUtils;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaPushMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.fenbeitong.finhub.kafka.producer.impl.KafkaProducerPublisher;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saas.api.service.message.setting.IMessageSettingService;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.luastar.swift.base.json.JsonUtils;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeCompanyDto;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupReceiverVO;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 海外卡开卡申请消息通知管理器
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class CardApplyNoticeManager {

    @Autowired
    public MessageService messageService;

    @Autowired
    private KafkaProducerPublisher kafkaProducerPublisher;

    @DubboReference
    private IBaseEmployeeExtService baseEmployeeExtService;
    @DubboReference
    private IMessageSettingService iMessageSettingService;

    /**
     * 海外卡开卡申请提醒消息配置key
     */
    public static String OVERSEA_CARD_OPEN_REMIND = "oversea_card_open_remind";

    public static String CARD_OPEN_MSG_TEMP_ID = "67569ddf59aac212705316a4";

    /**
     * 发送海外卡持卡人申请消息通知
     *
     * @param cardholderApplyDTO 持卡人申请ID
     */
    public void sendNoticeMsgForCardholderApply( CardholderApplyDTO cardholderApplyDTO) {
        try {
            if (cardholderApplyDTO == null) {
                FinhubLogger.warn("海外卡持卡人申请消息通知 - 未找到申请记录，cardholderApplyDTO = {}", JsonUtils.toJson(cardholderApplyDTO));
                return;
            }

            String title = "海外卡-海外卡持卡人申请通知";
            //查询配置
            FinhubLogger.info("海外卡持卡人申请通知 查询消息配置信息 companyId = {}", cardholderApplyDTO.getCompanyId());
            List<MessageSetupVO> messageSetupVOS = iMessageSettingService.queryCompanyMessageSetupWithDefault(
                cardholderApplyDTO.getCompanyId(), Collections.singletonList(OVERSEA_CARD_OPEN_REMIND));
            FinhubLogger.info("海外卡持卡人申请通知 查询消息配置信息 companyId = {}，res = {}",
                cardholderApplyDTO.getCompanyId(), JSON.toJSONString(messageSetupVOS));

            if (messageSetupVOS != null && messageSetupVOS.size() > 0) {
                MessageSetupVO messageSetupVO = messageSetupVOS.get(0);
                if (messageSetupVO.getIsChecked() > 0) {
                    sendCompanyNoticeForCardholderApply(messageSetupVO, cardholderApplyDTO, title);
                }
            }
        } catch (Exception e) {
            FinhubLogger.error("海外卡持卡人申请消息通知发送失败，未找到申请记录，cardholderApplyDTO = {}", JsonUtils.toJson(cardholderApplyDTO), e);
        }
    }

    /**
     * 发送海外卡开卡申请消息通知
     *
     * @param showResDTO 开卡申请ID
     */
    public void sendNoticeMsgForCardApply(CardApplyShowResDTO showResDTO) {
        try {
            if (showResDTO == null) {
                FinhubLogger.warn("海外卡开卡申请消息通知 - 未找到申请记录，showResDTO = {}", JsonUtils.toJson(showResDTO));
                return;
            }

            String title = "海外卡-海外卡开卡申请通知";
            //查询配置
            FinhubLogger.info("海外卡开卡申请通知 查询消息配置信息 companyId = {}", showResDTO.getCompanyId());
            List<MessageSetupVO> messageSetupVOS = iMessageSettingService.queryCompanyMessageSetupWithDefault(
                showResDTO.getCompanyId(), Collections.singletonList(OVERSEA_CARD_OPEN_REMIND));
            FinhubLogger.info("海外卡开卡申请通知 查询消息配置信息 companyId = {}，res = {}",
                showResDTO.getCompanyId(), JSON.toJSONString(messageSetupVOS));

            if (messageSetupVOS != null && messageSetupVOS.size() > 0) {
                MessageSetupVO messageSetupVO = messageSetupVOS.get(0);
                if (messageSetupVO.getIsChecked() > 0) {
                    sendCompanyNoticeForCardApply(messageSetupVO, showResDTO, title);
                }
            }
        } catch (Exception e) {
            FinhubLogger.error("海外卡开卡申请消息通知发送失败，showResDTO = {}", JsonUtils.toJson(showResDTO), e);
        }
    }

    /**
     * 发送持卡人申请企业通知
     */
    private void sendCompanyNoticeForCardholderApply(MessageSetupVO messageSetupVO, CardholderApplyDTO cardholderApplyDTO, String title) {
        boolean appWebFlag = (1 == messageSetupVO.getIntVal1());
        boolean emailFlag = (1 == messageSetupVO.getIntVal2());
        boolean smsFlag = (1 == messageSetupVO.getIntVal3());

        if (!appWebFlag && !emailFlag && !smsFlag) {
            // 无可发送渠道
            FinhubLogger.info("海外卡持卡人申请通知 无可发送渠道不发提醒 companyId = {}，res = {}",
                cardholderApplyDTO.getCompanyId(), JSON.toJSONString(messageSetupVO));
            return;
        }

        List<MessageSetupReceiverVO> messageSetupReceiverVOS = iMessageSettingService.queryMessageReceiverList(
            cardholderApplyDTO.getCompanyId(), OVERSEA_CARD_OPEN_REMIND);

        if (CollectionUtils.isEmpty(messageSetupReceiverVOS)) {
            return;
        }

        List<String> userIdList = messageSetupReceiverVOS.stream()
            .map(MessageSetupReceiverVO::getUserId)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }

        List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(
            Lists.newArrayList(cardholderApplyDTO.getEmployeeId()), cardholderApplyDTO.getCompanyId());
        EmployeeCompanyDto companyEmployeeHolder = employeeCompanyDtos.get(0);

        String message = convertMsgForCardholderApply(companyEmployeeHolder);

        if (appWebFlag) {//APP通知
            userIdList.forEach(userId -> {
                try {
                    FinhubLogger.info("海外卡持卡人申请通知发送站内信给配置人员  入参 req = {},msg = {}", userId, message);
                    sendWebPush4CardholderApply(cardholderApplyDTO.getCompanyId(), userId, message, title);
                    sendMsgCenter4CardholderApply(cardholderApplyDTO.getCompanyId(), userId, cardholderApplyDTO.getApplyId(), message, title);
                    pushAlert4CardholderApply(cardholderApplyDTO.getCompanyId(), userId, cardholderApplyDTO.getApplyId(), message, title, null);
                } catch (Exception e) {
                    FinhubLogger.info("海外卡持卡人申请通知发送站内通知给配置人员失败 userId = {} , message = {}", userId, message, e);
                }
            });
        }

        List<EmployeeCompanyDto> employeeOrgUnitDTOSOther = baseEmployeeExtService.queryEmployeeCompanyInfoListById(userIdList, cardholderApplyDTO.getCompanyId());

        if (emailFlag) {//邮件通知
            Set<String> mailSet = employeeOrgUnitDTOSOther.stream()
                .map(EmployeeCompanyDto::getUserEmail)
                .collect(Collectors.toSet());
            FinhubLogger.info("海外卡持卡人申请通知发送邮件给配置人员  入参 req = {},msg = {}", mailSet, message);
            sendMail4CardholderApply(mailSet, message);
        }

        if (smsFlag) {//短信通知
            employeeOrgUnitDTOSOther.forEach(employeeCompanyDto -> {
                String phone = employeeCompanyDto.getUserPhone();
                if (StringUtils.isNotBlank(phone)) {
                    FinhubLogger.info("海外卡持卡人申请通知发送短信给配置人员  入参 req = {},msg = {}", phone, message);
                    sendMsg4CardholderApply(phone, CARD_OPEN_MSG_TEMP_ID, message);
                }
            });
        }
    }

    /**
     * 发送开卡申请企业通知
     */
    private void sendCompanyNoticeForCardApply(MessageSetupVO messageSetupVO, CardApplyShowResDTO cardApplyDTO, String title) {
        boolean appWebFlag = (1 == messageSetupVO.getIntVal1());
        boolean emailFlag = (1 == messageSetupVO.getIntVal2());
        boolean smsFlag = (1 == messageSetupVO.getIntVal3());

        if (!appWebFlag && !emailFlag && !smsFlag) {
            // 无可发送渠道
            FinhubLogger.info("海外卡开卡申请通知 无可发送渠道不发提醒 companyId = {}，res = {}",
                cardApplyDTO.getCompanyId(), JSON.toJSONString(messageSetupVO));
            return;
        }

        List<MessageSetupReceiverVO> messageSetupReceiverVOS = iMessageSettingService.queryMessageReceiverList(
            cardApplyDTO.getCompanyId(), OVERSEA_CARD_OPEN_REMIND);

        if (CollectionUtils.isEmpty(messageSetupReceiverVOS)) {
            return;
        }

        List<String> userIdList = messageSetupReceiverVOS.stream()
            .map(MessageSetupReceiverVO::getUserId)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }

        List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(
            Lists.newArrayList(cardApplyDTO.getCreateUserId()), cardApplyDTO.getCompanyId());
        EmployeeCompanyDto companyEmployeeHolder = employeeCompanyDtos.get(0);

        String message = convertMsgForCardApply(companyEmployeeHolder);

        if (appWebFlag) {//APP通知
            userIdList.forEach(userId -> {
                try {
                    FinhubLogger.info("海外卡开卡申请通知发送站内信给配置人员  入参 req = {},msg = {}", userId, message);
                    sendWebPush4CardApply(cardApplyDTO.getCompanyId(), userId, message, title);
                    sendMsgCenter4CardApply(cardApplyDTO.getCompanyId(), userId, cardApplyDTO.getApplyId(), message, title);
                    pushAlert4CardApply(cardApplyDTO.getCompanyId(), userId, cardApplyDTO.getApplyId(), message, title, null);
                } catch (Exception e) {
                    FinhubLogger.info("海外卡开卡申请通知发送站内通知给配置人员失败 userId = {} , message = {}", userId, message, e);
                }
            });
        }

        List<EmployeeCompanyDto> employeeOrgUnitDTOSOther = baseEmployeeExtService.queryEmployeeCompanyInfoListById(userIdList, cardApplyDTO.getCompanyId());

        if (emailFlag) {//邮件通知
            Set<String> mailSet = employeeOrgUnitDTOSOther.stream()
                .map(EmployeeCompanyDto::getUserEmail)
                .collect(Collectors.toSet());
            FinhubLogger.info("海外卡开卡申请通知发送邮件给配置人员  入参 req = {},msg = {}", mailSet, message);
            sendMail4CardApply(mailSet, message);
        }

        if (smsFlag) {//短信通知
            employeeOrgUnitDTOSOther.forEach(employeeCompanyDto -> {
                String phone = employeeCompanyDto.getUserPhone();
                if (StringUtils.isNotBlank(phone)) {
                    FinhubLogger.info("海外卡开卡申请通知发送短信给配置人员  入参 req = {},msg = {}", phone, message);
                    sendMsg4CardApply(phone, CARD_OPEN_MSG_TEMP_ID, message);
                }
            });
        }
    }

    /**
     * 构建持卡人申请消息内容
     */
    private String convertMsgForCardholderApply(EmployeeCompanyDto employeeCompanyDto) {
        String message = String.format("员工%s刚提交了海外卡创建持卡人申请，请至“web管理端-支付管理-海外卡管理-持卡人管理”确认。",
            employeeCompanyDto.getUserName());
        return message;
    }

    /**
     * 构建开卡申请消息内容
     */
    private String convertMsgForCardApply(EmployeeCompanyDto employeeCompanyDto) {
        String message = String.format("员工%s刚提交了海外卡开卡申请，请至“web管理端-支付管理-海外卡管理-卡片管理”确认。",
            employeeCompanyDto.getUserName());
        return message;
    }

    /**
     * 发送持卡人申请Web推送
     */
    private void sendWebPush4CardholderApply(String companyId, String employeeId, String msg, String title) {
        try {
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgSubType(BizType.Nothing.getCode());
            kafkaWebMessageMsg.setBizType(BizType.Nothing.getCode());
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setTitle(title);
            kafkaWebMessageMsg.setCompanyId(companyId);
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setReceiver(employeeId);
            kafkaWebMessageMsg.setSender(employeeId);
            FinhubLogger.info("海外卡持卡人申请通知推送Web消息 参数={}", JsonUtils.toJson(kafkaWebMessageMsg));
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡持卡人申请通知Web推送发送失败！！！",e);
        }
    }

    /**
     * 发送开卡申请Web推送
     */
    public void sendWebPush4CardApply(String companyId, String employeeId, String msg, String title) {
        try {
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgSubType(BizType.Nothing.getCode());
            kafkaWebMessageMsg.setBizType(BizType.Nothing.getCode());
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setTitle(title);
            kafkaWebMessageMsg.setCompanyId(companyId);
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setReceiver(employeeId);
            kafkaWebMessageMsg.setSender(employeeId);
            FinhubLogger.info("海外卡开卡申请通知推送Web消息 参数={}", JsonUtils.toJson(kafkaWebMessageMsg));
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡开卡申请通知Web推送发送失败！！！",e);
        }
    }

    /**
     * 发送持卡人申请消息中心通知
     */
    public void sendMsgCenter4CardholderApply(String companyId, String employeeId, String bizNo, String msg, String title) {
        try {
            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(BizType.Nothing.getCode());
            kafkaSaasMessageMsg.setBizOrder(bizNo);
            kafkaSaasMessageMsg.setBizType(86);
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(companyId);
            kafkaSaasMessageMsg.setReceiver(employeeId);
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(employeeId);
            kafkaSaasMessageMsg.setTitle(title);
            FinhubLogger.info("海外卡持卡人申请通知推送 MsgCenter 参数={}", JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡持卡人申请通知消息中心发送失败！！！",e);
        }
    }

    /**
     * 发送开卡申请消息中心通知
     */
    public void sendMsgCenter4CardApply(String companyId, String employeeId, String bizNo, String msg, String title) {
        try {
            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(BizType.Nothing.getCode());
            kafkaSaasMessageMsg.setBizOrder(bizNo);
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(companyId);
            kafkaSaasMessageMsg.setReceiver(employeeId);
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(employeeId);
            kafkaSaasMessageMsg.setTitle(title);
            FinhubLogger.info("海外卡开卡申请通知推送 MsgCenter 参数={}", JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡开卡申请通知消息中心发送失败！！！",e);
        }
    }

    /**
     * 发送持卡人申请推送警报
     */
    private void pushAlert4CardholderApply(String companyId, String employeeId, String bizNo, String msg, String title, String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", "1");
            msgInfo.put("order_type", String.valueOf(BizType.Nothing.getCode()));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(0 + "");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("海外卡持卡人申请通知推送Alert 参数={}", JsonUtils.toJson(kafkaPushMsg));
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡持卡人申请通知推送Alert发送失败！！！",e);
        }
    }

    /**
     * 发送开卡申请推送警报
     */
    private void pushAlert4CardApply(String companyId, String employeeId, String bizNo, String msg, String title, String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", "1");
            msgInfo.put("order_type", String.valueOf(BizType.Nothing.getCode()));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(0 + "");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("海外卡开卡申请通知推送Alert 参数={}", JsonUtils.toJson(kafkaPushMsg));
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡开卡申请通知推送Alert发送失败！！！",e);
        }
    }

    /**
     * 发送持卡人申请短信
     */
    private void sendMsg4CardholderApply(String phone, String tempId, String message) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            phones.add(phone);
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", message);
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("海外卡持卡人申请通知,短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("海外卡持卡人申请通知，短信发送失败！！！",e2);
        }
    }

    /**
     * 发送开卡申请短信
     */
    private void sendMsg4CardApply(String phone, String tempId, String message) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            phones.add(phone);
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", message);
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("海外卡开卡申请通知,短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("海外卡开卡申请通知，短信发送失败！！！",e2);
        }
    }

    /**
     * 发送持卡人申请邮件
     */
    public void sendMail4CardholderApply(Set<String> emailSet, String msg) {
        try {
            EmailContract emailContract = new EmailContract();
            Set<String> checkList = new HashSet<>();
            emailSet.forEach(email -> {
                if (EmailCheckUtils.emailFormat(email)) {
                    checkList.add(email);
                }
            });
            if (CollectionUtils.isEmpty(checkList)) {
                return;
            }
            FinhubLogger.info("sendMail4CardholderApply req = {},msg = {}", emailSet, msg);
            // 收件人
            emailContract.setToList(checkList);
            // 邮件标题
            String subject = "【分贝通】海外卡持卡人申请通知";
            emailContract.setSubject(subject);
            StringBuffer textBufer= new StringBuffer();
            textBufer.append("您好！" + "\n");
            textBufer.append(msg+ "\n");
            textBufer.append("\n");
            textBufer.append("顺祝商祺！"+ "\n");
            textBufer.append("\n");
            textBufer.append("企业支出，就用分贝通！"+ "\n");
            textBufer.append("客服电话：400-817-6868"+ "\n");
            textBufer.append("公司官网：www.fenbeitong.com"+"\n");
            String text = textBufer.toString();
            emailContract.setText(text);
            // 发送邮件
            messageService.sendEmail(emailContract);
        } catch (Exception e) {
            FinhubLogger.error("发送持卡人申请邮件失败 email = {},msg = {}", emailSet, msg, e);
        }
    }

    /**
     * 发送开卡申请邮件
     */
    public void sendMail4CardApply(Set<String> emailSet, String msg) {
        try {
            EmailContract emailContract = new EmailContract();
            Set<String> checkList = new HashSet<>();
            emailSet.forEach(email -> {
                if (EmailCheckUtils.emailFormat(email)) {
                    checkList.add(email);
                }
            });
            if (CollectionUtils.isEmpty(checkList)) {
                return;
            }
            FinhubLogger.info("sendMail4CardApply req = {},msg = {}", emailSet, msg);
            // 收件人
            emailContract.setToList(checkList);
            // 邮件标题
            String subject = "【分贝通】海外卡开卡申请通知";
            emailContract.setSubject(subject);
            StringBuffer textBufer= new StringBuffer();
            textBufer.append("您好！" + "\n");
            textBufer.append(msg+ "\n");
            textBufer.append("\n");
            textBufer.append("顺祝商祺！"+ "\n");
            textBufer.append("\n");
            textBufer.append("企业支出，就用分贝通！"+ "\n");
            textBufer.append("客服电话：400-817-6868"+ "\n");
            textBufer.append("公司官网：www.fenbeitong.com"+"\n");
            String text = textBufer.toString();
            emailContract.setText(text);
            // 发送邮件
            messageService.sendEmail(emailContract);
        } catch (Exception e) {
            FinhubLogger.error("发送开卡申请邮件失败 email = {},msg = {}", emailSet, msg, e);
        }
    }
}
