package com.fenbei.fx.card.common.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.common.enums.FeiShuTypeEnum;
import com.fenbeitong.finhub.common.utils.webhook.TextMsg;
import com.fenbeitong.finhub.common.utils.webhook.WebHookUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

/**
 * 钉钉消息通知
 * <AUTHOR>
 * @date 2023-05-25 18:07:53
 */
@Service
@Slf4j
public class FeiShuUtil {

    public static FeiShuUtil me() {
        return SpringUtil.getBean(FeiShuUtil.class);
    }

    /**
     * 钉钉消息通知
     * <AUTHOR>
     * @date 2023-05-25 18:09:08
     */
    public void sendMsg(FeiShuTypeEnum dingDingType, String msg) {
        // 异步发送钉钉告警
        CompletableFuture.runAsync(() -> sendTextMsg(dingDingType, msg));
    }

    private void sendTextMsg(FeiShuTypeEnum dingDingType, String msg) {
        try {
            String type = dingDingType.getType();
            String token = NacosConfigUtil.me().getToken(type);
            String phone = NacosConfigUtil.me().getPhone(type);
            TextMsg textMsg = new TextMsg(token, msg);
            if (StringUtils.isNotBlank(phone)) {
                textMsg.at(Arrays.asList(phone.split(",")));
            }
            WebHookUtils.sendFeishu(textMsg);
        } catch (Exception e) {
            log.warn("钉钉消息通知DingDingService sendTextMsg error,dingDingType:{},msg:{}:{}", dingDingType, msg, e);
        }
    }
}
