package com.fenbei.fx.card.service.usercard;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.common.enums.ActiveModelEnum;
import com.fenbei.fx.card.common.enums.CardModelEnum;
import com.fenbei.fx.card.common.enums.CardPlatformCaseEnum;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardmodelconfig.CardModelConfigService;
import com.fenbei.fx.card.service.cardmodelconfig.dto.EmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardholderapply.CardholderApplyService;
import com.fenbei.fx.card.service.usercard.dto.FxHomePagePayApplyDetail;
import com.fenbei.fx.card.service.usercard.dto.UserCardHomePageDTO;
import com.fenbei.fx.card.service.usercard.dto.UserCardPettyInfoDTO;
import com.fenbei.fx.card.util.CurrencyNumberFormatUtil;
import com.fenbei.fx.card.util.I18nUtils;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormSimpleInfo;
import com.fenbeitong.saasplus.api.service.apply.IApplyOrderService;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UserCardHomePageManager {

    public static UserCardHomePageManager me() {
        return SpringUtil.getBean(UserCardHomePageManager.class);
    }


    @Autowired
    CardOrderService cardOrderService;
    @Autowired
    CardService cardService;
    @Autowired
    CardCreditManagerService cardCreditManagerService;
    @Autowired
    CardModelConfigService cardModelConfigService;
    @Autowired
    CardholderApplyService cardholderApplyService;
    @DubboReference
    private ICustomFormService iCustomFormService;
    @DubboReference
    private IApplyOrderService iApplyOrderService;


    public UserCardPettyInfoDTO pettyInfoByEmployeeId(String companyId,String employeeId){
        List<CardDTO> cardDTOS = cardService.queryByEmployeeId(employeeId);
        if (CollectionUtils.isEmpty(cardDTOS)){
            UserCardPettyInfoDTO userCardPettyInfoDTO = new UserCardPettyInfoDTO();
            userCardPettyInfoDTO.setHasCard(false);
            userCardPettyInfoDTO.setApplyInfoList(new ArrayList<>());
            userCardPettyInfoDTO.setApplyAppendFlag(false);
            userCardPettyInfoDTO.setApplyFlag(true);
            return userCardPettyInfoDTO;
        }
        return pettyInfo0(companyId,employeeId,null);
    }
    public UserCardPettyInfoDTO pettyInfo(String fxCardId){
        CardDTO cardDTO = cardService.cardDetailByFxCardId(fxCardId);
        if (cardDTO ==  null){
            UserCardPettyInfoDTO userCardPettyInfoDTO = new UserCardPettyInfoDTO();
            userCardPettyInfoDTO.setHasCard(false);
            userCardPettyInfoDTO.setApplyInfoList(new ArrayList<>());
            userCardPettyInfoDTO.setApplyAppendFlag(false);
            userCardPettyInfoDTO.setApplyFlag(false);
            return userCardPettyInfoDTO;
        }
        //查询卡信息
        return pettyInfo0(cardDTO.getCompanyId(),cardDTO.getEmployeeId(),fxCardId);
    }

    public UserCardPettyInfoDTO pettyInfo0(String companyId,String employeeId,String fxCardId){
        UserCardPettyInfoDTO userCardPettyInfoDTO = new UserCardPettyInfoDTO();
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(companyId,employeeId);
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        userCardPettyInfoDTO.setCardModel(cardModel);
        userCardPettyInfoDTO.setCompanyMode(cardModel);

        //模版id
        String formId = getFormId(companyId, cardModel);
        userCardPettyInfoDTO.setFormId(formId);


        //查询未核销的备用金
        List<CardCreditManagerDTO> list = null;
        List<CardCreditManagerDTO> listOfAll = null;
        if (fxCardId != null){
            list =  cardCreditManagerService.queryUncheckApply(fxCardId);
            listOfAll =  cardCreditManagerService.queryUncheckApplyByEmployeeId(employeeId);
        }else {
            list =  cardCreditManagerService.queryUncheckApplyByEmployeeId(employeeId);
        }
        /*
            普通模式（不切换模式），各种情况都不拦截
            备用金模式。1.存在申请中的单子需要拦截。2.有额度，未核销完，额度申请和额度追加都拦截。
         */
        boolean haveApply =  getApplyFlag(companyId,employeeId);
        if(CollectionUtils.isEmpty(list)){
            userCardPettyInfoDTO.setTotalBalance(BigDecimal.ZERO);
            userCardPettyInfoDTO.setTotalBalanceShow(String.valueOf(BigDecimal.ZERO));
            userCardPettyInfoDTO.setTotalApplyAmount(BigDecimal.ZERO);
            userCardPettyInfoDTO.setTotalApplyAmountShow(String.valueOf(BigDecimal.ZERO));
            userCardPettyInfoDTO.setHasCard(true);
            if (Objects.equals(cardModel, ActiveModelEnum.NORMAL.getCode())){
                userCardPettyInfoDTO.setApplyAppendFlag(true);
                userCardPettyInfoDTO.setApplyFlag(true);
            }else {
                boolean haveBalance = (listOfAll!= null && listOfAll.size() > 0 && BigDecimalUtils.hasPrice(listOfAll.get(0).getAvalibleAmount()));
                if (haveApply || haveBalance) {
                    if (haveApply){
                        userCardPettyInfoDTO.setApplyDesc(I18nUtils.transferI18nMessage("存在审批中的申请单，不允许提交新的申请单"));
                    }
                    if (listOfAll!= null && listOfAll.size() > 0 && BigDecimalUtils.hasPrice(listOfAll.get(0).getAvalibleAmount())){
                        userCardPettyInfoDTO.setApplyDesc(I18nUtils.transferI18nMessage("当前为模式为备用金模式，在额度未清零/未核销完成时，无法申请新的额度"));
                    }
                    userCardPettyInfoDTO.setApplyAppendFlag(false);
                    userCardPettyInfoDTO.setApplyFlag(false);
                }else {
                    userCardPettyInfoDTO.setApplyAppendFlag(true);
                    userCardPettyInfoDTO.setApplyFlag(true);
                }
            }
            userCardPettyInfoDTO.setApplyInfoList(new ArrayList<>());
        }else {
            BigDecimal totalApplyAmount = BigDecimal.ZERO;
            BigDecimal totalBalance = BigDecimal.ZERO;
            List<FxHomePagePayApplyDetail> homePagePayApplyDetails = new ArrayList<>();
            CurrencyEnum currencyEnum=CurrencyEnum.USD;
            for (CardCreditManagerDTO c: list) {
                currencyEnum = CurrencyEnum.getCurrencyByCodeIgnoreCase(c.getCurrency());
                totalApplyAmount  = totalApplyAmount.add(c.getAmount());
                totalBalance = totalBalance.add(c.getAvalibleAmount());
                FxHomePagePayApplyDetail fxHomePagePayApplyDetail  = new FxHomePagePayApplyDetail();
                fxHomePagePayApplyDetail.setApplyAmount(c.getAmount());
                fxHomePagePayApplyDetail.setApplyAmountShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(c.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(c.getAmount())));
                fxHomePagePayApplyDetail.setApplyTime(c.getCreateTime());
                fxHomePagePayApplyDetail.setBalance(c.getAvalibleAmount());
                fxHomePagePayApplyDetail.setBalanceShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(c.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(c.getAvalibleAmount())));
                fxHomePagePayApplyDetail.setApplyName(c.getApplyTitle());
                String applyReason = (c.getApplyReason() == null ? "额度申请":c.getApplyReason());
                fxHomePagePayApplyDetail.setApplyReason(applyReason);
                fxHomePagePayApplyDetail.setBizNo(c.getBizNo());
                fxHomePagePayApplyDetail.setApplyTransNo(c.getApplyTransNo());
                if (Objects.equals(cardModel, ActiveModelEnum.NORMAL.getCode())){
                    fxHomePagePayApplyDetail.setApplyAppendFlag(true);
                    fxHomePagePayApplyDetail.setApplyFlag(true);
                }else {
                        fxHomePagePayApplyDetail.setApplyAppendFlag(false);
                        fxHomePagePayApplyDetail.setApplyFlag(false);
                        userCardPettyInfoDTO.setApplyDesc(I18nUtils.transferI18nMessage("当前为模式为备用金模式，在额度未清零/未核销完成时，无法申请新的额度"));
                }

                fxHomePagePayApplyDetail.setApplyStatus(c.getApplyStatus());
                fxHomePagePayApplyDetail.setApplyStatusDesc(I18nUtils.transferI18nMessage("申请成功"));
                fxHomePagePayApplyDetail.setCostType(c.getCostType());
                fxHomePagePayApplyDetail.setCostTypeDesc(c.getCostTypeName());
                homePagePayApplyDetails.add(fxHomePagePayApplyDetail);
            }

            //说明是追加申请单
            if (list.get(0).getApplyTransBatchNo() != null){
                String key = list.get(0).getOriApplyTransNo();
                List<CardCreditManagerDTO> listV = list.stream().filter(i -> i.getOriApplyTransNo().equals(key)).collect(Collectors.toList());
                BigDecimal applyAmount = BigDecimal.ZERO;
                BigDecimal availableAmount = BigDecimal.ZERO;

                for (CardCreditManagerDTO c: listV) {
                    applyAmount = applyAmount.add(c.getAmount());
                    availableAmount = availableAmount.add(c.getAvalibleAmount());
                    currencyEnum = CurrencyEnum.getCurrencyByCodeIgnoreCase(c.getCurrency());
                }
                FxHomePagePayApplyDetail newRecord = homePagePayApplyDetails.get(0);
                newRecord.setApplyAmount(applyAmount);
                newRecord.setApplyAmountShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(list.get(0).getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(applyAmount)));
                newRecord.setBalance(availableAmount);
                newRecord.setBalanceShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(list.get(0).getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(availableAmount)));
                homePagePayApplyDetails.set(0,newRecord);
            }
            userCardPettyInfoDTO.setTotalBalance(totalBalance);
            userCardPettyInfoDTO.setTotalBalanceShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(list.get(0).getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(totalBalance)));
            userCardPettyInfoDTO.setTotalApplyAmount(totalApplyAmount);
            userCardPettyInfoDTO.setTotalApplyAmountShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(list.get(0).getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(totalApplyAmount)));
            userCardPettyInfoDTO.setApplyInfoList(homePagePayApplyDetails);
            if (Objects.equals(cardModel, ActiveModelEnum.NORMAL.getCode())){
                userCardPettyInfoDTO.setApplyAppendFlag(true);
                userCardPettyInfoDTO.setApplyFlag(true);
            }else {
                    userCardPettyInfoDTO.setApplyAppendFlag(false);
                    userCardPettyInfoDTO.setApplyFlag(false);
            }
            userCardPettyInfoDTO.setHasCard(true);
            userCardPettyInfoDTO.setPettyId(list.get(0).getApplyTransNo());
        }
        return userCardPettyInfoDTO;
    }

    /**
     * 获取用户额度申请的模版id
     * @param companyId
     * @param employeeId
     * @return
     */
    public String getUserApplyFormId(String companyId, String employeeId){
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(companyId,employeeId);
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        //模版id
        return getFormId(companyId, cardModel);
    }

    /**
     * 获取额度申请的模版id
     * @param companyId
     * @param cardModel
     * @return
     */
    public String getFormId(String companyId, Integer cardModel) {
        String formId = "";
        FinhubLogger.warn("获取额度申请单的模版id companyId={}", companyId);
        List<CustomFormSimpleInfo> infos = iCustomFormService.queryFormInfoByCompanyId(companyId);
        FinhubLogger.warn("获取额度申请单的模版id结果 companyId={}，infos={}", companyId, JsonUtils.toJson(infos));
        if (CollectionUtils.isEmpty(infos)) {
            return formId;
        }
        for (CustomFormSimpleInfo info : infos) {
            if (info.getFormType().equals(7) && CardModelEnum.isPetty(cardModel)) {
                return info.getFormId();
            }
            if (info.getFormType().equals(6) && CardModelEnum.isNormal(cardModel)) {
                return info.getFormId();
            }
        }
        return formId;
    }

    public Boolean getApplyFlag(String companyId, String employeeId){
        FinhubLogger.info("获取申请中的审批单 companyId={},employeeId={}", companyId,employeeId);
        List<String> list = iApplyOrderService.queryUnderApproveApplyOrders4OverseaCard(companyId,employeeId);
        FinhubLogger.info("获取申请中的审批单 companyId={}，infos={}", companyId, JsonUtils.toJson(list));
        return !(list == null || list.size() == 0);
    }

    public UserCardHomePageDTO homePage(String fxCardId){
        UserCardHomePageDTO userCardHomePageDTO = new UserCardHomePageDTO();
        userCardHomePageDTO.setApplyInfo(pettyInfo(fxCardId));
        userCardHomePageDTO.setTradeInfo(cardOrderService.latestTradeInfo(fxCardId));
        return userCardHomePageDTO;
    }

    public UserCardHomePageDTO homePageByEmployeeId(String companyId,String employeeId){
        UserCardHomePageDTO userCardHomePageDTO = new UserCardHomePageDTO();
        userCardHomePageDTO.setApplyInfo(pettyInfoByEmployeeId(companyId,employeeId));
        userCardHomePageDTO.setTradeInfo(cardOrderService.latestTradeInfoByEmployeeId(employeeId));
        return userCardHomePageDTO;
    }
}
