package com.fenbei.fx.card.service.cardapply.dto;

import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 国际卡操作申请 添加 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCardApplyReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 卡状态
     */
    private Integer cardShowStatus;

    /**
     * 卡申请ID
     */
    private String applyId;

    /**
     * 卡ID
     */
    private String fxCardId;

    private String currency;

    /**
     * 管控规则：频率，币种，金额
     */
    private List<BaseAirwallexRpcDTO.Limit> cardLimits;




    /**
     * 员工id
     */
    private String employeeId;


    /**
     * 员工id
     */
    private String employeeName;

    /**
     * 公司id
     */
    private String companyId;

}
