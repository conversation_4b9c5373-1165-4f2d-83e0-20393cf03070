package com.fenbei.fx.card.service.usercard;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.common.constant.CoreConstant;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.common.vo.KeyValueVO;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.manager.CardManager;
import com.fenbei.fx.card.service.cardapply.domain.CardApplyDO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyDTO;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyDTO;
import com.fenbei.fx.card.service.cardholderapply.manager.CardholderApplyManager;
import com.fenbei.fx.card.service.usercard.dto.CardInfoDTO;
import com.fenbei.fx.card.service.usercard.dto.UserCardInfosDTO;
import com.fenbei.fx.card.service.usercard.dto.UserCardPettyInfoDTO;
import com.fenbei.fx.card.service.usercard.dto.UserCardPrivilegeDTO;
import com.fenbei.fx.card.util.CurrencyNumberFormatUtil;
import com.fenbei.fx.card.util.I18nUtils;
import com.fenbei.fx.card.util.USDNumberFormatUtil;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbei.fx.card.util.MaskUtils;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.usercenter.api.model.dto.auth.VirtualCardAuthInfoDTO;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyVirtualCardIssuerDto;
import com.fenbeitong.usercenter.api.model.dto.company.account.CompanyForeignCurrencyAccountDTO;
import com.fenbeitong.usercenter.api.service.privilege.IRPrivilegeService;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.fenbeitong.finhub.common.constant.CurrencyEnum.getCurrencyByCodeIgnoreCase;

/**
 * 国际卡 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-20
 */
@Slf4j
@Component
public class UserCardManager {





    public static UserCardManager me() {
        return SpringUtil.getBean(UserCardManager.class);
    }

    @DubboReference
    private IRPrivilegeService irPrivilegeService;

    /**
     * 海外卡介绍url
     */
    @Value("${fxcard.intro.url}")
    String fxCardIntroUrl;

    @Value("${fxcard.intro.us.url}")
    String fxCardIntroUsUrl;


    @Autowired
    private CardholderApplyManager cardholderApplyManager;

    /**
     * 用户卡信息
     */
    public UserCardInfosDTO userCardInfos(HttpServletRequest request){
        UserComInfoVO user = UserAuthHolder.getCurrentUser();

        UserCardInfosDTO userCardInfosDTO = new UserCardInfosDTO();
        String companyId = user.getCompany_id();
        String userId = user.getUser_id();
        String str= request.getHeader("lang") ;
        log.info("海外卡介绍url  str = {}" , str);
        //介绍页
        if (StringUtils.isNotBlank(str)&&("zh_CN".equalsIgnoreCase(str)||"zh-CN".equalsIgnoreCase(str))) {
            userCardInfosDTO.setFxCardIntroUrl(fxCardIntroUrl);
        }else {
            userCardInfosDTO.setFxCardIntroUrl(fxCardIntroUsUrl);
        }
        log.info("userCardInfos companyId={},userId={}", companyId, userId);
        //员工离职
        if (StringUtils.isBlank(companyId)){
            log.warn("用户不是在职状态 userCardInfos companyId is null");
            return userCardInfosDTO;
        }

        //查询该公司各种银行卡开通权限
        VirtualCardAuthInfoDTO infoDTO = irPrivilegeService.queryVirtualCardAuthInfo(companyId, userId);
        log.info("userCardInfos virtualCardAuthInfoDTO={}", JsonUtils.toJson(infoDTO));
        if (Objects.isNull(infoDTO)){
            return userCardInfosDTO;
        }

        //企业开通海外卡权限：除了海外卡权限，如果国内卡列表中有连连也可以。后续改造Stereo-账户权限配置-企业外币账户权限配置
        List<CompanyVirtualCardIssuerDto> companyVirtualCardIssuerDtoList = infoDTO.getCompanyVirtualCardIssuerDtoList();
        boolean comHasFxCardAuth=false;
        //从国内的里面拿去连连
        if(ObjectUtils.isNotEmpty(companyVirtualCardIssuerDtoList)){
            for(CompanyVirtualCardIssuerDto cardIssuerDto:companyVirtualCardIssuerDtoList){
                FxAcctChannelEnum channelEnum = FxAcctChannelEnum.matchChannel(cardIssuerDto.getCardIssuerCode());
                if (!Objects.isNull(channelEnum)){
                    comHasFxCardAuth= true;
                    break;
                }
            }
        }

        userCardInfosDTO.setCompanyFxCardAuthStatus((comHasFxCardAuth||infoDTO.getCompanyOverseas())? CoreConstant.CARD_OPER_AUTH_EXIST : CoreConstant.CARD_OPER_AUTH_NOT);
        userCardInfosDTO.setEmployeeFxCardAuthStatus(infoDTO.getEmployeeOverseas()? CoreConstant.CARD_OPER_AUTH_EXIST : CoreConstant.CARD_OPER_AUTH_NOT);


        //如果公司或者个人没权限，直接返回
        if (!userCardInfosDTO.hasCompanyAuth(userCardInfosDTO.getCompanyFxCardAuthStatus())
            || !userCardInfosDTO.hasEmployeeAuth(userCardInfosDTO.getEmployeeFxCardAuthStatus())){
            return userCardInfosDTO;
        }

        //支持开卡的海外卡渠道和币种
        List<CompanyForeignCurrencyAccountDTO> companyAccountList = infoDTO.getForeignCurrencyAccountDTOList();
        //连连算电子账户业务，权限检查境外，开通账户检查境内
        if (CollectionUtils.isEmpty(companyAccountList)&&CollectionUtils.isEmpty(companyVirtualCardIssuerDtoList)){
            return userCardInfosDTO;
        }


        //公司有权限的渠道
        userCardInfosDTO.setCompanyFxCardInfoList(buildCompanyFxCardInfoList(companyAccountList,companyVirtualCardIssuerDtoList));

        //拼装为用户的卡
        userCardInfosDTO.setUserFxCardInfoList(buildUserFxCardInfList(userId, companyId));

        //用户可申请的国际卡（目前airwallex通道一个用户可以申请多张卡，所以有权限就可以申请）
        userCardInfosDTO.setUserCanApplyFxCardInfoList(userCardInfosDTO.getCompanyFxCardInfoList());



        // 查询持卡人申请状态
        Integer checkCardholderApplyStatus = checkCardholderApplyStatus(companyId, userId);
        userCardInfosDTO.setShowCardholderApplyStatus(checkCardholderApplyStatus);

        return userCardInfosDTO;

    }

    /**
     * 检查持卡人申请状态
     */
    private Integer checkCardholderApplyStatus(String companyId, String employeeId) {
        Integer showCardholderApplyStatus = 0;
        try {
            // 查询员工的持卡人申请记录
            List<CardholderApplyDTO> applyList = cardholderApplyManager.queryByEmployeeId(companyId, employeeId);
            if (org.springframework.util.CollectionUtils.isEmpty(applyList)) {
                return showCardholderApplyStatus;
            }
            CardholderApplyDTO latestSuccess = applyList.stream()
                .filter(apply -> CardholderApplyStatusEnum.isBankPass(apply.getApplyStatus()))
                .findFirst()
                .orElse(null);
            if(latestSuccess!= null){
                return CardholderApplyStatusEnum.BANK_PASS.getKey();
            }
            // 查找最新的申请记录，只显示申请中和银行处理中的状态
            CardholderApplyDTO latestApply = applyList.stream()
                .filter(apply -> CardholderApplyStatusEnum.isApplying(apply.getApplyStatus()) ||
                    CardholderApplyStatusEnum.isBankDealing(apply.getApplyStatus()))
                .findFirst()
                .orElse(null);

            if (latestApply != null) {
                if (CardholderApplyStatusEnum.isApplying(latestApply.getApplyStatus())) {
                    return CardholderApplyStatusEnum.APPLYING.getKey();
                    //userCardPettyInfoDTO.setCardholderApplyStatusMsg("实体卡开卡信息已提交，管理员审核中");
                } else if (CardholderApplyStatusEnum.isBankDealing(latestApply.getApplyStatus())) {
                    return CardholderApplyStatusEnum.BANK_DEALING.getKey();
                    //userCardPettyInfoDTO.setCardholderApplyStatusMsg("实体卡开卡信息已提交，银行审核中");
                }
            }
        } catch (Exception e) {
            log.error("查询持卡人申请状态失败，companyId={}, employeeId={}", companyId, employeeId, e);
        }
        return showCardholderApplyStatus;
    }

    private List<CardInfoDTO> buildCompanyFxCardInfoList(List<CompanyForeignCurrencyAccountDTO> companyAccountList,List<CompanyVirtualCardIssuerDto> companyVirtualCardIssuerDtoList) {
        List<CardInfoDTO> cardInfoDTOS = new ArrayList<>();

        if(ObjectUtils.isNotEmpty(companyAccountList)){
            companyAccountList.forEach(p -> {
                CardInfoDTO cardInfoDTO = new CardInfoDTO();
                FxAcctChannelEnum channelEnum = FxAcctChannelEnum.matchChannel(p.getCardIssuer());
                if (!Objects.isNull(channelEnum)){
                    cardInfoDTO.setCardPlatform(p.getCardIssuer());
                    cardInfoDTO.setCardPlatformName(channelEnum.getChannelName());
                    cardInfoDTO.setCardPlatformIcon(channelEnum.getBankIconWebSmall());
                    cardInfoDTOS.add(cardInfoDTO);
                }
            });
        }
        //从国内的里面拿去连连
        if(ObjectUtils.isNotEmpty(companyVirtualCardIssuerDtoList)){
            companyVirtualCardIssuerDtoList.forEach(p -> {
                CardInfoDTO cardInfoDTO = new CardInfoDTO();
                FxAcctChannelEnum channelEnum = FxAcctChannelEnum.matchChannel(p.getCardIssuerCode());
                if (!Objects.isNull(channelEnum)){
                    cardInfoDTO.setCardPlatform(channelEnum.getChannel());
                    cardInfoDTO.setCardPlatformName(channelEnum.getChannelName());
                    cardInfoDTO.setCardPlatformIcon(channelEnum.getBankIconWebSmall());
                    cardInfoDTOS.add(cardInfoDTO);
                }
            });
        }

        return cardInfoDTOS;
    }



    /**
     * 用户当前海外卡信息
     * @param userId
     * @param companyId
     * @return
     */
    private List<CardInfoDTO> buildUserFxCardInfList(String userId, String companyId){
        //获取在申请状态的（包括待审核和审核中）
        List<CardApplyDTO> cardApplyDTOS = CardApplyManager.me().userApplysExcludeSuc(userId, companyId);
        //获取用户全部卡
        List<CardDTO> cardDTOS = CardManager.me().userAllCards(userId, companyId);

        List<CardInfoDTO> cardInfoDTOS = new ArrayList<>();
        convertCardInfo(cardDTOS, cardInfoDTOS);

        convertApplyInfo(cardApplyDTOS, cardInfoDTOS);

        return cardInfoDTOS;
    }

    private void convertCardInfo(List<CardDTO> cardDTOS, List<CardInfoDTO> cardInfoDTOS) {
        if (CollectionUtils.isNotEmpty(cardDTOS)){
            cardDTOS.forEach(p->{
                CardInfoDTO cardInfoDTO = new CardInfoDTO();
                BeanUtils.copyProperties(p, cardInfoDTO);
                //状态处理
                Integer showStatus = CardStatusEnum.getShowStatus(p.getCardStatus());
                cardInfoDTO.setCardShowStatus(showStatus);
                CardShowStatusEnum cardShowStatusEnum = CardShowStatusEnum.get(showStatus);
                cardInfoDTO.setCardShowStatusVo(new KeyValueVO(cardShowStatusEnum.getStatus(),cardShowStatusEnum.getName()));
                CardActiveStatusEnum activeStatusEnum = CardActiveStatusEnum.get(p.getActiveStatus());
                cardInfoDTO.setActiveStatusVo(new KeyValueVO(activeStatusEnum.getStatus(),activeStatusEnum.getName()));
                cardInfoDTO.setMaskBankCardNo(MaskUtils.leftData(p.getBankCardNo(),4));
                cardInfoDTO.setShowUSDBalance(CurrencyNumberFormatUtil.moneyFormart(getCurrencyByCodeIgnoreCase(p.getCurrency()),BigDecimalUtils.fen2yuan(cardInfoDTO.getBalance())));
                //渠道信息
                CardPlatformEnum platform = CardPlatformEnum.getPlatform(p.getCardPlatform());
                if (!Objects.isNull(platform)){
                    cardInfoDTO.setCardPlatformName(platform.name());
                    cardInfoDTO.setCardPlatformIcon(platform.getPlatformIcon());
                }

                CardBrandEnum brand = CardBrandEnum.getBrand(p.getCardBrand());
                if (Objects.isNull(brand)){
                    cardInfoDTO.setCardBrandIcon(CardBrandEnum.NULL_BRAND.getBrandIcon());
                }else{
                    cardInfoDTO.setCardBrandIcon(brand.getBrandIcon());
                }
                //币种
                CurrencyEnum currencyEnum = getCurrencyByCodeIgnoreCase(p.getCurrency());
                if (!Objects.isNull(currencyEnum)){
                    cardInfoDTO.setCurrencySymbol(currencyEnum.getSymbol());
                    cardInfoDTO.setCurrencyName(currencyEnum.getDisplayName());
                }

                //实体卡可开卡赋值
                canActiveFlagCalculate(p, cardInfoDTO);
                if (CardPlatformEnum.isLianLian(p.getCardPlatform())) {
                   boolean isWaitActive =  p.getActiveStatus() != 4 && Objects.equals(CardStatusEnum.WAIT_ACTIVE.getStatus(), p.getCardStatus());
                   boolean isActiveded = p.getActiveStatus() == 4 && Objects.equals(CardStatusEnum.ACTIVE.getStatus(), p.getCardStatus());
                   if (isWaitActive||isActiveded) {
                       CardApplyDTO cardApplyDTO = CardApplyManager.me().queryCardApplyByFxCardId(p.getFxCardId());
                       cardInfoDTO.setPhone(cardApplyDTO.getApplyerPhone());
                       cardInfoDTO.setPhoneAreaCode(cardApplyDTO.getNationCode());
                   }
                }
                cardInfoDTOS.add(cardInfoDTO);
            });
        }
    }

    private void canActiveFlagCalculate(CardDTO p, CardInfoDTO cardInfoDTO) {
        Integer canActiveFlag = 0;
        if (!CardFormFactorEnum.isPhysical(p.getCardFormFactor())){
            return;
        }
        if(Objects.isNull(p.getCardPublicTime())){
            return;
        }
        //发卡时间已经过了5天，可以激活
//        Date date1 = DateUtils.addDay(p.getCardPublicTime(), 5);
//        int rs = DateUtils.compareByDateTime(date1, DateUtils.now());
//        if (rs <= 0 && !Objects.equals(p.getActiveStatus(), CardActiveStatusEnum.SUCCESS.getStatus())){
        if (!Objects.equals(p.getActiveStatus(), CardActiveStatusEnum.SUCCESS.getStatus())){
            canActiveFlag = 1;
        }
        cardInfoDTO.setCanActiveFlag(canActiveFlag);
    }

    private void convertApplyInfo(List<CardApplyDTO> cardApplyDTOS, List<CardInfoDTO> cardInfoDTOS) {
        if (CollectionUtils.isNotEmpty(cardApplyDTOS)){
            cardApplyDTOS.forEach(p->{
                CardInfoDTO cardInfoDTO = new CardInfoDTO();
                BeanUtils.copyProperties(p, cardInfoDTO);
                //状态处理
                Integer showStatus = CardApplyStatusEnum.getShowStatus(p.getApplyStatus());
                cardInfoDTO.setCardShowStatus(showStatus);
                CardShowStatusEnum cardShowStatusEnum = CardShowStatusEnum.get(showStatus);
                cardInfoDTO.setCardShowStatusVo(new KeyValueVO(cardShowStatusEnum.getStatus(),cardShowStatusEnum.getName()));
                //渠道信息
                cardInfoDTO.setCardPlatform(p.getCardPlatform());
                //渠道信息
                CardPlatformEnum platform = CardPlatformEnum.getPlatform(p.getCardPlatform());
                if (!Objects.isNull(platform)){
                    cardInfoDTO.setCardPlatformName(platform.getName());
                    cardInfoDTO.setCardPlatformIcon(platform.getPlatformIcon());
                }

                CardBrandEnum brand = CardBrandEnum.getBrand(p.getCardBrand());
                if (Objects.isNull(brand)){
                    cardInfoDTO.setCardBrandIcon(CardBrandEnum.NULL_BRAND.getBrandIcon());
                }else{
                    cardInfoDTO.setCardBrandIcon(brand.getBrandIcon());
                }
                //币种
                if (StringUtils.isNotBlank(p.getCurrency())){
                    CurrencyEnum currencyEnum = getCurrencyByCodeIgnoreCase(p.getCurrency());
                    if (!Objects.isNull(currencyEnum)){
                        cardInfoDTO.setCurrencySymbol(currencyEnum.getSymbol());
                        cardInfoDTO.setCurrencyName(currencyEnum.getDisplayName());
                    }
                }
                cardInfoDTO.setPhone(p.getApplyerPhone());
                cardInfoDTO.setPhoneAreaCode(p.getNationCode());
                cardInfoDTOS.add(cardInfoDTO);
            });
        }
    }

    /**
     * 用户卡权限（海外和境内）
     */
    public UserCardPrivilegeDTO userCardPrivilege(){
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String companyId = user.getCompany_id();
        String userId = user.getUser_id();
        UserCardPrivilegeDTO privilegeDTO = new UserCardPrivilegeDTO();
        log.info("userCardPrivilege companyId={},userId={}", companyId, userId);
        if (StringUtils.isBlank(companyId)){
            log.warn("用户不是在职状态 userCardPrivilege companyId is null");
            return privilegeDTO;
        }

        VirtualCardAuthInfoDTO infoDTO = irPrivilegeService.queryVirtualCardAuthInfo(companyId, userId);
        log.info("serCardPrivilege virtualCardAuthInfoDTO={}", JsonUtils.toJson(infoDTO));
        if (Objects.isNull(infoDTO)){
            return privilegeDTO;
        }

        privilegeDTO.setCompanyBankCardAuthStatus(infoDTO.getCompanyChurchyard()? CoreConstant.CARD_OPER_AUTH_EXIST : CoreConstant.CARD_OPER_AUTH_NOT);
        privilegeDTO.setEmployeeBankCardAuthStatus(infoDTO.getEmployeeChurchyard()? CoreConstant.CARD_OPER_AUTH_EXIST : CoreConstant.CARD_OPER_AUTH_NOT);
        privilegeDTO.setCompanyFxCardAuthStatus(infoDTO.getCompanyOverseas()? CoreConstant.CARD_OPER_AUTH_EXIST : CoreConstant.CARD_OPER_AUTH_NOT);
        privilegeDTO.setEmployeeFxCardAuthStatus(infoDTO.getEmployeeOverseas()? CoreConstant.CARD_OPER_AUTH_EXIST : CoreConstant.CARD_OPER_AUTH_NOT);

        log.info("serCardPrivilege privilegeDTO={}", JsonUtils.toJson(privilegeDTO));

        return privilegeDTO;
    }


}
