package com.fenbei.fx.card.service.cardapply;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardapply.dto.*;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import java.util.List;

/**
 * 国际卡操作申请 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
public interface CardApplyService extends BaseService<CardApplyDTO> {

    static CardApplyService me() {
        return SpringUtil.getBean(CardApplyService.class);
    }


    /**
     * First查询
     *
     * @param cardApplyListReqDTO 入参DTO
     * @return
     */
    CardApplyListResDTO listOne(CardApplyListReqDTO cardApplyListReqDTO);

    /**
     * 分页
     *
     * @param cardApplyPageReqDTO 入参DTO
     * @param current             当前页
     * @param size                每页大小
     * @return
     */
    Page<CardApplyPageResDTO> pagination(CardApplyPageReqDTO cardApplyPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardApplyAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardApplyAddReqDTO cardApplyAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardApplyAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardApplyAddReqDTO cardApplyAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardApplyAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardApplyAddReqDTO> cardApplyAddReqDTOList);

    /**
     * 详情
     */
    CardApplyShowResDTO cardApplyDetail(String applyId);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardApplyShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardApplyModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardApplyModifyReqDTO cardApplyModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardApplyModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardApplyModifyReqDTO cardApplyModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardApplyRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardApplyRemoveReqDTO cardApplyRemoveReqDTO);


    CardApplyShowResDTO createCardApply(CreateCardApplyReqDTO createCardApplyReqDTO);

    /**
     * 审批
     */
    Boolean approvedCardApply(UpdateCardApplyReqDTO updateCardApplyReqDTO);

    Boolean approvedCardApplyByStereo(String getApplyId);
}
