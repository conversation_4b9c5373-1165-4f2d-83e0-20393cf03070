package com.fenbei.fx.card.common.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * 配置中心
 * <AUTHOR>
 * @date 2023-03-29 17:12:42
 */
@Service
@Slf4j
public class NacosConfigUtil {

    public static NacosConfigUtil me() {
        return SpringUtil.getBean(NacosConfigUtil.class);
    }

    /**
     * 钉钉token
     */
    @NacosValue(value = "${fxCard.dingDing.type}", autoRefreshed = true)
    public String dingDingTokenMapStr;

    /**
     * 钉钉phone通知
     */
    @NacosValue(value = "${fxCard.dingDing.phone}", autoRefreshed = true)
    public String dingDingPhoneMapStr;

    /**
     * 钉钉token
     */
    public String getToken(String type){
        try {
            return getString(type, dingDingTokenMapStr);
        } catch (Exception e) {
            log.error("NacosConfigUtil getToken error,type:{}", type, e);
            return null;
        }
    }

    /**
     * 钉钉Phone
     */
    public String getPhone(String type){
        try {
            return getString(type, dingDingPhoneMapStr);
        } catch (Exception e) {
            log.error("NacosConfigUtil getPhone error,type:{}", type, e);
            return null;
        }
    }

    private String getString(String type, String dingDingTokenMapStr) {
        if (StringUtils.isBlank(dingDingTokenMapStr)) {
            return null;
        }
        Map<String, String> dingDingTokenMap = JSONObject.parseObject(dingDingTokenMapStr, Map.class);
        String token = dingDingTokenMap.get(type);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        return dingDingTokenMap.get("default");
    }
}
